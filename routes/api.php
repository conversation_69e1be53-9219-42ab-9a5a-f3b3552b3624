<?php

use App\Http\Controllers\InspectController;
use App\Jobs\ProcessLivechatWebhook;
use App\Models\Campaign;
use App\Models\Livechat as ModelsLivechat;
use App\Models\Setting;
use App\Webhook\Livechat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/webhook/livechat',function(Request $request){
    $json = json_encode($request->all());
    cache()->put('livechat',$json);
    $livechat = new Livechat();
    $livechat->record($request);
});

Route::get('/webhook/livechat/get',function(Request $request){
    $json = json_encode($request->all());
    return cache()->get('livechat','');
});

Route::get('/webhook/livechat/get_chat/{thread_id}',function(Request $request,$thread_id){
    $livechat = new Livechat();
    $response = $livechat->list_archives($thread_id);
    dd($response->object());
});

Route::get('/metabase',function(){
    return response()->json([
        'count' => \App\Models\Livechat::
        where('platform','wallex')
        ->where('created_at','>=',now()->subMinutes(request()->input('minutes',5)))->count(),
    ]);
})->name('metabase');

Route::get('/simultaneous',function(){
    return response()->json([
        'count'=>Setting::where('name','chat_limit')->first()->value,
    ]);
});

// wallex
Route::get('/online-chats',function(){
    $list = [];
    foreach(Cache::get('online_chat_list',[]) as $key=>$value)
    {
        $list[$key] = $value->diffInMinutes(now());
    }
    return response()->json($list);
})->name('online-chats');

Route::get('debug/{platform}',function($platform){

    return \App\Models\Livechat::where('platform',$platform)
    ->orderByDesc('id')
    ->whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
    ->whereNull('author_id')
    ->whereDoesntHave('inactive_archived')
    ->whereDoesntHave('queue_abandonment')
    ->whereDoesntHave('missed_chat')
    // ->take(100)
    ->get();
});

Route::get('livechats/json',[\App\Http\Controllers\ExportLivechatController::class,'index']);

Route::get('livechat/retry/{start_id}/{end_id}',function(int $start_id,int $end_id){
    $livechats = ModelsLivechat::
        where('id','>=',$start_id)
        ->where('id','<',$end_id)
        ->whereNull('data')
        ->get();

    foreach($livechats as $livechat)
    {
        ProcessLivechatWebhook::dispatch($livechat->id);
    }
});

// Route::get('test/metabase',function(){
//     $endpoint = "http://*************:9100/insert_event";
//     Http::post($endpoint,[
        
//         'name'=>'test meta 2',
//         'description'=>'description meta',
//         'question_id'=>2342,
//         'timestamp'=>'2025-03-28',
//     ]);
// });

Route::any('optimize',function(){
    $tables = DB::select('SHOW TABLES');
    $dbName = env('DB_DATABASE');
    $key = "Tables_in_{$dbName}";
    foreach ($tables as $table)
    {
        $tableName = $table->$key;
        DB::statement("OPTIMIZE TABLE {$tableName}");
        DB::statement("ANALYZE TABLE {$tableName}");
        dump($tableName);
    }
});

Route::get('fix/campaign',function(){
    $todayAtEight = Carbon::today()->setHour(8);
    Campaign::whereNull('updated_at')->update(['created_at' => $todayAtEight]);

    $campaigns = Campaign::whereNull('created_at')
    ->whereNotNull('updated_at')
    ->get();

    foreach ($campaigns as $campaign)
    {
        $campaign->created_at = Carbon::parse($campaign->updated_at)->setTime(8, 0);
        $campaign->save();
    }
});

Route::any('inspect',[InspectController::class,'index']);

