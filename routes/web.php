<?php

use App\Http\Controllers\ProfileController;
use App\Models\Parameter;
use App\Models\Parameter_record;
use App\Models\Period;
use App\Models\Record;
use Illuminate\Support\Facades\Route;
/*
|-------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
ini_set('memory_limit', '4096M');

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';

require __DIR__.'/qc.php';

Route::get('/login/{id}',function($id){
    auth()->loginUsingId($id);
});

Route::get('/php/info', function () {
    phpinfo();
});

Route::get('/weighted-subjects/{period}', function (Period $period) {
    $one = Record::where('coefficient',1)
    ->where('incoming_date','>=',$period->from)
    ->where('incoming_date','<',$period->to)
    ->count();

    $over = Record::where('coefficient','<>',1)
    ->where('incoming_date','>=',$period->from)
    ->where('incoming_date','<',$period->to)
    ->count();

    dd("one : {$one}","over : {$over}",$over/$one);
})->name('weighted-subjects-ratio');

Route::get('delete/null',function(){

    $red_id_list = Parameter::where('incoming_type','red_call')->orWhere('incoming_type','red_chat')->get()->pluck('id');
    Parameter_record::whereNull('value')->whereIn('parameter_id',$red_id_list)->delete();

})->name('delete-null');


