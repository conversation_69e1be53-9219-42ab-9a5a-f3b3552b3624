<?php

use App\Exports\AIExport;
use App\Exports\CampaignExport;
use App\Exports\ChatsExport;
use App\Exports\LivechatsExport;
use App\Http\Middleware\EnsureUserIsNotSupportAgent;
use App\Livewire\CreateObject;
use Illuminate\Support\Facades\Route;
use App\Livewire\CreateRecord;
use App\Livewire\ShowObjects;
use App\Livewire\UpdateRecord;
use App\Livewire\ShowRecords;
use App\Livewire\ShowUsers;
use App\Livewire\ShowParameters;
use App\Livewire\ShowPeriods;
use App\Livewire\ShowPool;
use App\Livewire\ShowReport;
use App\Livewire\ShowScores;
use App\Livewire\ShowSettings;
use App\Livewire\UploadCallExcel;
use App\Livewire\UploadChatExcel;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\PeriodsExport;
use App\Exports\ReactsExport;
use App\Exports\RecordsExport;
use App\Livewire\Ai\Voice2text;
use App\Livewire\Announcements\Archive;
use App\Livewire\Announcements\Create;
use App\Livewire\Announcements\Edit;
use App\Livewire\AutoCalc;
use App\Livewire\ChallengingRecords;
use App\Livewire\Crawler\Slack;
use App\Livewire\ExtractData;
use App\Livewire\Google\DepositWithoutMemo;
use App\Livewire\Leaderboard;
use App\Livewire\Livechat\ShowChatTransfer;
use App\Livewire\Livechat\ShowInactiveArchive;
use App\Livewire\Livechat\ShowInactiveTransfer;
use App\Livewire\Livechat\ShowLostConnections;
use App\Livewire\Livechat\ShowMissedChats;
use App\Livewire\Livechat\ShowQueueAbandonment;
use App\Livewire\Livechat\ShowSignedOutTransfer;
use App\Livewire\Livechat\ShowTakeOver;
use App\Livewire\OKR;
use App\Livewire\Okr\ShowOkrResult;
use App\Livewire\Okr\ShowQcData;
use App\Livewire\Okr\ShowQcItems;
use App\Livewire\Okr\ShowSupervisorData;
use App\Livewire\Okr\ShowSupervisorItems;
use App\Livewire\QCQC\Leaderboard as QCQCLeaderboard;
use App\Livewire\QCQC\ShowReport as QCQCShowReport;
use App\Livewire\QCQC\ShowScores as QCQCShowScores;
use App\Livewire\QCSupervisors\Leaderboard as QCSupervisorsLeaderboard;
use App\Livewire\QCSupervisors\ShowReport as QCSupervisorsShowReport;
use App\Livewire\QCSupervisors\ShowScores as QCSupervisorsShowScores;
use App\Livewire\ShowData;
use App\Livewire\ShowDetailedReport;
use App\Livewire\ShowDetailedStats;
use App\Livewire\ShowExceptions;
use App\Livewire\ShowHourlyMonitor;
// use App\Livewire\ShowExceptions;
use App\Livewire\ShowItems;
use App\Livewire\ShowLivechats;
use App\Livewire\ShowMonitor;
use App\Livewire\ShowParameterReport;
use App\Livewire\ShowQcReport;
use App\Livewire\ShowSensitiveWords;
use App\Livewire\ShowSnapshot;
use App\Livewire\ShowSnapshotReport;
use App\Livewire\ShowSnapshots;
use App\Livewire\ShowStats;
use App\Livewire\ShowSupervisorReport;
use App\Livewire\ShowTickets;
use App\Livewire\ShowWeightedSubjects;
use App\Livewire\SupervisorScores;
use App\Livewire\Upload\CampaignList;
use App\Livewire\Upload\Comprehensive;
use App\Livewire\Upload\HardCases;
use App\Livewire\Upload\IncomingCall;
use App\Livewire\Upload\MissedCall;
// use App\Livewire\Upload\Nanowatch;
use App\Livewire\Upload\OutgoingCall;
use App\Livewire\Upload\SecondNanowatch;
use App\Livewire\Upload\Top10;
use App\Livewire\UploadKycExcel;
use App\Livewire\UploadSlsExcel;
use App\Livewire\WeightedSubjectsReport;
use Carbon\Carbon;
use App\Livewire\Campaigns\Second\ShowList as ShowListSecond;
use App\Livewire\Metabase\TxHistory;
use App\Livewire\ShowDetailedStatsMultiPeriod;
use App\Livewire\ShowSubjectReport;
use App\Livewire\SupervisorRecordsLeaderboard;

Route::middleware('auth')->group(function () {

    Route::get('upload/chat/excel', UploadChatExcel::class)->name('upload-chat-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/call/excel', UploadCallExcel::class)->name('upload-call-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/kyc/excel', UploadKycExcel::class)->name('upload-kyc-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/sls/excel', UploadSlsExcel::class)->name('upload-sls-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/top10/excel', Top10::class)->name('upload-top10-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/incoming-call/excel', IncomingCall::class)->name('upload-incoming-call-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/missed-call/excel', MissedCall::class)->name('upload-missed-call-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/nanowatch/excel', SecondNanowatch::class)->name('upload-nanowatch-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/hard-cases/excel', HardCases::class)->name('upload-hard-cases-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('upload/comprehensive/excel', Comprehensive::class)->name('upload-comprehensive-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/upload/outgoing/excel',OutgoingCall::class)->name('upload-outgoing-excel')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/upload/campaign/excel',CampaignList::class)->name('upload-campaign-excel')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('show/pool', ShowPool::class)->name('pool')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('/show/users', ShowUsers::class)->name('show-users')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/show/parameters', ShowParameters::class)->name('show-parameters')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/show/periods', ShowPeriods::class)->name('show-periods')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/show/settings', ShowSettings::class)->name('show-settings')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('/create/record', CreateRecord::class)->name('create-record')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/update/record', UpdateRecord::class)->name('update-record')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('/show/stats', ShowStats::class)->name('show-stats')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/show/detailed/stats/{period_id}', ShowDetailedStats::class)->name('show-detailed-stats')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('/show/detailed/stats/{from_period_id}/{to_period_id}', ShowDetailedStatsMultiPeriod::class)->name('show-detailed-stats-multi-period')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('/show/monitor',ShowMonitor::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-monitor');
    Route::get('/show/hourly/monitor',ShowHourlyMonitor::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-hourly-monitor');

    Route::get('/okr', OKR::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('okr');

    Route::get('/show/data', ShowData::class)->name('show-data');

    Route::get('/show/supervisor/data', ShowSupervisorData::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-supervisor-data');
    Route::get('/show/qc/data', ShowQcData::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-qc-data');

    Route::get('show/items',ShowItems::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-items');
    Route::get('show/supervisor/items',ShowSupervisorItems::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-supervisor-items');
    Route::get('show/qc/items',ShowQcItems::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-qc-items');
    
    Route::get('/show/okr/result', ShowOkrResult::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-okr-result');
    Route::get('/show/okr/periods', \App\Livewire\Okr\ShowPeriods::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-okr-periods');

    Route::get('/show/tickets', ShowTickets::class)->name('show-tickets');
    Route::get('/auto/calc', AutoCalc::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('auto-calc');
    Route::get('/extract/data', ExtractData::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('extract-data');

    Route::get('/show/records', ShowRecords::class)->name('show-records');
    Route::get('/challenging/records', ChallengingRecords::class)->name('challenging-records');

    Route::get('/create/object', CreateObject::class)->name('create-object');
    Route::get('/show/objects', ShowObjects::class)->name('show-objects');
    
    Route::get('/show/report', ShowReport::class)->name('show-report');
    Route::get('/show/supervisor/report', ShowSupervisorReport::class)->name('show-supervisor-report');

    Route::get('/show/parameter/report', ShowParameterReport::class)->name('show-parameter-report');
    Route::get('/show/subject/report', ShowSubjectReport::class)->name('show-subject-report');

    Route::get('/show/qc/report', ShowQcReport::class)->name('show-qc-report')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('/show/detailed/report/{user_id}/{period_id}',ShowDetailedReport::class)->name('show-detailed-report');
    Route::get('/show/scores', ShowScores::class)->name('show-scores');

    Route::get('qc-supervisors/show/scores', QCSupervisorsShowScores::class)->name('qc-supervisors-show-scores')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('qc-supervisors/show/report', QCSupervisorsShowReport::class)->name('qc-supervisors-show-report')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('qc-supervisors/show/leaderboard', QCSupervisorsLeaderboard::class)->name('qc-supervisors-show-leaderboard')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('qc-qc/show/scores', QCQCShowScores::class)->name('qc-qc-show-scores')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('qc-qc/show/report', QCQCShowReport::class)->name('qc-qc-show-report')->middleware(EnsureUserIsNotSupportAgent::class);
    Route::get('qc-qc/show/leaderboard', QCQCLeaderboard::class)->name('qc-qc-show-leaderboard')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('/supervisor-scores',SupervisorScores::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('supervisor-scores');

    Route::get('/supervisor-records-leaderboard',SupervisorRecordsLeaderboard::class)->name('supervisor-records-leaderboard');

    Route::get('/show/weighted-subjects',ShowWeightedSubjects::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('weighted-subjects');
    Route::get('/weighted-subjects-report',WeightedSubjectsReport::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('weighted-subjects-report');

    Route::get('/show/snapshots',ShowSnapshots::class)->name('show-snapshots');
    Route::get('/show/snapshot/{id}', ShowSnapshot::class)->name('show-snapshot');
    Route::get('/show/snapshot/report/{id}', ShowSnapshotReport::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-snapshot-report');
    
    Route::get('/show/exceptions',ShowExceptions::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('exceptions');
    
    Route::get('/leaderboard',Leaderboard::class)->name('leaderboard');

    Route::get('show/sensitive-words',ShowSensitiveWords::class)->name('show-sensitive-words');
    
    Route::get('show/livechats',ShowLivechats::class)->name('show-livechats');
    Route::get('show/chat-transfers',ShowChatTransfer::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-chat-transfers');
    Route::get('show/inactive-transfers',ShowInactiveTransfer::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-inactive-transfers');
    Route::get('show/lost-connections',ShowLostConnections::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-lost-connections');
    Route::get('show/missed-chats',ShowMissedChats::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-missed-chats');
    Route::get('show/queue-abandonments',ShowQueueAbandonment::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-queue-abandonment');
    Route::get('show/take-overs',ShowTakeOver::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-take-overs');
    Route::get('show/signed-out-transfers',ShowSignedOutTransfer::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-signed-out-transfers');
    Route::get('show/inactive-archive',ShowInactiveArchive::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-inactive-archive');

    Route::get('announcements/create',Create::class)->name('announcements-create');
    Route::get('announcements/edit/{announcement}',Edit::class)->name('announcements-edit');
    Route::get('announcements/archive',Archive::class)->name('announcements-archive');

    Route::get('/show/deposit-without-memo', DepositWithoutMemo::class)->name('deposit-without-memo');

    Route::get('/metabase/tx-history', TxHistory::class)->name('tx-history');

    Route::get('/exam/show-report', \App\Livewire\Exams\ShowReport::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('exams-show-report');

    Route::get('/campaigns/show-list',\App\Livewire\Campaigns\ShowList::class)->name('campaigns-show-list');
    
    Route::get('/health/market',\App\Livewire\Health\Market::class)->name('market-health');

    Route::get('/ai/voice-to-text',Voice2text::class)->name('voice-to-text')->middleware(EnsureUserIsNotSupportAgent::class);

    Route::get('/crawler/slack',Slack::class)->name('crawler-slack')->middleware(EnsureUserIsNotSupportAgent::class);

    // Route::get('/campaigns/first/list',\App\Livewire\Campaigns\First\ShowList::class)->name('campaigns-first-list');
    // Route::get('/campaigns/first/help',\App\Livewire\Campaigns\First\Help::class)->name('campaigns-first-help');

    Route::get('/campaigns/second/list',\App\Livewire\Campaigns\Second\ShowList::class)->name('campaigns-second-list');
    Route::get('/campaigns/second/help',\App\Livewire\Campaigns\Second\Help::class)->name('campaigns-second-help');

    Route::get('/show/ai-outputs', \App\Livewire\ShowAiOutputs::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-ai-outputs');
    Route::get('/show/failed-jobs', \App\Livewire\ShowFailedJobs::class)->middleware(EnsureUserIsNotSupportAgent::class)->name('show-failed-jobs');


    Route::get('/goftino/show-chats',\App\Livewire\Goftino\ShowChats::class)->name('goftino-show-chats');
    Route::get('/goftino/show-transferred-chats',\App\Livewire\Goftino\ShowTransferredChats::class)->name('goftino-show-transferred-chats');
    Route::get('/goftino/show-unassigned-chats',\App\Livewire\Goftino\ShowUnassignedChats::class)->name('goftino-show-unassigned-chats');
    
    Route::get('/goftino/show-assignments',\App\Livewire\Goftino\ShowAssignments::class)->name('goftino-show-assignments');

    // نمرات ماهیانه کارشناسان
    Route::get('/periods/{period_id}', function (int $period_id) {
        return Excel::download(new PeriodsExport($period_id), 'period.xlsx');
    })->name('download');

    // qc records with feedback
    Route::get('/records/{period_id}', function (int $period_id) {
        return Excel::download(new RecordsExport($period_id), 'records.xlsx');
    })->name('download-records');

    // announcement reacts
    Route::get('/reacts/{platform}/{period_id}', function (string $platform,int $period_id) {
        return Excel::download(new ReactsExport($platform,$period_id), 'reacts.xlsx');
    })->name('download-reacts');

    Route::get('/livechats/download',function(){
        return Excel::download(new ChatsExport(), 'livechats.xlsx');
    })->name('download-livechats-second');

    Route::get('/livechats/{date}',function(string $date){
        $carbon_date = Carbon::createFromFormat('Y-m-d', $date)->startOfDay();
        return Excel::download(new LivechatsExport($carbon_date), 'livechats.xlsx');
    })->name('download-livechats');

    Route::get('/campaigns/download', function () {
        return Excel::download(new CampaignExport(), 'campaign.xlsx');
    })->name('download-campaign');

    Route::get('/ai/{uuid}',function(string $uuid){
        return Excel::download(new AIExport($uuid), 'ai.xlsx');
    })->name('download-ai');

});