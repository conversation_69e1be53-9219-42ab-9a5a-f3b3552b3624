<div>
  <form class="max-w-sm mx-auto">

    <div class="mb-5">
      
      
        <select wire:model.lazy="support_agent_id" id="support_agent_id" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option hidden selected value="">انتخاب کارشناس...</option>
            @foreach ($users as $user)
                <option value="{{ $user->id }}">{{ $user->name }}</option>
            @endforeach
          </select>
          {{-- @if($support_agent_id)
          <a href="{{ route('show-detailed-report',['id'=>$support_agent_id]) }}" class="text-red-800 mt-5 block w-full text-center">
            زیر گزارش عملکرد
          </a>
          @endif --}}
      </div>
    
  </form>
  
  <table class="w-full text-sm text-gray-500  text-center">
    <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
        <tr>
            <th scope="col" class="px-6 py-3">
                دوره
            </th>
            <th scope="col" class="px-6 py-3">
                نمره کل سوپروایزر
            </th>
            <th scope="col" class="px-6 py-3">
              نمره کل چت
            </th>
            <th scope="col" class="px-6 py-3">
              نمره کل تماس
            </th>
            <th scope="col" class="px-6 py-3">
              نمره کل احراز
            </th>
            <th scope="col" class="px-6 py-3">
              نمره کل خروجی
            </th>
            <th scope="col" class="px-6 py-3">
              نمره کل خطوط قرمز
            </th>

            <th scope="col" class="px-6 py-3">
              نمره کل آزمون ماهانه
            </th>

            <th scope="col" class="px-6 py-3">
              نمره کل qc
              (بدون احتساب خط قرمز)
            </th>
            <th scope="col" class="px-6 py-3">
              نمره کل qc
              (با احتساب خط قرمز)
            </th>
            <th scope="col" class="px-6 py-3">
              نمره کل کارشناس
            </th>
            <th scope="col" class="px-6 py-3">
              عملیات
            </th>
            {{-- 
            <th>
              chat qc resolution
            </th>
            <th>
              call qc resolution
            </th>
            --}}
        </tr>
    </thead>
    <tbody>

        @foreach ($periods as $period)
            <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                  {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
                </th>
                <td class="px-6 py-4">
                      {{ $supervisor_scores[$period->id] ?? '-' }}
                </td>

                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $chat_scores[$period->id] ?? '-' }}
                  @endif
                </td>
                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $call_scores[$period->id] ?? '-' }}
                  @endif
                </td>
                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $kyc_scores[$period->id] ?? '-' }}
                  @endif
                </td>
                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $outgoing_scores[$period->id] ?? '-' }}
                  @endif
                </td>
                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $redline_scores[$period->id] ?? '-' }}
                  @endif
                </td>

                <td class="px-6 py-4">
                  {{ $exam_stats?->where('period_id',$period->id)->first()?->value ?? '-' }}
                </td>

                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $qc_scores[$period->id] ?? '-' }}
                  @endif
                </td>
                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $final_qc_scores[$period->id] ?? '-' }}
                  @endif
                </td>
                <td class="px-6 py-4">
                  @if($period->id == 4)
                    -
                  @else
                    {{ $final_scores[$period->id] ?? '-' }}
                  @endif
                </td>

                <td class="px-6 py-4">
                  @if($period->id != 4 and $support_agent_id)
                    <a class="text-red-800" href="{{ route('show-detailed-report',['user_id'=>$support_agent_id,'period_id'=>$period->id]) }}">جزئیات</a>
                  @else
                    -
                  @endif
                </td>

            </tr>
        @endforeach
        
    </tbody>

  </table>

  <canvas id="myChart"></canvas>

</div>

<script>

      document.addEventListener('DOMContentLoaded', function () {
        
        window.addEventListener('reload', (event) => {

        const ctx = document.getElementById('myChart');
        
        let chartStatus = Chart.getChart("myChart");
        if (chartStatus != undefined)
        {
          chartStatus.destroy();
        }

        let periods = @js($periods->toArray());
        let labels = periods.map((item)=>item.id)
        let data = {
          labels: labels,
          datasets: [
            {
            label: 'نمرات',
            data: Object.values(event.detail.final_scores).map((value)=>value!='-' ? value : null),
            fill: false,
            borderColor: 'rgb(75, 192, 192)',
            tension: 0
            }
          ]
        };

        let options = {
          responsive: true,
          indexAxis: 'x',
          scales: {
            x: {
              title:{
                display: true,
                text : 'شناسه دوره'
              }
            },
            y: {
              // beginAtZero: true
            }
          },
          plugins: {
          legend: {
            position: 'top',
          },
          title: {
            display: true,
            text: 'عملکرد کلی'
          }
          }
        }

        new Chart(ctx, {
          type: 'line',
          data: data,
          options: options,
        });

        });
      
    })

</script>


  