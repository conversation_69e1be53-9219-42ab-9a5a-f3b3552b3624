<div>
    <form class="max-w-sm mx-auto">
  
      <div class="mb-5 space-y-3">
        
        <select wire:model.lazy="platform" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option hidden selected value="">انتخاب پلتفرم...</option>
                <option value="wallex">wallex</option>
                <option value="wallgold">wallgold</option>
          </select>
        
          <select wire:model.lazy="periods" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
              <option hidden selected value="">انتخاب دوره...</option>
                  <option value="1">آخرین دوره</option>
                    <option value="2">دو ماهه</option>
                    <option value="3">سه ماهه</option>
                    <option value="4">چهار ماهه</option>
                    <option value="6">شش ماهه</option>
                    <option value="8">هشت ماهه</option>
                    <option value="12">یک ساله</option>
            </select>

        </div>
      
    </form>
    
    <table class="max-w-sm mx-auto w-full text-sm text-gray-500  text-center">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
          <tr>
              <th scope="col" class="px-6 py-3">
                  رتبه
              </th>
              <th scope="col" class="px-6 py-3">
                  کارشناس
              </th>
                <th scope="col" class="px-6 py-3">
                    نمره سوپروایزر
                </th>
                <th scope="col" class="px-6 py-3">
                    نمره qc
                </th>
                @if(auth()->user()->role_id>3)
                <th scope="col" class="px-6 py-3">
                    تعداد رکوردهای qc شده
                </th>
                @endif
                <th scope="col" class="px-6 py-3">
                    نمره کل
                </th>
          </tr>
      </thead>
      <tbody>
          @foreach ($users as $user_id => $user)
                @if(!($loop->index >= 3 and auth()->user()->id !== $user_id and auth()->user()->role_id<2))
                    <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">
                    <td class="px-6 py-4">
                            {{$loop->index + 1}}
                    </td>
                    
                    <td class="px-6 py-4">
                            {{ $user['name'] }}
                            <span class="text-2xl">
                            @switch ($loop->index + 1)
                            @case(1)
                            🥇
                            @break
                            @case(2)
                            🥈
                            @break
                            @case(3)
                            🥉
                            @break
                            @default
                            @endswitch
                        </span>
                    </td>
                    @php
                        if($user['final_score']>=97){
                            $color = 'text-green-900';
                        }
                        elseif($user['final_score'] >=95){
                            $color = 'text-green-600';
                        }
                        elseif($user['final_score'] >=90){
                            $color = 'text-blue-700';
                        }
                        elseif($user['final_score'] >=85){
                            $color = 'text-yellow-500';
                        }
                        else{
                            $color = 'text-red-900';
                        }
                        
                    @endphp

                    <td class="px-6 py-4 {{ $color }}">
                    {{ $user['supervisor_score'] }}
                 </td>

                 <td class="px-6 py-4 {{ $color }}">
                    {{ $user['final_qc_score'] }}
                 </td>
                 @if(auth()->user()->role_id>3)
                  <td class="px-6 py-4 {{ $color }}">
                    {{ $user['count'] }}
                 </td>
                @endif
                 <td class="px-6 py-4 {{ $color }}">
                    {{ $user['final_score'] }}
                 </td>

                 </tr>
                @endif
          @endforeach
          
      </tbody>
  
    </table>
  
  </div>
  
    