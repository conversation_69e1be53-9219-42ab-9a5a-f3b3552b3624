<div class="relative overflow-x-auto">

    <div class="mb-5">
        <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" />
        <input wire:model.lazy="search" type="search" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="شناسه" />
        
        <select wire:model.lazy="incoming_type" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-32 p-2.5 ">
            <option selected hidden>ورودی</option>
            <option value="0">همه</option>
            <option value="chat">چت</option>
            <option value="call">تماس</option>
        </select>

        <select wire:model.lazy="support_agent_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-40 p-2.5 ">
            <option selected hidden>کارشناس پشتیبانی</option>
            <option value="0">همه</option>
            @foreach ($support_agents as $support_agent)
                <option value="{{ $support_agent->id }}">{{ $support_agent->name }}</option>
            @endforeach
        </select>
        @if(auth()->user()->role_id>1)
        <select wire:model.lazy="qc_agent_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-40 p-2.5 ">
            <option selected hidden>کارشناس qc</option>
            <option value="0">همه</option>
            @foreach ($qc_agents as $qc_agent)
                <option value="{{ $qc_agent->id }}">{{ $qc_agent->name }}</option>
            @endforeach
        </select>
        @endif
        <select wire:model.lazy="status" class="mt-1 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-72 p-2.5 ">
            <option selected hidden>وضعیت</option>
            <option value="0">همه</option>
            <option value="pending">در انتظار بررسی</option>
            <option value="accepted">تایید شده</option>
            <option value="accepted_partial">بخشی از اعتراض مورد تایید است</option>
            <option value="rejected">رد شده</option>
        </select>

        <span wire:loading>
            در حال پردازش...
        </span>

    </div>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500 text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        آیدی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شناسه رکورد
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کارشناس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ ثبت اعتراض
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وضعیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد تغییر وضعیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($objects as $object)
                    <tr wire:key="object-{{ $object->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $object->id }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $object->record->identity }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $object->record->user->name }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ verta($object->created_at)->format('Y/m/d') }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            @switch($object->status)
                                @case('pending')
                                    در انتظار بررسی
                                @break
                                @case('accepted')
                                    تایید شده
                                @break
                                @case('accepted_partial')
                                    بخشی از اعتراض مورد تایید است
                                @break
                                @case('rejected')
                                    رد شده
                                @break
                                @default
                                    نامعلوم                                    
                            @endswitch
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $object->status_change_count ?? '-' }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <a href="{{ route('create-object',['identity'=>$object->record->identity]) }}" class="font-medium text-gray-600 hover:underline mx-2">توضیحات</a>
                            <a href="{{ route('update-record',['identity'=>$object->record->identity]) }}" class="font-medium text-green-600 hover:underline mx-2">ویرایش</a>
                            <a wire:click="delete({{ $object->id }})" href="#" class="font-medium text-purple-600 hover:underline mx-2">حذف</a>
                        </th>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $objects->links() }}
    </div>
</div>
