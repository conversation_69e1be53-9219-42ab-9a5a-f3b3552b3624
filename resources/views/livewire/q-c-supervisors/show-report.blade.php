<div>
    <form class="max-w-sm mx-auto">
  
      <div class="mb-5">
        
        
          <select wire:model.lazy="support_agent_id" id="support_agent_id" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
              <option hidden selected value="">انتخاب کارشناس...</option>
              @foreach ($users as $user)
                  <option value="{{ $user->id }}">{{ $user->name }}</option>
              @endforeach
            </select>
            
        </div>
      
    </form>
    
    <table class="w-full text-sm text-gray-500  text-center">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
          <tr>
              <th scope="col" class="px-6 py-3">
                  دوره
              </th>
              <th scope="col" class="px-6 py-3">
                  نمره کل سرپرست
              </th>
          </tr>
      </thead>
      <tbody>
  
          @foreach ($periods as $period)
              <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">
                  <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
                  </th>
                  <td class="px-6 py-4">
                        {{ $supervisor_scores[$period->id] ?? '-' }}
                  </td>
              </tr>
          @endforeach
          
      </tbody>
  
    </table>

  </div>
  

  
  
    