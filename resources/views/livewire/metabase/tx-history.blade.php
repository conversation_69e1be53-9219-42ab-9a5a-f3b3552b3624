
<form wire:submit.prevent="email_file">
    <div class="mx-auto w-1/2 space-y-3">
        <div>
            <label for="national_code" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">کد ملی:</label>
            <input wire:model.lazy="national_code" type="text" id="national_code" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="0371357764" required />
        </div>
       
        <div>
            <label for="from" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">تاریخ شروع:</label>
            <input wire:model.lazy="from" type="text" data-jdp id="from" id="from" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="از" required />
        </div>

        <div>
            <label for="to" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">تاریخ پایان:</label>
            <input wire:model.lazy="to" type="text" data-jdp id="to" id="to" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="تا" required />
        </div>

        <div>
            <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">ایمیل:</label>
            <input disabled wire:model.lazy="email" type="text" data-jdp id="email" id="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="اتوماتیک" required />
        </div>

        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full px-5 py-2.5 text-center">ارسال ایمیل</button>
        
</form>
