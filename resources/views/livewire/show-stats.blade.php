<div>
    <form wire:submit.prevent="analyze" class="mx-auto">
        <div class="mb-5">
            <select wire:model.lazy="period_id" id="period_id" class="inline-block w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                <option hidden selected value="">انتخاب دوره...</option>
                @foreach ($periods as $period)
                <option value="{{ $period->id }}">
                    از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
                </option>
                @endforeach
            </select>

            <select wire:model.lazy="supervisor_id" id="supervisor_id" class="inline-block w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                <option hidden selected value="">انتخاب سوپروایزر...</option>
                <option value="0">همه</option>
                @foreach ($supervisors as $supervisor)
                <option value="{{ $supervisor->id }}">
                    {{ $supervisor->name }}
                </option>
                @endforeach
            </select>

            @isset($period_id)
                <a class="text-blue-900" href="{{ route('download',['period_id'=>$period_id]) }}">دانلود</a>
                <span class="text-red-700">&</span>
                <a class="text-blue-900" href="#" wire:click="picture()">اسنپ شات</a>    
                <span class="text-red-700">&</span>
                <a class="text-blue-900" href="{{ route('show-detailed-stats',['period_id'=>$period_id]) }}">تحلیل پارامترها</a>  
            @endisset
            <div wire:loading class="text-red-900">
                در حال پردازش...
            </div>
        </div>
    </form>
    
        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        کارشناس
                    </th>

                    <th scope="col" class="px-6 py-3">
                        سوپروایزر
                    </th>

                    @foreach ($parameters as $parameter)
                    <th scope="col" class="py-3">
                        {{ $parameter->name }}
                    </th>
                    @endforeach
                    <th scope="col" class="px-6 py-3">
                        نمره کل سوپروایزر
                    </th>

                    <th scope="col" class="px-6 py-3">
                        نمره کل چت
                    </th>
                    {{-- <th scope="col" class="px-6 py-3">
                        تعداد کل چت
                    </th> --}}
                    <th scope="col" class="px-6 py-3">
                        نمره کل تماس
                    </th>
                    {{-- <th scope="col" class="px-6 py-3">
                        تعداد کل تماس
                    </th> --}}
                    <th scope="col" class="px-6 py-3">
                        نمره کل احراز
                    </th>
                    {{-- <th scope="col" class="px-6 py-3">
                        تعداد کل احراز
                    </th> --}}
                    <th scope="col" class="px-6 py-3">
                        نمره کل خروجی
                    </th>
                    {{-- <th scope="col" class="px-6 py-3">
                        تعداد کل خروجی
                    </th> --}}
                    <th scope="col" class="px-6 py-3">
                        نمره کل خطوط قرمز
                    </th>

                    <th scope="col" class="px-6 py-3">
                        نمره کل qc
                        (بدون احتساب خط قرمز)
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نمره کل qc
                        (با احتساب خط قرمز)
                    </th>

                    <th scope="col" class="px-6 py-3">
                        نمره کل کارشناس
                    </th>
                </tr>
            </thead>
            <tbody>
                    <tr class="bg-gray-100 border-b  ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            حداکثر امتیاز پارامترها
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            -
                        </th>
                    @foreach ($parameters as $parameter)
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $parameter->score }}
                        </th>
                    @endforeach
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th>
                    {{-- <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th> --}}
                    {{-- <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th> --}}
                    {{-- <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th> --}}
                    {{-- <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        -
                    </th> --}}
                </tr>
                @foreach ($users as $user)
                <tr wire:key="{{ $period_id }}-{{ $user->id }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->name }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $this->supervisors->where('id',$user->supervisor_id)->first()?->name }}
                        </th>
                    @foreach ($parameters as $parameter)
                        <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->supervisor_parameters->firstWhere('id',$parameter->id)?->pivot?->value ?? '-' }}
                        </th>
                    @endforeach
                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $supervisor_scores[$user->id] }}
                    </th>

                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $chat_scores[$user->id] }}
                    </th>
                    {{-- <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $chat_count[$user->id] }}
                    </th> --}}

                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $call_scores[$user->id] }}
                    </th>
                    {{-- <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $call_count[$user->id] }}
                    </th> --}}

                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $kyc_scores[$user->id] }}
                    </th>
                    {{-- <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $kyc_count[$user->id] }}
                    </th> --}}

                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $outgoing_scores[$user->id] }}
                    </th>
                    {{-- <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $outgoing_count[$user->id] }}
                    </th> --}}

                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $redline_scores[$user->id] }}
                    </th>

                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $qc_scores[$user->id] }}
                    </th>
                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $final_qc_scores[$user->id] }}
                    </th>
                    <th title="{{ $user->name }}" scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $final_scores[$user->id] }}
                    </th>
                </tr>
                @endforeach
                
            </tbody>
        </table>
  </div>
  
    