<div>

    <div class="flex space-x-3 space-x-reverse">

        <div class="mb-4 w-48">
            <input wire:model.lazy="search" type="text" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="جستجو" />
        </div>

        <div class="mb-4 w-48">
            <select wire:model.lazy="channel" id="users" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
                <option hidden selected>انتخاب پلتفرم</option>
                <option value="C0889QPAMPD">تست</option>
                <option value="C044AHLCKCY">اطلاع رسانی والکس</option>
                <option value="C0826FMK266">اطلاع رسانی وال‌گلد</option>
                <option value="C04LB9KH83X">faq والکس</option>
                <option value="C0823UKN4CC">faq وال‌گلد</option>
                <option value="C03UUD0135X">SLS والکس</option>
                <option value="C08EYMKQWKU">SLS وال‌گلد</option>
              </select>
        </div>

        <div class="mb-4 w-48">
            <select wire:model.lazy="user_id" id="users" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
                <option hidden selected>انتخاب کاربر ایجاد کننده</option>
                <option value="">همه</option>
                    @foreach ($users as $user)
                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                    @endforeach
              </select>
        </div>
    
        <div class="mb-4 w-40">
            <select wire:model.lazy="category_id" id="users" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
                <option hidden selected>فیلتر طبقه بندی</option>
                <option value="">همه</option>
                    @foreach ($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->option }}</option>
                    @endforeach
              </select>
        </div>

        <div class="mb-4 w-40">
            <input wire:model.lazy="from_date" data-jdp type="text" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="از تاریخ" />
        </div>

        <div class="mb-4 w-40">
            <input wire:model.lazy="to_date" data-jdp type="text" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="تا تاریخ" />
        </div>

        <div class="mb-4 flex items-center justify-center">
            <div class="">
                <input wire:model.lazy="event" id="red" type="checkbox" value="" class="w-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 "/>
                <label for="red" class="ms-2 text-sm font-medium text-gray-900 ">event</label>
            </div>
            
        </div>


    </div>
    


    <div class="relative overflow-x-auto shadow-md sm:rounded-lg mb-3">
          
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        طبقه‌بندی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ارسال شده توسط
                    </th>
                    <th scope="col" class="px-6 py-3">
                        موضوع درخت‌دانش
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عنوان اطلاعیه
                    </th>
    
                    <th scope="col" class="px-6 py-3">
                        توضیحات
                    </th>
    
                    <th scope="col" class="px-6 py-3">
                        تاریخ
                    </th>
    
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($announcements as $announcement)
                    
                
                <tr class="bg-white border-b ">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $announcement->id }}
                    </th>
    
                    <td class="px-6 py-4">
                        {{ $announcement->category?->option ?? '-' }}
                    </td>
    
                    <td class="px-6 py-4">
                        {{ $announcement->user?->name }}
                    </td>
    
                    <td class="px-6 py-4">
                        {{ $announcement->tree ?? '-' }}
                    </td>
    
                    <td class="px-6 py-4">
                        {{ $announcement->subject ?? '-' }}
                    </td>
    
                    <td class="px-6 py-4">
                        {{ str_replace(['*'],[''],$announcement->body) ?? '-' }}
                    </td>
    
                    <td class="px-6 py-4">
                        {{ verta($announcement->created_at) ?? '-' }}
                    </td>
    
                    <td class="px-6 py-4">
                        <a
                        class=" text-green-700"
                        target="_blank"
                        href="{{ route('announcements-edit',$announcement->id) }}">ویرایش</a>
                        <a
                        target="_blank"
                        href="https://wallexexchange.slack.com/archives/{{ $announcement->channel }}/p{{ str_replace('.','',$announcement->thread_ts) }}"
                        class=" text-blue-700">لینک اسلک</a>
                        <button
                        wire:click="get_reacts({{ $announcement->id }})"
                        >گزارش</button>
                        <button
                        wire:click="delete({{ $announcement->id }})"
                        class=" text-red-700">حذف</button>
                    </td>
    
                </tr>
    
                @endforeach
            </tbody>
        </table>
    </div>

    {{ $announcements->links() }}

</div>