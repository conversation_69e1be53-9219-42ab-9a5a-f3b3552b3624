<div class="mx-auto max-w-4xl">
    
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach

    <form wire:submit.prevent="create" wire:confirm="امکان ویرایش بعد از تایید وجود ندارد، آیا مطمئن هستید؟!">

        <select wire:model="channel" id="channel" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
            
            <option hidden selected>انتخاب کانال</option>
            <option value="C0889QPAMPD">تست</option>
            
            <option value="C044AHLCKCY">اطلاع رسانی والکس</option>
            <option value="C0826FMK266">اطلاع رسانی وال‌گلد</option>

            <option value="C04LB9KH83X">faq والکس</option>
            <option value="C0823UKN4CC">faq وال‌گلد</option>
            <option value="C03UUD0135X">SLS والکس</option>
            <option value="C08EYMKQWKU">SLS وال‌گلد</option>
            
            
      </select>

        <select wire:model="category_id" id="category_id" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
            
            <option hidden selected>انتخاب طبقه‌بندی (الزامی)</option>

            @foreach ($categories as $category)
                <option value={{$category->id}}>{{ $category->option }}</option>
            @endforeach

      </select>

       <input wire:model="url" type="text" id="subject" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="لینک مرتبط (اختیاری)" />

        <input wire:model="tree" type="text" id="subject" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="موضوع درخت‌دانش (اختیاری)" />

        <input wire:model="subject" type="text" id="subject" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="عنوان اطلاعیه" required />

        <textarea required wire:model="body" id="body" rows="6" class=" block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 " placeholder="توضیحات..."></textarea>
        <div class="mb-2 text-gray-400 text-sm">برای بولد کردن کافی است قسمت مورد نظر را داخل ستاره قرار دهید، مثلا *bold*</div>
        
        <input wire:model="files" class=" block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 " id="multiple_files" type="file" multiple>
        <div class="mb-2 text-gray-400 text-sm">فایل ها به صورت جداگانه در thread پیام اصلی ارسال خواهند شد</div>

        <div class="flex items-center mb-3">
            <input wire:model.lazy="slack" id="slack-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 ">
            <label for="slack-checkbox" class="ms-2 text-sm font-medium text-gray-900 ">ارسال اتوماتیک به اسلک</label>
        </div>

        <div class="flex items-center mb-3">
            <input wire:model="mention" id="mention-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 ">
            <label for="mention-checkbox" class="ms-2 text-sm font-medium text-gray-900 ">منشن کردن @channel در اسلک</label>
        </div>

        <div class="flex items-center mb-3">
            <input disabled id="preview-checkbox" type="checkbox" checked class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 ">
            <label for="preview-checkbox" class="ms-2 text-sm font-medium text-gray-900 ">پیش نمایش لینک در اسلک</label>
        </div>

        <div class="flex items-center mb-3">
            <input wire:model.lazy="bale" id="bale-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 ">
            <label for="bale-checkbox" class="ms-2 text-sm font-medium text-gray-900 ">ارسال اتوماتیک به بله</label>
        </div>

        <div class="flex items-center mb-3">
            <input wire:model="event" id="event-checkbox" type="checkbox" checked class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 ">
            <label for="event-checkbox" class="ms-2 text-sm font-medium text-gray-900 ">ذخیره به عنوان event</label>
        </div>

        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد</button>
    </form>
</div>
