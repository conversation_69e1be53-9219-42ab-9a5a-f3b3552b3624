<div class="mx-auto max-w-4xl">
    
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach

    <form wire:submit.prevent="save" wire:confirm="آیا مطمئن هستید؟!">
        <select wire:model="category_id" id="category_id" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 0">
            
            <option hidden selected>انتخاب طبقه‌بندی (الزامی)</option>

            @foreach ($categories as $category)
                <option value={{$category->id}}>{{ $category->option }}</option>
            @endforeach

      </select>

        <input wire:model="url" type="text" id="subject" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 0" placeholder="لینک مرتبط (اختیاری)" />

        <input wire:model="tree" type="text" id="subject" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 0" placeholder="موضوع درخت‌دانش (اختیاری)" />

        <input wire:model="subject" disabled type="text" id="subject" class="mb-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 0" placeholder="عنوان اطلاعیه" required />
        

        <textarea required wire:model="body" id="body" rows="6" class=" block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 0" placeholder="توضیحات..."></textarea>
        <div class="mb-2 text-gray-400 text-sm">برای بولد کردن کافی است قسمت مورد نظر را داخل ستاره قرار دهید، مثلا *bold*</div>
        

        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 focus:outline-none ">ویرایش</button>
        
    </form>
    @unless($event)
            <button wire:click="convert" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 focus:outline-none ">تبدیل به event</button>
    @endunless
</div>
