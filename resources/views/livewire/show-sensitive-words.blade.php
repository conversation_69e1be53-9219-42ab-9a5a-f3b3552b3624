<div>
    @foreach ($errors->all() as $error)
            <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
                {{$error}}
          </div>
        @endforeach
    
        <form wire:submit.prevent="add" class="mb-5">
        <input type="text" wire:model="value" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="فیلتر" required />
        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد</button>
        </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        مقدار
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وضعیت هشدار
                    </th>
                    <th scope="col" class="px-6 py-3">
                       وضعیت حساسیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($sensitive_words as $sensitive_word)
                    <tr wire:key="sensitive_word-{{ $sensitive_word->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $sensitive_word->id }}
                        </th>                      
                        <td class="px-6 py-4">
                            <input wire:change="change_value({{ $sensitive_word->id }},$event.target.value)" value="{{ $sensitive_word->value }}" type="text" class="border-0 text-sm text-center">
                        </td>
                        <td class="px-6 py-4">
                            <select class="w-44" wire:change="toggle({{ $sensitive_word->id }})">
                                <option {{ $sensitive_word->alert ? 'selected' : '' }} value="1">فعال</option>
                                <option {{ $sensitive_word->alert ? '' : 'selected' }} value="0">غیرفعال</option>
                            </select>
                        </td>
                        <td class="px-6 py-4">
                            <select class="w-44" wire:change="toggle_sensitivity({{ $sensitive_word->id }})">
                                <option {{ $sensitive_word->mode == 'includes' ? 'selected' : '' }} value="includes">فعال</option>
                                <option {{ $sensitive_word->mode == 'excludes' ? 'selected' : '' }} value="excludes">غیرفعال</option>
                            </select>
                        </td>
                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $sensitive_word->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
