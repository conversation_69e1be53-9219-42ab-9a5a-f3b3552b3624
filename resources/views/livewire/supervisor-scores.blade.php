<div>
    <form class="max-w-sm mx-auto">
  
      <div class="mb-5">
        
          <select wire:model.lazy="periods" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
              <option hidden selected value="">انتخاب دوره...</option>
                  <option value="1">آخرین دوره</option>
                  <option value="2">دو ماهه</option>
                  <option value="3">سه ماهه</option>
                  <option value="4">چهار ماهه</option>
                  <option value="6">شش ماهه</option>
                  <option value="8">هشت ماهه</option>
                  <option value="12">یک ساله</option>
            </select>

        </div>
      
    </form>
    
    <table class="max-w-sm mx-auto w-full text-sm text-gray-500  text-center">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
          <tr>
              <th scope="col" class="px-6 py-3">
                  رتبه
              </th>
              <th scope="col" class="px-6 py-3">
                  سوپروایزر
              </th>
              <th scope="col" class="px-6 py-3">
                نمره سوپروایزر زیرمجموعه ها
              </th>
              <th scope="col" class="px-6 py-3">
                نمره qc زیرمجموعه ها
              </th>
              <th scope="col" class="px-6 py-3">
                نمره کل زیرمجموعه ها
            </th>
              
          </tr>
      </thead>
      <tbody>
  
          @foreach ($supervisors_score as $supervisor_score)
              <tr wire:key="{{ now() }}" class="bg-white border-b ">
                 <td class="px-6 py-4">
                        {{$loop->index + 1}}
                 </td>
                 
                 <td class="px-6 py-4">
                        {{ $supervisor_score['name'] }}
                 </td>
                 <td class="px-6 py-4">
                    {{ $supervisor_score['supervisor_score'] }}
                 </td>
                 <td class="px-6 py-4">
                    {{ $supervisor_score['final_qc_score'] }}
                 </td>
                 <td class="px-6 py-4">
                    {{ $supervisor_score['final_score'] }}
                 </td>
  
                  
  
              </tr>
          @endforeach
          
      </tbody>
  
    </table>
  
  </div>
  
    