
<div class="space-y-3">
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        uuid
                    </th>
                    <th scope="col" class="px-6 py-3">
                        queue
                    </th>
                    <th scope="col" class="px-6 py-3">
                        payload
                    </th>
                    <th scope="col" class="px-6 py-3">
                        exception
                    </th>
                    <th scope="col" class="px-6 py-3">
                        failed_at
                    </th>
                    
                </tr>
            </thead>
            <tbody>
                
                @foreach ($jobs as $job)
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            {{ $job->id }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $job->uuid }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $job->queue }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $job->payload }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $job->exception }}
                        </td>
                        <td class="px-6 py-4">
                            {{ verta($job->failed_at) }}
                        </td>
                    </tr>
                @endforeach
                
                
            </tbody>
        </table>
    </div>
    {{ $jobs->links() }}
</div>