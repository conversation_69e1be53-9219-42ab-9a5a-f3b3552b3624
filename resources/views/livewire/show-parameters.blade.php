<div class="relative overflow-x-auto">

    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach

    <form wire:submit.prevent="create" class="mb-5">
    <input type="text" wire:model="name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="عنوان" required />
    <input type="number" min="0" max="100" wire:model="score" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="امتیاز" required />
    <select wire:model.lazy="incoming_type" class="bg-gray-50 border border-gray-300 w-48 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
        <option selected hidden>انتخاب...</option>
        
        <option value="supervisor">سوپروایزر</option>

        <option value="chat">چت</option>
        <option value="call">تماس</option>
        <option value="kyc">احراز</option>
        <option value="outgoing">خروجی</option>
        <option value="red_chat">خط قرمز چت</option>
        <option value="red_call">خط قرمز تماس</option>
        <option value="red_outgoing">خط قرمز خروجی</option>

        {{-- توسط سرپرست --}}
        <option value="QC_head">ارزیابی qc</option>
        <option value="supervisor_head">ارزیابی سوپروایزر</option>

        <option value="faq">faq</option>
        <option value="email">ایمیل</option>
        <option value="ticket">تیکت</option>

      </select>
      <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد پارامتر</button>
      <select wire:model.lazy="active" class="bg-gray-50 border border-gray-300 w-48 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
        <option selected hidden>فیلتر</option>
        <option value="1">فقط فعال</option>
        <option value="0">همه</option>
      </select>
    </form>
    
    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ورودی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        امتیاز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وضعیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($parameters as $parameter)
                    <tr wire:key="parameter-{{ $parameter->id }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $parameter->id }}
                        </th>
                        <td class="px-6 py-4">
                            <input wire:change="changeName({{ $parameter->id }},$event.target.value)" value="{{ $parameter->name }}" type="text" class="border-0 text-sm text-center">
                        </td>
                        <td class="px-6 py-4">
                            <select wire:change="changeIncomingType({{ $parameter->id }},$event.target.value)" class="w-32 border-0">
                                <option {{ $parameter->incoming_type == 'chat' ? 'selected' : '' }} value="chat">چت</option>
                                <option {{ $parameter->incoming_type == 'call' ? 'selected' : '' }} value="call">تماس</option>
                                <option {{ $parameter->incoming_type == 'kyc' ? 'selected' : '' }} value="kyc">احراز</option>
                                <option {{ $parameter->incoming_type == 'outgoing' ? 'selected' : '' }} value="outgoing">خروجی</option>
                                <option {{ $parameter->incoming_type == 'supervisor' ? 'selected' : '' }} value="supervisor">سوپروایزر</option>
                                <option {{ $parameter->incoming_type == 'red_chat' ? 'selected' : '' }} value="red_chat">خط قرمز چت</option>
                                <option {{ $parameter->incoming_type == 'red_call' ? 'selected' : '' }} value="red_call">خط قرمز تماس</option>
                                <option {{ $parameter->incoming_type == 'red_call' ? 'selected' : '' }} value="red_outgoing">خط قرمز خروجی</option>
                                <option {{ $parameter->incoming_type == 'supervisor_head' ? 'selected' : '' }} value="supervisor_head">ارزیابی سوپروایزر</option>
                                <option {{ $parameter->incoming_type == 'QC_head' ? 'selected' : '' }} value="supervisor_head">ارزیابی qc</option>
                                <option {{ $parameter->incoming_type == 'faq' ? 'selected' : '' }} value="faq">faq</option>
                                <option {{ $parameter->incoming_type == 'email' ? 'selected' : '' }} value="email">email</option>
                                <option {{ $parameter->incoming_type == 'ticket' ? 'selected' : '' }} value="ticket">ticket</option>
                            </select>
                        </td>
                        <td class="px-6 py-4">
                            <input wire:change="changeScore({{ $parameter->id }},$event.target.value)" value="{{ $parameter->score }}" type="number" class="border-0 text-sm text-center">
                        </td>
                        <td class="px-6 py-4">
                            <select wire:change="changeStatus({{ $parameter->id }},$event.target.value)" class="w-32 border-0">
                                <option {{ $parameter->active ? 'selected' : '' }} value="1">فعال</option>
                                <option {{ !$parameter->active ? 'selected' : '' }} value="0">غیرفعال</option>
                            </select>
                        </td>
                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $parameter->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    
</div>
