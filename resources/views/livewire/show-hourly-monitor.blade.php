<div>
    <div class="mb-5">
        <input wire:model.lazy="from" data-jdp type="text" readonly class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" />
        <input wire:model.lazy="to" data-jdp type="text" readonly class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" />
    </div>

    <div class="relative overflow-x-auto mb-10">
        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        کارشناس qc
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 0
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 1
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 2
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 3
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 4
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 5
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 6
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 7
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 8
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 9
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 10
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 11
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 12
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 13
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 14
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 15
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 16
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 17
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 18
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 19
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 20
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 21
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 22
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 23
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($users as $user)
                    <tr class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->name }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h0_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h1_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h2_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h3_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h4_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h5_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h6_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h7_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h8_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h9_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h10_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h11_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h12_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h13_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h14_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h15_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h16_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h17_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h18_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h19_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h20_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h21_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h22_records }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->h23_records }}
                        </th>
                        
                        
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="relative overflow-x-auto mb-10">
        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        کارشناس qc
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 0
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 1
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 2
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 3
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 4
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 5
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 6
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 7
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 8
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 9
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 10
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 11
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 12
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 13
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 14
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 15
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 16
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 17
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 18
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 19
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 20
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 21
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 22
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ساعت 23
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($users as $user)
                    @if ($user->qc_records_count)
                        <tr class="bg-white border-b ">
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ $user->name }}
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h0_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h1_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h2_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h3_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h4_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h5_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h6_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h7_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h8_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h9_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h10_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h11_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h12_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h13_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h14_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h15_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h16_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h17_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h18_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h19_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h20_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h21_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h22_records / $user->qc_records_count * 100,2) }}%
                            </th>
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                                {{ round($user->h23_records / $user->qc_records_count * 100,2) }}%
                            </th>
                        </tr>
                    @endif
                @endforeach
            </tbody>
        </table>
    </div>
</div>
