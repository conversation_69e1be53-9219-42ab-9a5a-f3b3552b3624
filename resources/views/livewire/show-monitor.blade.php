<div>
    
    <div class="mb-5">
        <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" />

        <label for="default-checkbox" class="me-2 text-sm font-medium text-gray-900 ">جستجو بر اساس تاریخ ایجاد</label>
        <input wire:model.lazy="filter" id="default-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">
    </div>

    <div class="relative overflow-x-auto mb-10">
        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        کارشناس qc (average trace time)
                    </th>
                    <th scope="col" class="px-6 py-3">
                        chat
                    </th>
                    <th scope="col" class="px-6 py-3">
                        call
                    </th>
                    <th scope="col" class="px-6 py-3">
                        kyc
                    </th>
                    <th scope="col" class="px-6 py-3">
                        outgoing
                    </th>
                    <th scope="col" class="px-6 py-3">
                        faq
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ticket
                    </th>
                    <th scope="col" class="px-6 py-3">
                        email
                    </th>
                    
                    <th scope="col" class="px-6 py-3">
                        all
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($qc_agents as $qc_agent)
                    <tr class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $qc_agent->name }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_chat_trace_time) }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_call_trace_time) }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_kyc_trace_time) }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_outgoing_trace_time) }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_faq_trace_time) }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_ticket_trace_time) }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_email_trace_time) }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ floor($qc_agent->average_trace_time) }}
                        </th>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="relative overflow-x-auto mb-10">
        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        کارشناس qc
                    </th>
                    <th scope="col" class="px-6 py-3">
                        چت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تماس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        احراز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        خروجی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد کل رکوردها
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراضات در انتظار
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراضات تایید شده
                    </th>
                    <th scope="col" class="px-6 py-3">
                        بخشی از اعتراض تایید شده
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراضات رد شده
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد کل اعتراض ها
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($qc_agents as $qc_agent)
                    <tr class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $qc_agent->name }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $qc_agent->chat_qc_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->call_qc_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->kyc_qc_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->outgoing_qc_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->qc_records_count }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->objects_pending }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->objects_accepted }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->objects_accepted_partial }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->objects_rejected }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $qc_agent->objects }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="relative overflow-x-auto mb-10">
        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        کارشناس پشتیبانی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        چت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تماس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        احراز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        خروجی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد کل رکوردها
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراضات در انتظار
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراض تایید شده
                    </th>
                    <th scope="col" class="px-6 py-3">
                        بخشی از اعتراض تایید شده
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراض رد شده
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراض ثبت شده
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اعتراض تکراری
                    </th>
                    
                </tr>
            </thead>
            <tbody>
                @foreach ($support_agents as $support_agent)
                    <tr class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $support_agent->name }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $support_agent->chat_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->call_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->kyc_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->outgoing_records }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->records_count }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->objects_pending }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->objects_accepted }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->objects_accepted_partial }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->objects_rejected }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->objects }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $support_agent->reobjects }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
