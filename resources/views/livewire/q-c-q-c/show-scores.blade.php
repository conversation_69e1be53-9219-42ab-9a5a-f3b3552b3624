<form wire:submit.prevent="save" class="max-w-4xl mx-auto">
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
        </div>
    @endforeach

    <div class="mb-5">
        <label for="user_id" class="block mb-2 text-sm font-medium text-gray-900 ">کارشناس</label>
        <select wire:model.lazy="user_id" id="user_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
            <option hidden selected value="">انتخاب...</option>
            @foreach ($users as $user)
                <option value="{{ $user->id }}">{{ $user->name }}</option>
            @endforeach
          </select>
      </div>
      <div class="mb-5">
        <label for="period_id" class="block mb-2 text-sm font-medium text-gray-900 ">دوره</label>
        <select wire:model.lazy="period_id" id="period_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
            <option hidden selected value="">انتخاب...</option>
            @foreach ($periods as $period)
                <option value="{{ $period->id }}">
                از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
                </option>
            @endforeach
          </select>
      </div>

      @if(!is_null($period_id) and !is_null($user_id))
      <div class="flex items-start mb-6">

        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        امتیاز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نمره
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($parameters as $parameter)
                    <tr wire:key="{{ $parameter->id }}-{{ $user_id }}-{{ $period_id }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $parameter->name }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $parameter->score }}
                        </td>
                        <td class="px-6 py-4">
                            @if(auth()->user()->role_id>3)
                            <input value="{{ $scores[$parameter->id] }}" wire:change="changeScore({{$parameter->id}},$event.target.value)" min="0" max="{{ $parameter->score }}" type="number" step="any" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                            @else
                            {{ $scores[$parameter->id] }}
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            @if(auth()->user()->role_id>3)
                                <input type="text" value="{{ $notes[$parameter->id] }}" wire:change="changeNote({{ $parameter->id }},$event.target.value)" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                            @else
                                {{ $notes[$parameter->id] }}
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

    </div>
      @endif
    
    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm block w-full px-5 py-2.5 text-center ">ذخیره</button>
  </form>
  