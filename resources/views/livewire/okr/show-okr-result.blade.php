<div>
    <form class="max-w-sm mx-auto">
  
      <div class="mb-5">
        
        
          <select wire:model.lazy="period_id" id="period_id" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
              <option hidden selected value="">انتخاب دوره...</option>
              @foreach ($periods as $period)
                  <option value="{{ $period->id }}">
                    از
                    {{ verta($period->from)->formatDate() }}
                    تا
                    {{ verta($period->to)->formatDate() }}
                  </option>
              @endforeach
            </select>

        </div>
      
    </form>
    
    <table class="w-full text-sm text-gray-500  text-center">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
          <tr>
              <th scope="col" class="px-6 py-3">
                  سوپروایزر
              </th>
              <th scope="col" class="px-6 py-3">
                درصد اشتغال
              </th>
          </tr>
      </thead>
      <tbody>
  
          @foreach ($supervisor_team as $supervisor_member)
              <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">

                <td class="px-6 py-4">
                    {{ $supervisor_member->name }}
              </td>

                  <td class="px-6 py-4">
                        {{ $supervisor_team_percent[$supervisor_member->id] ?? '-' }}
                  </td>
              </tr>
          @endforeach
            <tr>
                <td class="px-6 py-4">
                    میانگین
              </td>
              <td class="px-6 py-4">
                    {{ $supervisor_team_percent ? array_sum($supervisor_team_percent) / count($supervisor_team_percent) : '-' }}
             </td>
            </tr>
      </tbody>
  
    </table>

    <br><br>

    <table class="w-full text-sm text-gray-500  text-center">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
            <tr>
                <th scope="col" class="px-6 py-3">
                    کارشناس qc
                </th>
                <th scope="col" class="px-6 py-3">
                  درصد اشتغال
                </th>
            </tr>
        </thead>
        <tbody>
    
            @foreach ($qc_team as $qc_member)
                <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">
  
                  <td class="px-6 py-4">
                      {{ $qc_member->name }}
                </td>
  
                    <td class="px-6 py-4">
                          {{ $qc_team_percent[$qc_member->id] ?? '-' }}
                    </td>
                </tr>
                <tr>
                    <td class="px-6 py-4">
                        میانگین
                  </td>
                  <td class="px-6 py-4">
                        {{ $qc_team_percent ? array_sum($qc_team_percent) / count($qc_team_percent) : '-' }}
                 </td>
                </tr>
            @endforeach
            
        </tbody>
    
      </table>
  
  </div>