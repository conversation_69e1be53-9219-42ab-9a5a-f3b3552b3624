<div>
    @foreach ($errors->all() as $error)
            <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
                {{$error}}
          </div>
        @endforeach
    
        <form wire:submit.prevent="insert" class="mb-5">
        <input type="text" wire:model="from" data-jdp class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" required />
        
        <input type="text" wire:model="to" data-jdp class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" required />
          <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد دوره</button>
        </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50  ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        از تاریخ
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تا تاریخ
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($periods as $period)
                    <tr wire:key="parameter-{{ $period->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $period->id }}
                        </th>
                        <td class="px-6 py-4">
                            <input data-jdp wire:change="change_start({{ $period->id }},$event.target.value)" value="{{ verta($period->from)->format('Y/m/d') }}" type="text" class="border-0 text-sm text-center">
                        </td>
                        
                        <td class="px-6 py-4">
                            <input data-jdp wire:change="change_end({{ $period->id }},$event.target.value)" value="{{ verta($period->to)->format('Y/m/d') }}" type="text" class="border-0 text-sm text-center">
                        </td>
    
                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $period->id }})" href="#" class="font-medium text-red-600 hover:underline mx-2">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
