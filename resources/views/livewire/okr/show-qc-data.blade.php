<div>
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
        </div>
    @endforeach

        <div class="mb-5">
            <select wire:model.lazy="period_id" id="period_id" class="inline-block w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                <option hidden selected value="">انتخاب دوره...</option>
                @foreach ($periods as $period)
                <option value="{{ $period->id }}">
                    از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
                </option>
                @endforeach
            </select>
            
        {{-- <a href="{{ route('show-items') }}" class="text-blue-900">راهنمای استفاده</a> --}}
            <div wire:loading class="text-red-900">
                در حال پردازش...
            </div>
        </div>

        @if($period_id)
            <div wire:key="period-{{ $period_id }}" class="sm:rounded-lg">
                <table class="w-full text-sm text-gray-500  text-center">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                        <tr>
                            <th scope="col" class="px-6 py-3">
                                کارشناس
                            </th>

                            @foreach($items as $item)
                                <th scope="col" class="px-6 py-3">
                                    {{ $item->title }}
                                </th>
                            @endforeach
                            
                        </tr>
                    </thead>

                    <tbody>
                        <tr>
                            <td class="py-4 font-bold">
                                <span>توضیحات</span>
                            </td>
                            @foreach($items as $item)
                                <td class="py-4">
                                    {{ $item->description }}
                                </td>
                            @endforeach
                        </tr>
                        @foreach ($users as $user)
                            <tr wire:key="user-{{ $user->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                                
                                <td class="py-4">
                                    {{ $user->name }}
                                </td>
                                
                                @foreach($items as $item)
                                    <td class="py-4" title="{{ $user->name }}">
                                    <input value="{{ $stats->where('user_id',$user->id)->where('qc_item_id',$item->id)->first()?->value }}"
                                    wire:change="change({{ $user->id }},{{ $item->id }},$event.target.value)"
                                    type="number" class="border-0 text-center text-sm">
                                    </td>
                                @endforeach
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
</div>
