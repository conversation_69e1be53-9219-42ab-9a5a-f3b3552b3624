<div class="relative overflow-x-auto">
    
    <div class="mb-5">
        <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="w-28 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="w-28 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" />
        
        <select wire:model.lazy="period_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-52 p-2.5 ">
            <option selected hidden>دوره...</option>
            <option value="0">همه</option>
            @foreach ($periods as $period)
                <option value="{{ $period->id }}">
                    از
                    {{ verta($period->from)->formatDate() }}
                    تا
                    {{ verta($period->to)->formatDate() }}
                </option>
            @endforeach
        </select>

        <input wire:model.lazy="search" type="search" id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="شناسه یا موضوع" />
        
        <select wire:model.lazy="incoming_type" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-32 p-2.5 ">
            <option selected hidden>ورودی</option>
            <option value="0">همه</option>
            <option value="chat">چت</option>
            <option value="call">تماس</option>
            <option value="kyc">احراز</option>
            <option value="outgoing">خروجی</option>
            
            <option value="faq">faq</option>
            <option value="email">ایمیل</option>
            <option value="ticket">تیکت</option>
        </select>

        <select wire:model.lazy="support_agent_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-40 p-2.5 ">
            <option selected hidden>کارشناس پشتیبانی</option>
            <option value="0">همه</option>
            @foreach ($support_agents as $support_agent)
                <option value="{{ $support_agent->id }}">{{ $support_agent->name }}</option>
            @endforeach
        </select>
        @if(in_array(auth()->user()->role_id,[2,4,5,6]))
        <select wire:model.lazy="qc_agent_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-40 p-2.5 ">
            <option selected hidden>کارشناس qc</option>
            <option value="0">همه</option>
            @foreach ($qc_agents as $qc_agent)
                <option value="{{ $qc_agent->id }}">{{ $qc_agent->name }}</option>
            @endforeach
            <option value="except">همه بجز qc</option>
        </select>
        @endif
        <label for="default-checkbox" class="me-2 text-sm font-medium text-gray-900 ">خط قرمز</label>
        <input wire:model.lazy="red" id="default-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">

        <label for="default-checkbox2" class="me-2 text-sm font-medium text-gray-900 ">نادیده گرفتن</label>
        <input wire:model.lazy="ignore" id="default-checkbox2" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">
    
        <span wire:loading>
            در حال پردازش...
        </span>

    </div>
    
    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500 text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        آیدی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کارشناس پشتیبانی
                    </th>
                    @if(in_array(auth()->user()->role_id,[2,4,5,6]))
                    <th scope="col" class="px-6 py-3">
                        کارشناس کنترل کیفیت
                    </th>
                    @endif
                    <th scope="col" class="px-6 py-3">
                        ورودی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ رکورد
                    </th>
                    @if(auth()->user()->role_id>1)
                    <th scope="col" class="px-6 py-3">
                        تاریخ trace
                    </th>
                    @endif
                    <th scope="col" class="px-6 py-3">
                        نمره
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($records as $record)
                    <tr wire:key="record-{{ $record->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $record->id }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{-- <a target="_blank" class="underline {{ $record->object ? 'text-red-700' : '' }}" href="https://my.livechatinc.com/archives/{{ $record->identity }}">{{ $record->identity }}</a> --}}
                            {{ $record->identity }}
                            
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $record->user->name }}
                        </th>
                        @if(in_array(auth()->user()->role_id,[2,4,5,6]))
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $record->qc_agent->name }}
                        </th>
                        @endif
                        <th scope="row" class="{{ (!is_null($record->feedback) and trim($record->feedback)!='-') ? 'text-green-600' : '' }} px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            @if($record->incoming_type=='chat')
                                چت
                            @elseif($record->incoming_type=='call')
                                تماس
                            @elseif($record->incoming_type=='kyc')
                                احراز
                            @elseif($record->incoming_type=='outgoing')
                                خروجی
                            @elseif($record->incoming_type=='faq')
                                faq
                            @elseif($record->incoming_type=='email')
                                email
                            @elseif($record->incoming_type=='ticket')
                                ticket
                            @else
                                -
                            @endif
                        </th>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ verta($record->incoming_date)->format('Y/m/d') }}
                        </th>

                        @if(auth()->user()->role_id>1)
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ verta($record->created_at) }}
                        </th>
                        @endif
                        
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            @php
                                $sum=0.0;
                                $total=0.0;
                                foreach ($record->qc_parameters as $qc_parameter)
                                {
                                    if(is_null($qc_parameter->pivot->value)) {
                                        continue;
                                    }
                                    $sum += $qc_parameter->score;
                                    $total += $qc_parameter->pivot->value;
                                }
                                if($record->ignore) {echo "-";}
                                else{echo $sum ? round($total/$sum*100,2) : '-';}
                                
                            @endphp
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <a data-modal-target="modal-{{ $record->id }}" data-modal-toggle="modal-{{ $record->id }}" href="#" class="font-medium  hover:underline mx-2">جزئیات</a>
                            <div id="modal-{{ $record->id }}" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                <div class="relative p-4 w-full max-w-2xl max-h-full">
                                    <!-- Modal content -->
                                    <div class="relative bg-white rounded-lg shadow ">
                                        <!-- Modal header -->
                                        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t ">
                                            <h3 class="text-xl font-semibold text-gray-900 ">
                                                ریز نمرات
                                            </h3>
                                            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center  " data-modal-hide="modal-{{ $record->id }}">
                                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                                </svg>
                                                <span class="sr-only">Close modal</span>
                                            </button>
                                        </div>
                                        <!-- Modal body -->
                                        <div class="p-4 md:p-5 space-y-4">
                                            <p class="text-base leading-relaxed text-gray-500 ">

                                                @foreach ($record->qc_parameters as $parameter)
                                                    
                                                    <span class="{{ $parameter->pivot->value == $parameter->score ? 'text-green-900' : 'text-red-900' }}">
                                                        {{ $parameter->name }} : {{ $parameter->pivot->value ?? '-' }} از {{ $parameter->score }}
                                                    </span>
                                                    <br>
                                                @endforeach

                                                @if($record->red_lines->whereNotNull('pivot.value')->count())
                                                    <br>
                                                    پارامترهای خطوط قرمز:
                                                    <br />
                                                    @foreach ($record->red_lines as $red_line)
                                                        
                                                        <span class="{{ $red_line->pivot->value == $red_line->score ? 'text-green-900' : 'text-red-900' }}">
                                                            {{ $red_line->name }} : {{ $red_line->pivot->value ?? '-' }} از {{ $red_line->score }}
                                                        </span>
                                                        <br>
                                                    @endforeach
                                                @endif

                                                @if(in_array($record->incoming_type,['chat','call']))
                                                    <br>
                                                    <span class="break-words whitespace-normal word-break text-blue-900">
                                                        موضوع مکالمه:
                                                        {{ $record->incoming_subject }}
                                                    </span>
                                                    <br>
                                                    <span class="break-words whitespace-normal word-break text-blue-900">
                                                        موضوع ثبت شده:
                                                        {{ $record->recorded_subject }}
                                                    </span>
                                                @endif
                                            </p>

                                            <p class="break-words whitespace-normal word-break text-base leading-relaxed text-gray-500 ">
                                                توضیحات: {{ $record->message }}
                                            </p>

                                            <p class="break-words whitespace-normal word-break text-base leading-relaxed text-gray-500 ">
                                                فیدبک: {{ $record->feedback }}
                                            </p>

                                            <p class="break-words whitespace-normal word-break text-base leading-relaxed text-gray-500 ">
                                                کوچ روزانه: {{ $record->daily_coach }}
                                            </p>
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <a href="{{ route('create-object',['identity'=>$record->identity]) }}" class="font-medium text-red-600 hover:underline mx-2">اعتراض</a>
                            <a href="{{ route('update-record',['identity'=>$record->identity]) }}" class="font-medium text-green-600 hover:underline mx-2">ویرایش</a>
                            {{-- <a wire:click="delete({{ $record->id }})" href="#" class="font-medium text-purple-600 hover:underline mx-2">حذف</a> --}}
                        </th>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $records->links() }}
    </div>
</div>
