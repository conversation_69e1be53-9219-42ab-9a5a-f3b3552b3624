<div class="relative overflow-x-auto">
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach

    <form wire:submit.prevent="create" class="mb-5">
        <input type="text" wire:model="name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="اسم" required />
        <input type="text" wire:model="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="ایمیل" required />
        <input type="password" wire:model="password" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="پسورد" required />

          <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 focus:outline-none ">ایجاد کارشناس</button>
    </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نام
                    </th>
                    <th scope="col" class="px-6 py-3">
                        nickname
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وزن
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد چت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد تماس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد احراز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد خروجی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کد پرسنل
                    </th>
                    <th scope="col" class="px-6 py-3">
                        slack member ID
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شماره eyebeam
                    </th>
                    <th scope="col" class="px-6 py-3">
                        روز کاری کارشناس در ماه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        سمت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        سوپروایزر
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شیفت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        پلتفرم
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وضعیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ایمیل
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($users as $user)
                    <tr wire:key="user-{{ $user->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $user->id }}
                        </th>
                        <td class="px-6 py-4">
                            <input value="{{ $user->name }}" wire:change="changeName({{ $user->id }},$event.target.value)" type="text" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->nickname }}" wire:change="changeNickName({{ $user->id }},$event.target.value)" type="text" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->weight }}" wire:change="changeWeight({{ $user->id }},$event.target.value)" type="number" min="0" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->trace_chat }}" wire:change="changeTraceChat({{ $user->id }},$event.target.value)" type="number" min="0" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->trace_call }}" wire:change="changeTraceCall({{ $user->id }},$event.target.value)" type="number" min="0" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->trace_kyc }}" wire:change="changeTraceKyc({{ $user->id }},$event.target.value)" type="number" min="0" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->trace_outgoing }}" wire:change="changeTraceOutgoing({{ $user->id }},$event.target.value)" type="number" min="0" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->personal_code }}" wire:change="changePersonalCode({{ $user->id }},$event.target.value)" type="text" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->slack_id }}" wire:change="changeSlackId({{ $user->id }},$event.target.value)" type="text" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->eyebeam_id }}" wire:change="changeEyebeamId({{ $user->id }},$event.target.value)" type="number" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $user->working_days }}" wire:change="changeWorkingDays({{ $user->id }},$event.target.value)" type="number" min="0" max="31" class="border-0 text-center text-sm">
                        </td>
                        <td class="px-6 py-4">
                            <select wire:change="changeRole({{ $user->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                @foreach ($roles as $key => $value)
                                    <option {{ $user->role_id == $key ? 'selected' : '' }} value="{{ $key }}">{{ $value }}</option>                                
                                @endforeach
                            </select>
                        </td>
                        <td class="px-6 py-4">
                            <select wire:change="changeSupervisorId({{ $user->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                <option value="0">انتخاب...</option>
                                @foreach ($users->where('role_id',3) as $supervisor)
                                    <option {{ $user->supervisor_id == $supervisor->id ? 'selected' : '' }} value="{{ $supervisor->id }}">{{ $supervisor->name }}</option>
                                @endforeach
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <input value="{{ $user->shift }}" wire:change="changeShift({{ $user->id }},$event.target.value)" type="text" class="border-0 text-center text-sm">
                        </td>

                        <td class="px-6 py-4">
                            <input value="{{ $user->platform }}" wire:change="changePlatform({{ $user->id }},$event.target.value)" type="text" class="border-0 text-center text-sm">
                        </td>
                        
                        <td class="px-6 py-4">
                            <select wire:change="changeStatus({{ $user->id }},$event.target.value)" class="border-0 w-40 text-sm">
                                    <option {{ $user->status == false ? 'selected' : '' }} value="0">غیرفعال</option>
                                    <option {{ $user->status == true ? 'selected' : '' }} value="1">فعال</option>
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <input value="{{ $user->email }}" wire:change="changeEmail({{ $user->id }},$event.target.value)" type="text" class="border-0 text-center text-sm">
                        </td>

                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $user->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                            <a wire:click="reset_password({{ $user->id }})" href="#" class="font-medium text-blue-600  hover:underline mx-2">reset</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    
</div>
