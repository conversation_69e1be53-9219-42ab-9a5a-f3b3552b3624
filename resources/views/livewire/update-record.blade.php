<form wire:submit.prevent="save" class="w-7/12 mx-auto">

    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50  " role="alert">
            {{$error}}
      </div>
    @endforeach

    <div class="grid gap-6 md:grid-cols-2 mb-6">
        <div>
            <label for="first_name" class="block mb-2 text-sm font-medium text-gray-900 ">شناسه</label>
            <input disabled wire:model="identity" type="text" id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="SCMTK8HM29" required />
        </div>
        <div>
            <label for="incoming_type" class="block mb-2 text-sm font-medium text-gray-900 ">ورودی</label>
            <select disabled wire:model="incoming_type" id="incoming_type" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
                <option hidden>انتخاب...</option>
                <option value="chat">چت</option>
                <option value="call">تماس</option>
                <option value="kyc">احراز</option>
                <option value="outgoing">خروجی</option>

                <option value="faq">faq</option>
                <option value="email">ایمیل</option>
                <option value="ticket">تیکت</option>
              </select>
        </div>
        <div>
            <label for="incoming_date" class="block mb-2 text-sm font-medium text-gray-900 ">تاریخ مکالمه یا احراز</label>
            <input wire:model="incoming_date" data-jdp id="incoming_date" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required disabled />
        </div>
        <div>
            <label for="support_agent_id" class="block mb-2 text-sm font-medium text-gray-900 ">کارشناس</label>
            <select wire:model="support_agent_id" id="support_agent_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
                <option hidden>انتخاب...</option>
                @foreach ($users as $user)
                    <option value="{{ $user->id }}">{{ $user->name }}</option>
                @endforeach
              </select>
        </div>
        <div>
            <label for="incoming_subject" class="block mb-2 text-sm font-medium text-gray-900 ">موضوع مکالمه</label>
            <input wire:model.lazy="incoming_subject" type="text" id="incoming_subject" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>
        <div>
            <label for="recorded_subject" class="block mb-2 text-sm font-medium text-gray-900 ">موضوع ثبت شده</label>
            <input wire:model="recorded_subject" type="text" id="recorded_subject" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>

        <div>
            <label for="crm_identity" class="block mb-2 text-sm font-medium text-gray-900 ">شناسه crm</label>
            <input wire:model="crm_identity" type="text" id="crm_identity" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>
        <div>
            <label for="coefficient" class="block mb-2 text-sm font-medium text-gray-900 ">ضریب اهمیت</label>
            <input disabled wire:model="coefficient" type="text" id="coefficient" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>
    </div>

    @if($qc_parameters)
    <div class="flex items-start mb-6">

        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50  ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        امتیاز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نمره
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($qc_parameters as $qc_parameter)
                    <tr wire:key="qc_parameters-{{ $qc_parameter->id }}" class="bg-white border-b  ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $qc_parameter->name }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $qc_parameter->score }}
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $scores[$qc_parameter->id] }}" wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

    </div>
    @endif

    <div class="flex items-start mb-6">
        <textarea wire:model="message" id="message" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 " placeholder="توضیحات"></textarea>
    </div>

    <div class="flex items-start mb-6">
        <textarea wire:model="feedback" id="feedback" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 " placeholder="فیدبک"></textarea>
    </div>

    @if(in_array($incoming_type,['chat','call','outgoing']))
        <div class="flex items-start mb-6">
            <div class="flex items-center h-5">
            <input wire:model.lazy="red" id="red" type="checkbox" value="" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 "/>
            </div>
            <label for="red" class="ms-2 text-sm font-medium text-gray-900 ">خط قرمز</label>
        </div>
    @endif

    @if($red)
    <div class="flex items-start mb-6">

        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50  ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        امتیاز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نمره
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($red_lines as $red_line)
                    <tr wire:key="red_lines-{{ $red_line->id }}" class="bg-white border-b  ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $red_line->name }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $red_line->score }}
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $red_scores[$red_line->id] }}" wire:change="changeRedScore({{$red_line->id}},$event.target.value)" min="0" max="{{ $red_line->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

    </div>
    @endif

    {{-- <div class="flex items-start mb-6">
        <div class="flex items-center h-5">
        <input wire:model="draft" id="draft" type="checkbox" value="" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 "/>
        </div>
        <label for="draft" class="ms-2 text-sm font-medium text-gray-900 ">ذخیره به عنوان پیش نویس</label>
    </div> --}}

    <div class="space-x-4 space-x-reverse mb-3">
        <label class="inline-flex items-center cursor-pointer">
            <input wire:model="challenging" type="checkbox" value="" class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600"></div>
            <span class="ms-3 text-sm font-medium text-gray-900 ">چالش برانگیز</span>
        </label>
        
        <label class="inline-flex items-center cursor-pointer">
            <input wire:model="ignore" type="checkbox" value="" class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600"></div>
            <span class="ms-3 text-sm font-medium text-gray-900 ">نادیده گرفتن</span>
        </label>
    </div>
    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center ">آپدیت رکورد</button>
</form>
