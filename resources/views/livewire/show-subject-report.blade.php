<div>
    <form class="max-w-sm mx-auto">
  
      <div class="mb-5">
          <select wire:model.lazy="platform" id="platform" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
              <option hidden selected value="">انتخاب پلتفرم...</option>
                  <option value="wallex">والکس</option>
                  <option value="wallgold">وال گلد</option>
            </select>
        </div>

        <div class="mb-5">
            <select wire:model.lazy="period_id" id="platform" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                <option hidden selected value="">انتخاب دوره...</option>
                @foreach ($periods as $period)
                    <option value="{{ $period->id }}">از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}</option>
                @endforeach
              </select>
          </div>

          <div class="mb-5">
            <select wire:model.lazy="parameter_id" id="parameter_id" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                <option hidden selected value="">انتخاب پارامتر...</option>
                @foreach ($parameters as $parameter)
                    <option value="{{ $parameter->id }}">{{ $parameter->name }}</option>
                @endforeach
              </select>
          </div>
      
    </form>
    
    <table class="w-full text-sm text-gray-500  text-center">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
          <tr>
              <th scope="col" class="px-6 py-3">
                  موضوع مکالمه
              </th>
              <th scope="col" class="px-6 py-3">
                  میانگین نمره
              </th>
          </tr>
      </thead>
      <tbody>
  
        <div wire:loading>loading...</div>
          @foreach ($list as $key => $value)
              <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">
                  <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $key }}
                  </th>
                  <td class="px-6 py-4">
                    {{ $value }}
                  </td>
              </tr>
          @endforeach
      </tbody>
  
    </table>
  </div>
  

  
  
    