<div class="relative overflow-x-auto">

    <form x-data="{open: false}" wire:submit.prevent="create" class="mb-5">
        <input wire:model.lazy="search" type="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="سرچ" />

        <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" />

        <select wire:model.lazy="support_agent_email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-40 p-2.5 ">
            <option selected hidden>کارشناس پشتیبانی</option>
            <option value="0">همه</option>
            @foreach ($support_agents as $support_agent)
                <option value="{{ $support_agent->email }}">{{ $support_agent->name }}</option>
            @endforeach
        </select>

        <select wire:model.lazy="shift" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-32 p-2.5 ">
            <option selected hidden>شیفت</option>
            <option value="0">همه</option>
            <option value="صبح">صبح</option>
            <option value="عصر">عصر</option>
            <option value="شب">شب</option>
        </select>

        <select wire:model.lazy="orderBy" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-36 p-2.5 ">
            <option value="id" selected hidden>ترتیب نزولی</option>
            <option value="created_at">زمان ایجاد</option>
            <option value="FRT">FRT</option>
            <option value="chat_duration">chat_duration</option>
            <option value="queues_duration">queues_duration</option>
        </select>

        <select wire:model.lazy="filter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-36 p-2.5 ">
            <option selected hidden>فیلتر</option>
            <option value="0">همه</option>
                <option value="missed-chat">missed-chats</option>
                <option value="inactive-transfer">inactive-transfers</option>
                <option value="lost-connection">lost-connections</option>
                <option value="queue-abandonment">queue-abandonment</option>
                <option value="signed-out-transfer">signed-out-transfers</option>
                <option value="chat-transfer">chat-transfers</option>
                <option value="take-over">take-overs</option>
                <option value="inactive-archived">inactive-archived</option>

                <option value="offline">offline</option>
                <option value="ai">ai</option>
                
                <option value="comment">comment</option>

                <option value="ticket">api</option>
                
                <option value="chat_rated">rated chats</option>
                <option value="good">rated good</option>
                <option value="bad">rated bad</option>

                <option value="started_by_agent">started by agent</option>

                <option value="closed_by_agent">closed by agent</option>


        </select>

        <select wire:model.lazy="platform" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-28 p-2.5 ">
            <option selected hidden>platform</option>
                <option value="wallex">wallex</option>
                <option value="wallgold">wallgold</option>
        </select>

        <svg :class="open && 'rotate-90'" x-on:click="open = ! open" class="inline w-6 h-6 text-gray-800 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 5.326 5.7a.909.909 0 0 0 1.348 0L13 1"/>
        </svg>

        <div x-show="open" x-cloak class="space-y-3 mt-3">

            <div class="text-green-800">total count : {{ $count }}</div>

            <div class="text-red-800">
                <div>min FRT : {{ $min_frt }} s</div>
                <div>average FRT : {{ $average_frt }} s</div>
                <div>max FRT : {{ $max_frt }} s</div>
            </div>

            <div class="text-blue-800">
                <div>min chat duration : {{ $min_chat_duration }} s</div>
                <div>average chat duration (AHT) : {{ $aht }} s</div>
                <div>max chat duration : {{ $max_chat_duration }} s</div>
            </div>

            <div class="text-yellow-800">
                <div>queue count : {{ $queue_count }}</div>
                <div>average queue duration : {{ $average_queue }} s</div>
                <div>max queue duration : {{ $max_queue }} s</div>
            </div>
        
        </div>


        <span wire:loading>
            در حال پردازش...
        </span>

    </form>
    
    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        chat id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        thread id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        admin id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        user id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        author id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        last message by
                    </th>
                    <th scope="col" class="px-6 py-3">
                        FRT
                    </th>
                    <th scope="col" class="px-6 py-3">
                        chat duration
                    </th>
                    <th scope="col" class="px-6 py-3">
                        queues duration
                    </th>
                    <th scope="col" class="px-6 py-3">
                        agents count
                    </th>
                    <th scope="col" class="px-6 py-3">
                        transfers count
                    </th>
                    <th scope="col" class="px-6 py-3">
                        agent message count
                    </th>
                    <th scope="col" class="px-6 py-3">
                        customer message count
                    </th>

                    <th scope="col" class="px-6 py-3">
                        flagged
                    </th>
                    
                    <th scope="col" class="px-6 py-3">
                        called_by_name
                    </th>
                    <th scope="col" class="px-6 py-3">
                        survey
                    </th>
                    <th scope="col" class="px-6 py-3">
                        start_scenario
                    </th>

                    <th scope="col" class="px-6 py-3">
                        chat_management
                    </th>

                    <th scope="col" class="px-6 py-3">
                        platform
                    </th>

                    <th scope="col" class="px-6 py-3">
                        offline
                    </th>

                    <th scope="col" class="px-6 py-3">
                        api ticket
                    </th>

                    <th scope="col" class="px-6 py-3">
                        chat not transferred
                    </th>

                    <th scope="col" class="px-6 py-3">
                        تاریخ
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($livechats as $livechat)
                    <tr wire:key="livechat-{{ $livechat->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $livechat->id }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $livechat->chat_id }}
                        </td>
                        <td class="px-6 py-4 underline">
                            <a target="_blank" href="https://my.livechatinc.com/archives/{{ $livechat->thread_id }}">{{ $livechat->thread_id }}</a>
                        </td>
                        <td class="px-6 py-4 underline">
                            <a target="_blank" href="https://admin-api.wallex.ir/encode-id/{{ $livechat->admin_user_id }}">{{ $livechat->admin_user_id }}</a>
                        </td>
                        <td class="px-6 py-4">
                            {{ $livechat->user_id }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $livechat->author_id }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $livechat->last_message_by }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $livechat->FRT }}
                        </td>
                        
                        <td class="px-6 py-4">
                            {{ $livechat->chat_duration }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->queues_duration }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->agents_count }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->transfers_count }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->agent_message_count }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->customer_message_count }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->flagged }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->called_by_name }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->survey }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->start_scenario }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->chat_management }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->platform }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->offline }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->ticket }}
                        </td>

                        <td class="px-6 py-4">
                            {{ $livechat->chatbot }}
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ verta($livechat->created_at) }}
                        </td>

                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $livechat->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                            <span>&</span>
                            <a wire:click="inspect({{ $livechat->id }})" href="#" class="font-medium text-blue-600  hover:underline mx-2">استعلام</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $livechats->links() }}
    </div>
</div>
