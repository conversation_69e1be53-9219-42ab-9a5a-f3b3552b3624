
<div class="space-y-3">
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        chat_id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        uuid
                    </th>
                    <th scope="col" class="px-6 py-3">
                        created_at
                    </th>
                    <th scope="col" class="px-6 py-3">
                        body
                    </th>
                    
                </tr>
            </thead>
            <tbody>
                
                @foreach ($outputs as $output)
                    <tr class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            {{ $output->id }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $output->chat_id }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $output->uuid }}
                        </td>
                        <td class="px-6 py-4">
                            {{ verta($output->created_at) }}
                        </td>
                        <td class="px-6 py-4">
                            @php
                                $json = json_encode(json_decode($output->body,true),JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)
                            @endphp
                            {{ $json }}
                        </td>
                    </tr>
                @endforeach
                
                
            </tbody>
        </table>
    </div>
    {{ $outputs->links() }}
</div>