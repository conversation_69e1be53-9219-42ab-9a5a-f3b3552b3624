<div class="relative overflow-x-auto">
    
    <div class="shadow-md sm:rounded-lg">
        <div class="text-center py-3 text-red-600 bg-gray-200 w-full">چت های برتر</div>
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        آیدی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کارشناس پشتیبانی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ورودی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ رکورد
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($records as $record)
                    <tr wire:key="record-{{ $record->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $record->id }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <a target="_blank" class="underline" href="https://my.livechatinc.com/archives/{{ $record->identity }}">{{ $record->identity }}</a>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $record->user->name }}
                        </th>
                        <th scope="row" class="{{ (!is_null($record->feedback) and trim($record->feedback)!='-') ? 'text-green-600' : '' }} px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            @if($record->incoming_type=='chat')
                                چت
                            @elseif($record->incoming_type=='call')
                                تماس
                            @elseif($record->incoming_type=='kyc')
                                احراز
                            @else
                                -
                            @endif
                        </th>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ verta($record->incoming_date)->format('Y/m/d') }}
                        </th>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $records->links() }}
    </div>
</div>
