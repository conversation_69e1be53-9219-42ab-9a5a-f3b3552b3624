<div>
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
        </div>
    @endforeach
    
    <div class="mb-5">
        <select wire:model.lazy="period_id" id="period_id" class="inline-block w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option hidden selected value="">انتخاب دوره...</option>
            @foreach ($periods as $period)
            <option value="{{ $period->id }}">
                از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
            </option>
            @endforeach
        </select>
        <div wire:loading class="text-red-900">
            در حال پردازش...
        </div>
    </div>

    @if($period_id)
        <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 " role="alert">
            هشدار:
            با کلیک روی هر گزینه، دیتای کارشناسان برای همان پارامتر استخراج می شود و در
            <a class="text-red-700" href="{{ route('show-data') }}">جدول دیتا</a>
            مطابق دوره انتخاب شده ذخیره می گردند.
      </div>
        <br>

        <button wire:click="extract_everything" type="button" class="text-white bg-violet-700 hover:bg-violet-800 focus:ring-4 focus:ring-violet-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">همه (احتیاط)</button>

        <br>

        <button wire:click="extract_chat_count" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تعداد چت</button>
        <button wire:click="extract_chat_aht" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">AHT چت</button>
        {{-- <button wire:click="extract_lost_connection" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تعداد lost connection</button> --}}
        <button wire:click="extract_unassigned_chats" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تعداد unassigned chats</button>
        <br>
        
        <button wire:click="extract_wrong_subject" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ثبت تیکت با موضوع اشتباه</button>
        <button wire:click="extract_repetitive" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ثبت تیکت با موضوع تکراری</button>
        <button wire:click="extract_wrong_user" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">انتخاب کاربر اشتباه</button>
        <button wire:click="extract_wrong_sl" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">انتخاب گزینه ارجاع به SLS به اشتباه</button>
        <button wire:click="extract_back_to_call" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">برگشت تیکت به علت نیاز به تکمیل اطلاعات</button>
        <br>
        <button wire:click="extract_position_logs" type="button" class="focus:outline-none text-white bg-yellow-600 hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ثبت تردد و استراحت CRM و سایت پوزیشن</button>
        <button wire:click="extract_waiting_queue" type="button" class="focus:outline-none text-white bg-yellow-600 hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">مدت زمان انتظار در صف</button>
        <br>
        <button wire:click="extract_leave_announcement" type="button" class="focus:outline-none text-white bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-2.5 mb-2 ">مرخصی به موقع و حضور به موقع در محیط کار</button>

    @endif
</div>
