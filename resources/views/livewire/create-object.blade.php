<form wire:submit.prevent="save" class="w-1/2 mx-auto">

    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach

    <div class="grid gap-6 mb-6">
        <div>
            

        <div>
            @if($objection)
                    وضعیت: 
                <select wire:change="changeStatus($event.target.value)" class="w-72 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                    <option {{ $objection->status=='pending' ? 'selected' : '' }} value="pending">در انتظار بررسی</option>
                    <option {{ $objection->status=='accepted' ? 'selected' : '' }} value="accepted">تایید شده</option>
                    <option {{ $objection->status=='accepted_partial' ? 'selected' : '' }} value="accepted_partial">بخشی از اعتراض مورد تایید است</option>
                    <option {{ $objection->status=='rejected' ? 'selected' : '' }} value="rejected">رد شده</option>
                </select>
            @endif
            <button type="submit" class="inline text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center ">ثبت</button>
            <label for="identity" class="mb-2 text-sm font-medium text-gray-900 ">شناسه: </label>
            <a class="underline text-blue-600" href="{{ route('update-record',['identity'=>$identity]) }}">{{ $identity }}</a>
            {{-- <input disabled wire:model="identity" type="text" id="identity" class="inline bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " /> --}}
        </div>
    </div>

    
    <div class="flex items-start mb-6">
        <textarea wire:model="message" id="message" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 " placeholder="توضیحات"></textarea>
    </div>

    <div>
        @foreach ($comments as $comment)
            
<div class="flex items-start gap-2.5 mb-5">
    <div class="flex flex-col w-full leading-1.5 p-4 border-gray-200 bg-gray-100 rounded-e-xl rounded-es-xl ">
       <div class="flex items-center space-x-2 rtl:space-x-reverse">
          <span class="text-sm font-semibold text-gray-900 ">{{ $comment->user->name }}</span>
          <span class="text-sm font-normal text-gray-500 ">{{ verta($comment->created_at) }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
           class="size-6 cursor-pointer" wire:click="delete({{ $comment->id }})">
            <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
          </svg>
          
       </div>
       <p class="text-sm font-normal py-2.5 text-gray-900 ">{{ $comment->value }}</p>
    </div>
    
    
 </div>
 
        @endforeach
    </div>

</form>
