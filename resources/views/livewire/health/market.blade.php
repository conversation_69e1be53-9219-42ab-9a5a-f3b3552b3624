<section class=" p-3 sm:p-5">
    <div class="mx-auto max-w-screen-xl px-4 lg:px-12">

    <input wire:model.lazy="filter" type="search" id="search" class="mb-3 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="سرچ" />

    <select wire:model.lazy="buy_status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-32 p-2.5 ">
        <option selected hidden>وضعیت خرید</option>
        <option value="0">همه</option>
        <option value="ENABLE">فعال</option>
        <option value="DISABLE">غیرفعال</option>
    </select>

    <select wire:model.lazy="sell_status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-32 p-2.5 ">
        <option selected hidden>وضعیت فروش</option>
        <option value="0">همه</option>
        <option value="ENABLE">فعال</option>
        <option value="DISABLE">غیرفعال</option>
    </select>

        <div class="bg-white dark:bg-gray-800 relative shadow-md sm:rounded-lg overflow-hidden">

            <div class="overflow-x-auto">
                <table class="w-full text-sm text-center text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">کوین</th>
                            <th scope="col" class="px-4 py-3">وضعیت خرید</th>
                            <th scope="col" class="px-4 py-3">وضعیت فروش</th>
                            <th scope="col" class="px-4 py-3">قیمت خرید</th>
                            <th scope="col" class="px-4 py-3">قیمت فروش</th>
                            <th scope="col" class="px-4 py-3">درصد اختلاف</th>
                            <th scope="col" class="px-4 py-3">آخرین پایش وضعیت</th>
                            <th scope="col" class="px-4 py-3">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($data as $item)
                            <tr class="border-b dark:border-gray-700" wire:key="item-{{$item['id']}}">
                                <td class="px-4 py-3">{{ $item['symbol'] }}</td>
                                <td class="px-4 py-3 {{ $item['buyStatus']=='ENABLE' ? 'text-green-700' : 'text-red-700' }}">{{ $item['buyStatus'] }}</td>
                                <td class="px-4 py-3 {{ $item['sellStatus']=='ENABLE' ? 'text-green-700' : 'text-red-700' }}">{{ $item['sellStatus'] }}</td>
                                <td class="px-4 py-3">{{ $item['BUY'] }}</td>
                                <td class="px-4 py-3">{{ $item['SELL'] }}</td>
                                <td class="px-4 py-3 text-blue-700">{{ $item['diff'] }}</td>
                                <td class="px-4 py-3">{{ $item['checked_at'] }}</td>
                                <td class="px-4 py-3 cursor-pointer flex items-center justify-center"
                                >
                                    <button
                                    wire:click="freshData({{$item['id']}})"
                                    >
                                    <svg
                                        class="h-4"
                                        fill="#ff0505" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 489.645 489.645" xml:space="preserve" stroke="#ff0505"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <path d="M460.656,132.911c-58.7-122.1-212.2-166.5-331.8-104.1c-9.4,5.2-13.5,16.6-8.3,27c5.2,9.4,16.6,13.5,27,8.3 c99.9-52,227.4-14.9,276.7,86.3c65.4,134.3-19,236.7-87.4,274.6c-93.1,51.7-211.2,17.4-267.6-70.7l69.3,14.5 c10.4,2.1,21.8-4.2,23.9-15.6c2.1-10.4-4.2-21.8-15.6-23.9l-122.8-25c-20.6-2-25,16.6-23.9,22.9l15.6,123.8 c1,10.4,9.4,17.7,19.8,17.7c12.8,0,20.8-12.5,19.8-23.9l-6-50.5c57.4,70.8,170.3,131.2,307.4,68.2 C414.856,432.511,548.256,314.811,460.656,132.911z"></path> </g> </g>
                                    </svg>
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                            
                    </tbody>
                </table>
            </div>
           
        </div>
    </div>
    </section>