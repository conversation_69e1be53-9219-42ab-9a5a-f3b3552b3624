<div>
    <div class="mb-5">
        <input wire:model.lazy="search" type="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="سرچ" />
        
        <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" />

        <span wire:loading>
            در حال پردازش...
        </span>

    </div>
    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        id
                    </th>
                    <th scope="col" class="px-6 py-3">
                        thread_id (chat_id)
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($chats as $chat)
                        <td wire:key="chat-{{ $chat->id }}" class="px-6 py-4">
                            {{ $chat->id }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat->livechat->thread_id }}
                        </td>

                        <td class="px-6 py-4">
                            {{ verta($chat->created_at) }}
                        </td>

                        <td class="px-6 py-4">
                            <a href="{{ route('show-livechats',['id'=>$chat->livechat_id]) }}" class="font-medium text-blue-600  hover:underline mx-2">جزئیات</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $chats->links() }}
    </div>
</div>
