<div class="relative overflow-x-auto">
    
    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        دوره
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ ایجاد اسنپ شات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($snapshots as $snapshot)
                    <tr wire:key="snapshot-{{ $snapshot->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <td class="px-6 py-4">
                            {{ $snapshot->id }}
                        </td>
                        <td class="px-6 py-4">
                            از {{ verta($snapshot->period->from)->format('Y/m/d') }}
                            تا {{ verta($snapshot->period->to)->format('Y/m/d') }}
                        </td>
                        <td class="px-6 py-4">
                            {{ verta($snapshot->created_at) }}
                        </td>

                        <td class="px-6 py-4">
                            <input type="text" class="text-center" value="{{ $snapshot->note }}" wire:change="changeNote({{$snapshot->id}},$event.target.value)">
                        </td>

                        <td class="px-6 py-4">
                            <a href="{{ route('show-snapshot',['id'=>$snapshot->id]) }}" class="text-blue-700">مشاهده</a>
                            <span>&</span>
                            <a href="{{ route('show-snapshot-report',['id'=>$snapshot->id]) }}" class="text-green-700">گزارش</a>
                            <span>&</span>
                            <a href="#" wire:click="delete({{ $snapshot->id }})" class="text-red-700">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $snapshots->links() }}
    </div>
</div>
