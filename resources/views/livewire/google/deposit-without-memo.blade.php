<div>
        @foreach ($errors->all() as $error)
            <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
                {{$error}}
          </div>
        @endforeach
          
        <form class="mb-5">
        <input type="text" wire:model.lazy="search" class="w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="هش یا ایمیل یا موبایل یا کد ملی یا اسکرین شات" />
        
        
        <button type="button" wire:click="sync_sheet" class="border rounded py-2 px-4 bg-blue-700 text-white">auto-update from googlesheet</button>
        
        <label for="default-checkbox" class="me-2 text-sm font-medium text-gray-900 ">فیلتر کردن موارد بدون نتیجه</label>
        <input wire:model.lazy="filter" id="default-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">
    
        <label for="default-checkbox2" class="me-2 text-sm font-medium text-gray-900 ">فقط موارد تخصیص به من</label>
        <input wire:model.lazy="only_me" id="default-checkbox2" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">
            
        <span class="mr-5">count : {{$count}}</span>

        <span wire:loading>
            در حال پردازش...
        </span>
        </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کارشناس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کد ملی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کوین
                    </th>
                    <th scope="col" class="px-6 py-3">
                        هش
                    </th>
                    <th scope="col" class="px-6 py-3">
                        اسکرین شات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ممو
                    </th>
                    <th scope="col" class="px-6 py-3">
                        مقدار
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات کاربر
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ 
                    </th>
                    <th scope="col" class="px-6 py-3">
                       ایمیل
                    </th>
                    <th scope="col" class="px-6 py-3">
                        موبایل
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نتیجه 
                    </th>
                    <th scope="col" class="px-6 py-3">
                        علت رد 
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات کارشناس
                    </th>
                    
                </tr>
            </thead>
            <tbody>
                @foreach ($wrong_deposits as $wrong_deposit)
                    <tr wire:key="wrong_deposit-{{ $wrong_deposit->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $wrong_deposit->id }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                           @if(is_null($wrong_deposit->updated_by))
                                <button class="text-red-900" wire:click="assign({{ $wrong_deposit->id }})">تخصیص به من</button>
                           @else
                                {{ $wrong_deposit->user?->name }}
                           @endif
                        </th>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->national_code }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->coin }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->hash }}
                        </td>
                        <td class="px-6 py-4">
                            <a target="_blank" class="text-blue-800" href="{{$wrong_deposit->screenshot}}">{{$wrong_deposit->screenshot}}</a>
                        </td>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->memo }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->amount }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->user_note }}
                        </td>
                        <td class="px-6 py-4">
                            {{ verta($wrong_deposit->form_filled_at) }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->email }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $wrong_deposit->phone_number }}
                        </td>
                        

                        <td class="px-6 py-4">
                            <select wire:change="changeResult({{ $wrong_deposit->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                <option hidden value="">انتخاب...</option>
                                    @foreach ($results as $result)
                                        <option {{ $wrong_deposit->result == $result ? 'selected' : '' }} value="{{ $result }}">{{$result }}</option>
                                    @endforeach
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <select wire:change="changeReason({{ $wrong_deposit->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                <option hidden value="">انتخاب...</option>
                                    @foreach ($reasons as $reason)
                                        <option {{ $wrong_deposit->reason == $reason ? 'selected' : '' }} value="{{ $reason }}">{{$reason }}</option>
                                    @endforeach
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <input wire:change="changeNote({{ $wrong_deposit->id }},$event.target.value)" value="{{ $wrong_deposit->agent_note }}" type="text" class="border-0 text-sm text-center">
                        </td>

                        
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $wrong_deposits->links() }}
    </div>
</div>
