<div>
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
        </div>
    @endforeach

    <form class="mb-5">
        <input type="text" wire:model.lazy="search" class="w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="جستجو شماره همراه" />
        
        <label for="without_result" class="me-2 text-sm font-medium text-gray-900 ">فیلتر کردن موارد بدون نتیجه</label>
        <input wire:model.lazy="without_result" id="without_result" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">

        <label for="call_again" class="me-2 text-sm font-medium text-gray-900 ">فیلتر کردن موارد درخواست تماس مجدد</label>
        <input wire:model.lazy="call_again" id="call_again" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">
    
        <label for="only_me" class="me-2 text-sm font-medium text-gray-900 ">فقط موارد تخصیص به من</label>
        <input wire:model.lazy="only_me" id="only_me" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500  focus:ring-2 ">
            
        <span wire:loading>
            در حال پردازش...
        </span>
        </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کارشناس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شماره همراه
                     </th>
                    <th scope="col" class="px-6 py-3">
                       آیدی ادمین
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نام کاربر
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وضعیت تماس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نتیجه تماس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        آشنایی قبلی با وال‌گلد
                    </th>
                    <th scope="col" class="px-6 py-3">
                        خرید قبلی در وال‌گلد
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تجربه خرید از وال‌گلد 
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        زمان آخرین تماس
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($list as $item)
                    <tr wire:key="item-{{ $item->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $item->id }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                           @if(is_null($item->user_id))
                                <button class="text-red-900" wire:click="assign({{ $item->id }})">تخصیص به من</button>
                           @else
                                {{ $item->user?->name }}
                           @endif
                        </th>
                        <td class="px-6 py-4">
                            <a class="text-blue-700" href="/campaigns/first/help?phone_number={{ $item->phone_number }}">تماس</a>
                        </td>
                        <td class="px-6 py-4">
                            {{ $item->phone_number }}
                        </td>

                        <td class="px-6 py-4">
                            <input wire:change="changeAdminId({{ $item->id }},$event.target.value)" value="{{ $item->admin_id }}" type="text" class="border-0 text-sm text-center">
                        </td>

                        <td class="px-6 py-4">
                            {{ $item->name }}
                        </td>
                        
                        <td class="px-6 py-4">
                            <select wire:change="changeStatus({{ $item->id }},$event.target.value)" class="border-0 w-56 text-sm">
                                <option hidden value="">انتخاب...</option>
                                    @foreach ($statuses as $status)
                                        <option {{ $item->call_status == $status ? 'selected' : '' }} value="{{ $status }}">{{$status }}</option>
                                    @endforeach
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <select wire:change="changeResult({{ $item->id }},$event.target.value)" class="border-0 w-64 text-sm">
                                <option hidden value="">انتخاب...</option>
                                    @foreach ($results as $result)
                                        <option {{ $item->call_result == $result ? 'selected' : '' }} value="{{ $result }}">{{$result }}</option>
                                    @endforeach
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <select wire:change="changeFamiliar({{ $item->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                <option hidden value="">انتخاب...</option>
                                <option {{ $item->familiar == '1' ? 'selected' : '' }} value="1">بله</option>
                                <option {{ $item->familiar == '0' ? 'selected' : '' }} value="0">خیر</option>
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <select wire:change="changeUsed({{ $item->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                <option hidden value="">انتخاب...</option>
                                <option {{ $item->used == '1' ? 'selected' : '' }} value="1">بله</option>
                                <option {{ $item->used == '0' ? 'selected' : '' }} value="0">خیر</option>
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <select wire:change="changeExperience({{ $item->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                <option hidden value="">انتخاب...</option>
                                <option {{ $item->experience == '1' ? 'selected' : '' }} value="1">مثبت</option>
                                <option {{ $item->experience == '0' ? 'selected' : '' }} value="0">منفی</option>
                            </select>
                        </td>

                        <td class="px-6 py-4">
                            <input wire:change="changeDescription({{ $item->id }},$event.target.value)" value="{{ $item->description }}" type="text" class="border-0 text-sm text-center w-96">
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $item->called_at ? verta($item->called_at) : '-' }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
   
</div>
