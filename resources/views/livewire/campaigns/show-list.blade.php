<div class="relative overflow-x-auto">
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
        </div>
    @endforeach

    <form wire:submit.prevent="add" class="mb-5">
        <input type="text" wire:model="name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sx rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="عنوان" required />
        <input type="text" wire:model="description" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="توضیحات" required />
        <input data-jdp wire:model="from" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تاریخ شروع" required />
        <input data-jdp wire:model="to" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تاریخ پایان" required />
        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد کمپین</button>
          
        </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500 text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نام کمپین
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات کمپین
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ شروع
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ پایان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($campaigns as $campaign)
                    <tr wire:key="campaign-{{ $campaign->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $campaign->id }}
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <input
                            wire:change="changeName({{ $campaign->id }},$event.target.value)"
                            class="text-sm border-0 text-center" type="text" value="{{ $campaign->name}}">
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <textarea
                            wire:change="changeDescription({{ $campaign->id }},$event.target.value)"
                            class="text-sm border-0" type="text">{{ $campaign->description }}</textarea>
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <input
                            wire:change="changeFrom({{ $campaign->id }},$event.target.value)"
                            data-jdp class="text-sm border-0 text-center" type="text" value="{{ verta($campaign->from)->format('Y/m/d')}}">
                        </th>
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <input
                            wire:change="changeTo({{ $campaign->id }},$event.target.value)"
                            data-jdp class="text-sm border-0 text-center" type="text" value="{{ $campaign->to ? verta($campaign->to)->format('Y/m/d') : ''}}">
                        </th>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <button wire:click="delete({{ $campaign->id }})" class="text-red-700 text-sm">حذف</button>
                        </th>
                        
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $campaigns->links() }}
    </div>
</div>
