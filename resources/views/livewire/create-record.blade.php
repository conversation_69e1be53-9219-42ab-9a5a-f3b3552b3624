<form wire:submit.prevent class="w-7/12 mx-auto">

    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach
        @if(!empty($chatTimeline))
            <div class="mb-6 border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto flex flex-col space-y-4">
                @forelse ($chatTimeline as $event)
                    @if(isset($event['event_type']) && $event['event_type'] === 'message')
                        <div class="flex @if($event['sender'] == 'operator') justify-end @else justify-start @endif">
                            <div class="relative max-w-xl px-4 py-2 rounded-lg shadow @if($event['sender'] == 'operator') bg-blue-600 text-white @else bg-gray-200 text-gray-900 @endif">
                                @if($event['sender'] == 'operator')
                                    <p class="text-xs font-bold text-blue-200 mb-1">{{ $event['user']['name'] ?? 'کارشناس' }}</p>
                                @endif
                                @if($event['content'] && (\Illuminate\Support\Str::endsWith(strtolower($event['content']), ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']) || \Illuminate\Support\Str::startsWith($event['content'], ['http://', 'https://']) && \Illuminate\Support\Str::contains($event['content'], ['goftino.com/files/'])))
                                    <a href="{{ $event['content'] }}" target="_blank"><img src="{{ $event['content'] }}" alt="تصویر پیوست" class="max-w-xs rounded-lg shadow-md"></a>
                                @else
                                    <span class="block whitespace-pre-wrap break-words">{{ $event['content'] }}</span>
                                @endif
                                <div class="text-xs @if($event['sender'] == 'operator') text-blue-200 @else text-gray-500 @endif mt-1 text-left">
                                    {{ verta($event['date'])->formatJalaliDatetime() }}
                                </div>
                            </div>
                        </div>

                    @elseif(isset($event['event_type']) && $event['event_type'] === 'assignment')
                        <div class="flex justify-center">
                            <div class="text-xs text-center text-gray-500 bg-gray-200 rounded-full px-4 py-1 shadow-sm">
                                چت به <strong>{{ $event['user']['name'] ?? 'اپراتور ناشناس' }}</strong> تخصیص داده شد
                                <span class="text-gray-400 mx-1">|</span>
                                <span>{{ verta(\Carbon\Carbon::parse($event['created_at'])->timezone('Asia/Tehran'))->formatJalaliDatetime() }}</span>
                            </div>
                        </div>
                    @endif

                @empty
                    <div class="text-center text-gray-500 py-4">هیچ پیام یا رویدادی برای این شناسه یافت نشد.</div>
                @endforelse
            </div>
        @endif
    <div class="grid gap-6 md:grid-cols-2 mb-6">
        <div>
            <label wire:click="inspect" for="identity_input" class="cursor-pointer block mb-2 text-sm font-medium text-gray-900 ">شناسه (استعلام)</label>
            <input {{ empty($identity) ?: 'disabled' }} wire:model.live="identity" type="text" id="identity_input" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="SCMTK8HM29" required />


            @if($identity && $incoming_type === 'chat' && $platform)
                <div class="mt-2">
                    @if($platform === 'wallgold')
                        <a href="https://my.goftino.com/app/chat_archive/684c030a2eb8556006cd945d/{{ $identity }}"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="font-medium text-sm text-blue-600 hover:text-blue-800 visited:text-purple-600">
                            لینک چت در گفتینو
                        </a>
                    @elseif($platform === 'wallex')
                        <a href="https://my.goftino.com/app/chat_archive/65ed7f885ad9665897338333/{{ $identity }}"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="font-medium text-sm text-blue-600 hover:text-blue-800 visited:text-purple-600">
                            لینک چت در گفتینو
                        </a>
                    @endif
                </div>
            @endif
        </div>
        <div>
            <label for="incoming_type" class="block mb-2 text-sm font-medium text-gray-900 ">ورودی</label>
            <select {{ !$registered ?: 'disabled' }} wire:model.lazy="incoming_type" id="incoming_type" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
                <option hidden>انتخاب...</option>
                <option value="chat">چت</option>
                <option value="call">تماس</option>
                <option value="kyc">احراز</option>
                <option value="outgoing">خروجی</option>

                <option value="faq">faq</option>
                <option value="email">ایمیل</option>
                <option value="ticket">تیکت</option>
              </select>
        </div>
        <div>
            <label for="incoming_date" class="block mb-2 text-sm font-medium text-gray-900 ">تاریخ مکالمه یا احراز</label>
            <input wire:model="incoming_date" data-jdp id="incoming_date" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>
        <div>
            <label for="agent_search" class="block mb-2 text-sm font-medium text-gray-900 ">کارشناس</label>
            <div
                x-data="{ open: false }"
                @keydown.escape.stop="open = false"
                @click.away="open = false"
                class="relative"
            >
                <input
                    id="agent_search"
                    type="text"
                    wire:model.live.debounce.300ms="agentSearch"
                    @focus="open = true"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    placeholder="جستجوی نام کارشناس..."
                    autocomplete="off"
                >
                <div
                    x-show="open && {{ count($users) > 0 }}"
                x-transition
                    style="display: none;"
                    class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
                >
                    @foreach ($users as $user)
                        <div
                            wire:click="selectAgent({{ $user->id }}, '{{ addslashes($user->name) }}')"
                            @click="open = false"
                            class="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                        >
                            {{ $user->name }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        <div>
            <label for="incoming_subject" class="block mb-2 text-sm font-medium text-gray-900 ">موضوع مکالمه</label>
            <input wire:model.lazy="incoming_subject" type="text" id="incoming_subject" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>
        <div>
            <label for="recorded_subject" class="block mb-2 text-sm font-medium text-gray-900 ">موضوع ثبت شده</label>
            <input wire:model.lazy="recorded_subject" type="text" id="recorded_subject" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>

        <div>
            <label for="crm_identity" class="block mb-2 text-sm font-medium text-gray-900 ">شناسه crm</label>
            <input wire:model.lazy="crm_identity" type="text" id="crm_identity" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>
        <div>
            <label for="coefficient" class="block mb-2 text-sm font-medium text-gray-900 ">ضریب اهمیت</label>
            <input disabled wire:model.lazy="coefficient" type="text" id="coefficient" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 " placeholder="" required />
        </div>

    </div>
    {{-- test --}}

    @if($qc_parameters)
    <div class="flex items-start mb-6">

        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        امتیاز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نمره
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($qc_parameters as $qc_parameter)
                    <tr wire:key="qc_parameters-{{ $qc_parameter->id }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $qc_parameter->name }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $qc_parameter->score }}
                        </td>
                        <td class="px-6 py-4">
                            @if($qc_parameter->id == 25)
                                {{-- عدم تاخیر در شروع مکالمه --}}
                                @php $frt = app('App\Tools\FRT')($identity,$users->where('id',$support_agent_id)->first()?->email) @endphp
                                @if ($frt === true)
                                    0
                                    {{ $this->changeScore($qc_parameter->id,0) }}
                                @elseif($frt === false)
                                    {{ $qc_parameter->score }}
                                    {{ $this->changeScore($qc_parameter->id,$qc_parameter->score) }}
                                @else
                                    <input wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" value="{{ $qc_parameter->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                                @endif
                            @elseif($qc_parameter->id == 26)
                                {{-- ارتباط موثر --}}
                                @php $called_by_name = app('App\Tools\User_called')($identity,$users->where('id',$support_agent_id)->first()?->email) @endphp
                                @if ($called_by_name == true)
                                    {{ $qc_parameter->score }}
                                    {{ $this->changeScore($qc_parameter->id,$qc_parameter->score) }}
                                @else
                                    <input wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" value="0" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                                @endif
                            @elseif($qc_parameter->id == 33)
                                {{-- مدیریت مکالمه --}}
                                @php $chat_management = app('App\Tools\Chat_management')($identity,$users->where('id',$support_agent_id)->first()?->email) @endphp
                                @if($chat_management == false)
                                    {{-- 0 --}}
                                    {{-- {{ $this->changeScore($qc_parameter->id,0) }} --}}
                                    <input wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" value="{{ $qc_parameter->score }}" type="number" class="bg-red-50 border border-red-300 text-red-900 text-sm rounded-lg focus:ring-red-500 focus:border-red-500 block w-full p-2.5  text-center" placeholder="" />
                                @else
                                    <input wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" value="{{ $qc_parameter->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                                @endif
                            @elseif(in_array($qc_parameter->id,[18,36]))
                                {{-- انتخاب موضوع صحیح --}}
                                @if ($incoming_subject == $recorded_subject)
                                    {{ $qc_parameter->score }}
                                    {{ $this->changeScore($qc_parameter->id,$qc_parameter->score) }}
                                @else
                                    0
                                    {{ $this->changeScore($qc_parameter->id,0) }}
                                @endif
                            @elseif($qc_parameter->id == 40)
                                {{-- سایر نیازها و سناریو نظرسنجی --}}
                                @php $survey = app('App\Tools\Survey')($identity,$users->where('id',$support_agent_id)->first()?->email) @endphp
                                @if ($survey == true)
                                    {{ $qc_parameter->score }}
                                    {{ $this->changeScore($qc_parameter->id,$qc_parameter->score) }}
                                @else
                                    <input wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" value="{{ $qc_parameter->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                                @endif
                            @elseif ($qc_parameter->id == 24)
                                {{-- رعایت سناریو ابتدایی --}}
                                @php $start_scenario = app('App\Tools\Start_scenario')($identity,$users->where('id',$support_agent_id)->first()?->email) @endphp
                                @if ($start_scenario == true)
                                    {{ $qc_parameter->score }}
                                    {{ $this->changeScore($qc_parameter->id,$qc_parameter->score) }}
                                @else
                                    <input wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" value="{{ $qc_parameter->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                                @endif
                            @else
                            <input wire:change="changeScore({{$qc_parameter->id}},$event.target.value)" min="0" max="{{ $qc_parameter->score }}" value="{{ $qc_parameter->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

    </div>
    @endif

    <div class="flex items-start mb-6">
        <textarea wire:model="message" id="message" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 " placeholder="توضیحات"></textarea>
    </div>

    <div class="flex items-start mb-6">
        <textarea wire:model="feedback" id="feedback" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 " placeholder="فیدبک"></textarea>
    </div>

    <div class="flex items-start mb-6">
        <textarea wire:model="daily_coach" id="daily_coach" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 " placeholder="کوچ روزانه"></textarea>
    </div>

    @if(in_array($incoming_type,['chat','call','outgoing']))
        <div class="flex items-start mb-6">
            <div class="flex items-center h-5">
            <input wire:model.lazy="red" id="red" type="checkbox" value="" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 "/>
            </div>
            <label for="red" class="ms-2 text-sm font-medium text-gray-900 ">خط قرمز</label>
        </div>
    @endif

    @if($red)
    <div class="flex items-start mb-6">

        <table class="w-full text-sm text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50  ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        امتیاز
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نمره
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($red_lines as $red_line)
                    <tr wire:key="parameters-{{ $red_line->id }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $red_line->name }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $red_line->score }}
                        </td>
                        <td class="px-6 py-4">
                            <input value="{{ $red_scores[$red_line->id] }}" wire:change="changeRedScore({{$red_line->id}},$event.target.value)" min="0" max="{{ $red_line->score }}" type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  text-center" placeholder="" />
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

    </div>
    @endif

    {{-- <div class="flex items-start mb-6">
        <div class="flex items-center h-5">
        <input wire:model="draft" id="draft" type="checkbox" value="" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 "/>
        </div>
        <label for="draft" class="ms-2 text-sm font-medium text-gray-900 ">ذخیره به عنوان پیش نویس</label>
    </div> --}}

    <div class="space-x-4 space-x-reverse mb-3">
        <label class="inline-flex items-center cursor-pointer">
            <input wire:model="challenging" type="checkbox" value="" class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600"></div>
            <span class="ms-3 text-sm font-medium text-gray-900 ">چالش برانگیز</span>
        </label>

        <label class="inline-flex items-center cursor-pointer">
            <input wire:model="ignore" type="checkbox" value="" class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600"></div>
            <span class="ms-3 text-sm font-medium text-gray-900 ">نادیده گرفتن</span>
        </label>
    </div>

    <button type="button" wire:click="save" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center ">ایجاد یا آپدیت رکورد</button>
    <button type="button" wire:click="next" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center ">بعدی</button>

</form>
