<div class="max-w-lg mx-auto">

    @foreach (['چت'=>'chat','تماس'=>'call'] as $key => $incoming_type)

        <table class="w-full text-sm text-gray-500  text-center mt-5">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        پارامتر {{ $key }}
                    </th>
                    <th scope="col" class="px-6 py-3">
                        میانگین نمره
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
        
            @foreach ($parameters->where('incoming_type',$incoming_type) as $parameter)
                    <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">
                    <td class="px-6 py-4">
                            {{ $parameter->name }}
                    </td>
                    <td class="px-6 py-4">
                        @php
                            $hundred = (isset($parameters_scores[$parameter->id]) and $parameters_scores[$parameter->id] == 100);
                        @endphp
                        <span class="{{ $hundred ? 'text-green-800' : 'text-red-800'}}">
                            {{ $parameters_scores[$parameter->id] ?? '-' }}
                        </span>
                    </td>

                    <td class="px-6 py-4 text-blue-900">
                        @if(!$hundred)
                            <a href="{{ route('show-records',['parameter_id'=>$parameter->id,'support_agent_id'=>$support_agent_id]) }}">جزئیات</a>
                        @else
                            -
                        @endif
                    </td>
                    </tr>
                    @endforeach
                
            </tbody>
        </table>

      @endforeach
  </div>
  
    