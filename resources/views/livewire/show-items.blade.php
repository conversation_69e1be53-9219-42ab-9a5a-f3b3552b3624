<div class="relative overflow-x-auto">
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach

    <form wire:submit.prevent="add" class="mb-5">
        <input type="text" wire:model="title" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="عنوان" required />
        <input type="text" wire:model="description" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="توضیحات" required />

          <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد آیتم</button>
    </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500 text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وضعیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($items as $item)
                    <tr wire:key="item-{{ $item->id }}" class="bg-white border-b ">
                        
                        <td class="px-6 py-4">
                            {{ $item->id }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $item->title }}
                            {{-- <input value="{{ $item->title }}" wire:change="changeTitle({{ $item->id }},$event.target.value)" type="text" class="border-0 text-center text-sm"> --}}
                        </td>
                        <td class="px-6 py-4">
                            {{ $item->description }}
                            {{-- <input value="{{ $item->description }}" wire:change="changeDescription({{ $item->id }},$event.target.value)" type="text" class="border-0 text-center text-sm"> --}}
                        </td>
                        <td class="px-6 py-4">
                            <select wire:change="changeStatus({{ $item->id }},$event.target.value)" class="border-0 w-40 text-sm">
                                <option {{ $item->status == false ? 'selected' : '' }} value="0">غیرفعال</option>
                                <option {{ $item->status == true ? 'selected' : '' }} value="1">فعال</option>
                        </select>
                        </td>
                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $item->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $items->links() }}
    </div>
</div>
