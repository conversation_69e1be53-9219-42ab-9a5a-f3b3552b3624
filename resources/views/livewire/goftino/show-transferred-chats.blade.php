
<div>

    <form class="mb-5 space-x-3 space-x-reverse">

       

        <select wire:model.lazy="from_operator_id" class="inline-block  w-52 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option value="0" disabled selected>کارشناس</option>
            @foreach ($agents as $agent)
                <option value="{{ $agent->goftino_operator_id }}">{{ $agent->name }}</option>
            @endforeach
        </select>

        <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 mt-3" placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 mt-3" placeholder="تا تاریخ" />

    </form>

    <div class="relative overflow-x-auto">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        چت‌آیدی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        پلتفرم
                    </th>
                    <th scope="col" class="px-6 py-3">
                        از اپراتور
                    </th>
                    <th scope="col" class="px-6 py-3">
                        به اپراتور
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($chats as $chat)
                    <tr class="bg-white border-b  border-gray-200">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $chat?->id }}
                        </th>
                        <td class="px-6 py-4">
                            
                            @if ($chat?->data?->platform=='wallex')
                                <a target="_blank" class="underline" href="https://my.goftino.com/app/chat_archive/65ed7f885ad9665897338333/{{ $chat?->chat_id }}">{{ $chat?->chat_id }}</a>
                            @else
                                <a target="_blank" class="underline" href="https://my.goftino.com/app/chat_archive/684c030a2eb8556006cd945d/{{ $chat?->chat_id }}">{{ $chat?->chat_id }}</a>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat?->data?->platform }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat->from_operator->name ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat->to_operator->name ?? '-' }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        <div class="mt-5">
            {{ $chats->links() }}
        </div>
    </div>
</div>