
<div>

    <form class="mb-5 space-x-3 space-x-reverse">

        <select wire:model.lazy="order" class="inline-block w-52 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option disabled selected>ترتیب</option>
            <option value="id_desc">آیدی نزولی (پیش فرض)</option>
            <option value="id_asc">آیدی صعودی</option>
            <option value="duration">مدت (بیشترین به کمترین)</option>
            <option value="queue">صف (بیشترین به کمترین)</option>
        </select>

        <select wire:model.lazy="user_id" class="inline-block  w-52 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option value="0" disabled selected>کارشناس</option>
            @foreach ($agents as $agent)
                <option value="{{ $agent->goftino_operator_id }}">{{ $agent->name }}</option>
            @endforeach
        </select>

        <select wire:model.lazy="rating" class="inline-block w-52 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option disabled value="0" selected>rating</option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
        </select>

        <select wire:model.lazy="platform" class="inline-block w-52 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option disabled value="0" selected>platform</option>
            <option value="wallex">wallex</option>
            <option value="wallgold">wallgold</option>
        </select>

        <select wire:model.lazy="shift_id" class="inline-block w-52 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option disabled value="0" selected>شیفت</option>
            <option value="1">صبح</option>
            <option value="2">عصر</option>
            <option value="3">شب</option>
        </select>
        <br />
        <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 mt-3" placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 mt-3" placeholder="تا تاریخ" />

    </form>

    <div class="mb-5">
        <div class="text-green-700">count : {{ $data['count'] }}</div>

        <div class="text-red-700">max duration : {{ $data['max_duration'] }}</div>
        <div class="text-red-700">average duration : {{ $data['average_duration'] }}</div>

        <div class="text-blue-700">max queue : {{ $data['max_queue'] }}</div>
        <div class="text-blue-700">average queue : {{ $data['average_queue'] }}</div>

    </div>

    <div class="relative overflow-x-auto">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        چت‌آیدی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        پلتفرم
                    </th>
                    <th scope="col" class="px-6 py-3">
                        اپراتور
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نظرسنجی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        مدت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        صف
                    </th>
                    <th scope="col" class="px-6 py-3">
                        زمان تخصیص
                    </th>
                    <th scope="col" class="px-6 py-3">
                        زمان اولین پیام کاربر
                    </th>
                    <th scope="col" class="px-6 py-3">
                        زمان اولین پاسخ کارشناس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        زمان آخرین پیام
                    </th>
                    <th scope="col" class="px-6 py-3">
                        زمان بسته‌شدن
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد پیام‌ها
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تعداد اپراتورها
                    </th>
                    <th scope="col" class="px-6 py-3">
                        آیدی کارشناس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        آیدی کاربر
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($chats as $chat)
                    <tr class="bg-white border-b  border-gray-200">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $chat?->id }}
                        </th>
                        <td class="px-6 py-4">
                            
                            @if ($chat?->data?->platform=='wallex')
                                <a target="_blank" class="underline" href="https://my.goftino.com/app/chat_archive/65ed7f885ad9665897338333/{{ $chat?->chat_id }}">{{ $chat?->chat_id }}</a>
                            @else
                                <a target="_blank" class="underline" href="https://my.goftino.com/app/chat_archive/684c030a2eb8556006cd945d/{{ $chat?->chat_id }}">{{ $chat?->chat_id }}</a>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat?->data?->platform }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat->user->name ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat->rating ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat?->duration ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat?->queue ?? '-' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $chat?->assignment_date ? verta($chat?->assignment_date) : '-' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $chat?->first_message_datetime ? verta($chat?->first_message_datetime) : '-' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $chat?->first_response_datetime ? verta($chat?->first_response_datetime) : '-' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $chat?->last_message_datetime ? verta($chat?->last_message_datetime) : '-' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $chat?->created_at ? verta($chat?->created_at) : '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat?->messages_count ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat?->operators_count ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat->operator_id ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $chat?->data?->user_id }}
                        </td>
                        <td class="px-6 py-4">
                            <a class="text-red-700" href="#">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        <div class="mt-5">
            {{ $chats->links() }}
        </div>
    </div>
</div>