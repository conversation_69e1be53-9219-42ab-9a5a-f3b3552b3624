<div class="relative overflow-x-auto">
    
    <div class="mb-5">
        {{-- <input wire:model.lazy="from_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="از تاریخ" />
        <input wire:model.lazy="to_date" data-jdp type="text" readonly id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="تا تاریخ" /> --}}
        
        <select wire:model.lazy="period_id" class="bg-gray-50 w-72 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option selected hidden>انتخاب دوره</option>
            <option value="0">همه</option>
            @foreach ($periods as $period)
                <option value="{{ $period->id }}">
                    از
                    {{ verta($period->from)->formatDate() }}
                    تا
                    {{ verta($period->to)->formatDate() }}
                </option>
            @endforeach
        </select>

        <select wire:model.lazy="subject" class="bg-gray-50 w-80 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option selected hidden>فیلتر موضوع</option>
            <option value="0">همه</option>
                <option value="انتخاب موضوع اشتباه">
                    انتخاب موضوع اشتباه
                </option>

                <option value="انتخاب کاربر اشتباه">
                    انتخاب کاربر اشتباه	
                </option>

                <option value="قابل پیگیری تکراری">
                    قابل پیگیری تکراری	
                </option>

                <option value="تماس خروجی جهت تکمیل اطلاعات">
                    تماس خروجی جهت تکمیل اطلاعات
                </option>

                <option value='انتخاب گزینه ی "ارجاع جهت پیگیری" به اشتباه'>
                    انتخاب گزینه ی "ارجاع جهت پیگیری" به اشتباه	
                </option>
                
        </select>

        <select wire:model.lazy="support_agent_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-56 p-2.5 ">
            <option selected hidden>کارشناس پشتیبانی</option>
            <option value="0">همه</option>
            @foreach ($support_agents as $support_agent)
                <option value="{{ $support_agent->id }}">{{ $support_agent->name }}</option>
            @endforeach
        </select>

        <select wire:model.lazy="supervisor_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-56 p-2.5 ">
            <option selected hidden>سوپروایزر</option>
            <option value="0">همه</option>
            @foreach ($supervisors as $supervisor)
                <option value="{{ $supervisor->id }}">{{ $supervisor->name }}</option>
            @endforeach
        </select>

        @if(auth()->user()->role_id > 1)
        <select wire:model.lazy="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-36 p-2.5 ">
            <option selected hidden>وضعیت</option>
            <option value="">همه</option>
            <option value="1">تایید</option>
            <option value="0">عدم تایید</option>
            <option value="null">در انتظار</option>
        </select>
        @endif
    
        <span wire:loading>
            در حال پردازش...
        </span>

    </div>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        آیدی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شناسه پارس لاجیک
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کارشناس پشتیبانی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        موضوع
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ ثبت تیکت
                    </th>
                    @if(auth()->user()->role_id>1)
                    <th scope="col" class="px-6 py-3">
                        وضعیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        سوپروایزر
                    </th>
                    @endif
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($tickets as $ticket)
                    <tr wire:key="ticket-{{ $ticket->id }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $ticket->id }}
                        </th>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $ticket->crm_id }}
                        </th>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $ticket->user->name }}
                        </th>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $ticket->subject }}
                        </th>
                        
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ verta($ticket->creation_date) }}
                        </th>

                        @if(auth()->user()->role_id > 1)
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            @if(is_null($ticket->status))
                            <select class="w-36" wire:change="change({{ $ticket->id }},$event.target.value)">
                                <option hidden selected>انتخاب...</option>
                                <option value="1">تایید</option>
                                <option value="0">عدم تایید</option>
                            </select>
                            @else
                            {{ $ticket->status ? 'تایید' : 'عدم تایید' }}
                            @endif
                        </th>
                        @endif

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $ticket?->supervisor?->name }}
                        </th>

                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            <a wire:click="delete({{ $ticket->id }})" href="#" class="font-medium text-red-600 hover:underline mx-2">حذف</a>
                        </th>
                        
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-4" dir="ltr">
        {{ $tickets->links() }}
    </div>
</div>
