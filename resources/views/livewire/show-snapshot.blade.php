<div>
    <div class="text-red-800 text-center mb-5">
        تاریخ اسنپ شات: 
        <span dir="rtl">
            {{ verta($snapshot->created_at) }}
            (از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }})
        </span>
    </div>
    <table class="w-full text-sm text-center text-gray-500 ">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50  ">
            <tr>
                <th scope="col" class="px-6 py-3">
                    کارشناس
                </th>
                @foreach (($snapshot->supervisor_parameters ?? []) as $parameter_id => $parameter_name)
                    <th scope="col" class="py-3">
                        {{$parameter_name }}
                    </th>
                @endforeach
                <th scope="col" class="px-6 py-3">
                    نمره کل سوپروایزر
                </th>

                <th scope="col" class="px-6 py-3">
                    نمره کل چت
                </th>
                <th scope="col" class="px-6 py-3">
                    تعداد کل چت
                </th>
                <th scope="col" class="px-6 py-3">
                    نمره کل تماس
                </th>
                <th scope="col" class="px-6 py-3">
                    تعداد کل تماس
                </th>
                <th scope="col" class="px-6 py-3">
                    نمره کل احراز
                </th>
                <th scope="col" class="px-6 py-3">
                    تعداد کل احراز
                </th>
                <th scope="col" class="px-6 py-3">
                    نمره کل خطوط قرمز
                </th>

                <th scope="col" class="px-6 py-3">
                    نمره کل qc
                    (بدون احتساب خط قرمز)
                </th>
                <th scope="col" class="px-6 py-3">
                    نمره کل qc
                    (با احتساب خط قرمز)
                </th>

                <th scope="col" class="px-6 py-3">
                    نمره کل کارشناس
                </th>
            </tr>
        </thead>
        <tbody>
                <tr class="bg-gray-100 border-b ">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        حداکثر امتیاز پارامترها
                    </th>
                @foreach (($snapshot->supervisor_parameters_scores ?? []) as $parameter_score )
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $parameter_score }}
                    </th>
                @endforeach
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    -
                </th>
            </tr>
            @foreach ($snapshot->users as $user_id => $user)
            @if(auth()->user()->role_id > 1 or auth()->user()->id == $user_id)
            <tr wire:key="user-{{ $user_id }}" class="bg-white border-b ">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $user->name }}
                    </th>
                @foreach (($user->supervisor_parameters ?? []) as $parameter_key => $parameter_value)
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $parameter_value }}
                    </th>
                @endforeach
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->supervisor_score }}
                </th>

                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->chat_score }}
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->chat_count }}
                </th>

                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->call_score }}
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->call_count }}
                </th>

                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->kyc_score }}
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->kyc_count }}
                </th>

                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->redline_score }}
                </th>

                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->qc_score }}
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->final_qc_score }}
                </th>
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                    {{ $user->final_score }}
                </th>
            </tr>
            @endif
            @endforeach
            
        </tbody>
    </table>
</div>

