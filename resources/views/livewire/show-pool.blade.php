<div>
    <form x-data="{open: false}" wire:submit.prevent>
        <button wire:click="assign" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تخصیص همه</button>
        <button wire:click="truncate" wire:confirm="Are you sure you want to delete them all?" type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">حذف همه</button>
        
        <select wire:model.lazy="platform" class="border-0 w-52 text-sm">
            <option value="wallex">wallex</option>
            <option value="wallgold">wallgold</option>
        </select>

        <select wire:model.lazy="incoming_type" class="border-0 w-52 text-sm">
            <option value="0">ورودی...</option>
            <option value="chat">چت</option>
            <option value="call">تماس</option>
            <option value="kyc">احراز</option>
            <option value="outgoing">خروجی</option>
        </select>

        <select wire:model.lazy="filter" class="border-0 w-52 text-sm">
            <option value="0">کارشناس qc...</option>
            @foreach ($qc_agents as $qc_agent)
                <option value="{{ $qc_agent->id }}">{{ $qc_agent->name }}</option>
            @endforeach
        </select>

        <svg :class="open && 'rotate-90'" x-on:click="open = ! open" class="inline w-6 h-6 text-gray-800 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 5.326 5.7a.909.909 0 0 0 1.348 0L13 1"/>
        </svg>
        <div x-show="open" x-cloak>
                <button wire:click="assignChats" class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تخصیص چت ها</button>
                <button wire:click="assignCalls" class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تخصیص تماس ها</button>
                <button wire:click="assignKyc" class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تخصیص احراز ها</button>
                <button wire:click="assignOutgoing" class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تخصیص خروجی ها</button>
                <br>
                <button wire:click="deleteChats" wire:confirm="Are you sure you want to delete them all?" type="button" class="focus:outline-none text-white bg-pink-700 hover:bg-pink-800 focus:ring-4 focus:ring-pink-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">حذف چت ها</button>
                <button wire:click="deleteCalls" wire:confirm="Are you sure you want to delete them all?" type="button" class="focus:outline-none text-white bg-pink-700 hover:bg-pink-800 focus:ring-4 focus:ring-pink-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">حذف تماس ها</button>
                <button wire:click="deleteKyc" wire:confirm="Are you sure you want to delete them all?" type="button" class="focus:outline-none text-white bg-pink-700 hover:bg-pink-800 focus:ring-4 focus:ring-pink-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">حذف احراز ها</button>    
                <button wire:click="deleteOutgoing" wire:confirm="Are you sure you want to delete them all?" type="button" class="focus:outline-none text-white bg-pink-700 hover:bg-pink-800 focus:ring-4 focus:ring-pink-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">حذف خروجی ها</button>    
                <br>
                <span>بدون اقدام کننده : {{ $data->whereNull('qc_agent_id')->count() }}</span>
                <br>
            @foreach ($qc_agents as $qc_agent)
                <span>{{ $qc_agent->name }} : {{ $data->where('qc_agent_id',$qc_agent->id)->count() }}</span>
                <br>
            @endforeach
        </div>
    </form>
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        آیدی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        ورودی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        كارشناس پشتیبانی
                    </th>
                    <th scope="col" class="px-6 py-3">
                        كارشناس qc
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ
                    </th>
                    <th scope="col" class="px-6 py-3">
                        note
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($data as $datum)
                    <tr wire:key="record-{{ $datum->identity }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $datum->id }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $datum->identity }}
                        </td>
                        <td class="px-6 py-4">
                            @if($datum->incoming_type=='chat')
                                چت
                            @elseif($datum->incoming_type=='call')
                                ورودی
                            @elseif($datum->incoming_type=='kyc')
                                احراز
                            @elseif($datum->incoming_type=='outgoing')
                                خروجی
                            @else
                                -
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            {{ $datum->support_agent?->name ?? '-' }}
                        </td>
                        <td class="px-6 py-4">
                            @if(auth()->user()->role_id>2)
                                <select wire:change="changeAgent({{ $datum->id }},$event.target.value)" class="border-0 w-52 text-sm">
                                    <option value="0">انتخاب...</option>
                                    @foreach ($qc_agents as $qc_agent)
                                        <option {{ $datum->qc_agent?->id == $qc_agent->id ? 'selected' : '' }} value="{{ $qc_agent->id }}">{{ $qc_agent->name }}</option>
                                    @endforeach
                                </select>
                            @else
                                @if($datum->qc_agent)
                                {{ $datum->qc_agent->name }}
                                @else
                                    <button wire:click="me({{ $datum->id }})" type="button" class="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">تخصیص به من</button>
                                @endif
                            @endif
                            

                        </td>
                        <td class="px-6 py-4">
                            {{ verta($datum->incoming_date)->format('Y/m/d H:i') }}
                        </td>
                        <td class="px-6 py-4">
                            {!! $datum->note ?? '-' !!}
                        </td>
                        <td class="px-6 py-4">
                            @php
                                $list = [
                                    'identity' => urlencode($datum->identity),
                                    'incoming_type' => $datum->incoming_type,
                                    'incoming_date'=>verta($datum->incoming_date)->format('Y/m/d'),
                                ];
                                if($datum->support_agent?->id)
                                {
                                    $list['support_agent_id'] = $datum->support_agent?->id;
                                }
                                $list['message'] = $datum->incoming_type=='kyc' ? $datum->note . "\nTime: " . verta($datum->incoming_date)->format('H:i:s') : $datum->note;

                            @endphp
                            <a href="{{ route('create-record',$list) }}" class="font-medium text-blue-600  hover:underline mx-2">کنترل</a>
                            <a wire:click="delete({{ $datum->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    {{-- <div class="mt-4" dir="ltr">ver
        {{ $data->links() }}
    </div> --}}
</div>