<div>
    <form wire:submit.prevent="analyze" class="mx-auto">
        <div class="mb-5">
            <select wire:model.lazy="period_id" id="period_id" class="inline-block w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                <option hidden selected value="">انتخاب دوره...</option>
                @foreach ($periods as $period)
                <option value="{{ $period->id }}">
                    از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
                </option>
                @endforeach
            </select>
            
            <div wire:loading class="text-red-900">
                در حال پردازش...
            </div>
        </div>
    </form>

    @if($period_id)

    <div wire:key="period-{{ $period_id }}" class="sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        کارشناس
                    </th>

                    <th scope="col" class="px-6 py-3">
                        نمره کل دوره
                    </th>
                    
                </tr>
            </thead>

            <tbody>
                @foreach ($users as $user)
                        <tr wire:key="user-{{ $user->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                            
                            <td class="py-4">
                                {{ $user->name }}
                            </td>
                            
                            <td class="py-4">
                                <input
                                min="0"
                                max="100"
                                 type="number" 
                                 step="1" 
                                 class="text-center" 
                                 value="{{ $exam_stats->where('user_id',$user->id)->first()?->value }}"
                                 wire:change="change({{ $user->id }},$event.target.value)"
                                 >
                            </td>
                        </tr>
                    @endforeach
            </tbody>
        </table>
    </div>

    @endif  
</div>
