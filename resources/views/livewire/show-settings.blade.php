<div>
    @foreach ($errors->all() as $error)
            <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
                {{$error}}
          </div>
        @endforeach
          
        <form wire:submit.prevent="add" class="mb-5">
        <input type="text" wire:model="name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="نام" required />
        
        <input type="text" wire:model="value" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="مقدار" required />
        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد</button>
        </form>

    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        نام
                    </th>
                    <th scope="col" class="px-6 py-3">
                        مقدار
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($settings as $setting)
                    <tr wire:key="parameter-{{ $setting->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $setting->id }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $setting->name }}
                        </td>
                        
                        <td class="px-6 py-4">
                            <input wire:change="change_value({{ $setting->id }},$event.target.value)" value="{{ $setting->value }}" type="text" class="border-0 text-sm text-center">
                        </td>

                        <td class="px-6 py-4">
                            @if(auth()->user()->role_id == 6)
                            <input wire:change="change_note({{ $setting->id }},$event.target.value)" value="{{ $setting->note }}" type="text" class="border-0 text-sm text-center">
                            @else
                                {{ $setting->note }}
                            @endif
                        </td>
    
                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $setting->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
