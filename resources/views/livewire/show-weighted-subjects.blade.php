<div class="relative overflow-x-auto">

    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
      </div>
    @endforeach

    <form wire:submit.prevent="add" class="mb-5">
    <input type="text" wire:model="subject" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="عنوان" required />
    <input type="number" min="1" wire:model="weight" step="1" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 " placeholder="وزن" required />
    
      <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">ایجاد پارامتر</button>
     
    </form>
    
    <div class="shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-gray-500  text-center">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عنوان
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وزن
                    </th>
                    <th scope="col" class="px-6 py-3">
                        وضعیت
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($weighted_subjects as $weighted_subject)
                    <tr wire:key="parameter-{{ $weighted_subject->id }}" class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $weighted_subject->id }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $weighted_subject->subject }}
                        </td>
                        
                        <td class="px-6 py-4">
                            {{ $weighted_subject->weight }}
                        </td>
                        <td class="px-6 py-4">
                            <select wire:change="toggle({{ $weighted_subject->id }},$event.target.value)" class="w-32 border-0">
                                <option {{ $weighted_subject->status ? 'selected' : '' }} value="1">فعال</option>
                                <option {{ !$weighted_subject->status ? 'selected' : '' }} value="0">غیرفعال</option>
                            </select>
                        </td>
                        <td class="px-6 py-4">
                            <a wire:click="delete({{ $weighted_subject->id }})" href="#" class="font-medium text-red-600  hover:underline mx-2">حذف</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    
</div>
