<div>
    @foreach ($errors->all() as $error)
        <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
            {{$error}}
        </div>
    @endforeach

    <div class="mb-5">
        <select wire:model.lazy="period_id" id="period_id" class="inline-block w-96 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
            <option hidden selected value="">انتخاب دوره...</option>
            @foreach ($periods as $period)
            <option value="{{ $period->id }}">
                از {{ verta($period->from)->format('Y/m/d') }} تا {{ verta($period->to)->format('Y/m/d') }}
            </option>
            @endforeach
        </select>
        
        <div wire:loading class="text-red-900">
            در حال پردازش...
        </div>
    </div>

    <table class="w-full text-sm text-gray-500  text-center">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
            <tr>
                <th scope="col" class="px-6 py-3">
                    کارشناس
                </th>
                <th scope="col" class="px-6 py-3">
                    تعداد رکورد وزن یک
                </th>
                <th scope="col" class="px-6 py-3">
                    تعداد رکورد وزن بیشتر از یک
                </th>
                <th scope="col" class="px-6 py-3">
                    نسبت (درصد)
                </th>
            </tr>
        </thead>
        <tbody>
    
            @foreach ($users as $user)
                <tr wire:key="{{ $period_id }}{{ $user->id }}" class="bg-white border-b  hover:bg-gray-50 ">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                        {{ $user->name }}
                    </th>
                    <td class="px-6 py-4">
                          {{ $user->weighted_one }}
                    </td>

                    <td class="px-6 py-4">
                        {{ $user->weighted_more }}
                  </td>

                  <td class="px-6 py-4">
                    {{ round($user->weighted_more/$user->weighted_one*100,2) }}
              </td>
    
                </tr>
            @endforeach
            
        </tbody>
    
      </table>
</div>
