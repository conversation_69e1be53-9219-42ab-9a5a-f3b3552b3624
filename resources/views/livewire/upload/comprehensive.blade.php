
<form wire:submit.prevent="save">
    <div
    x-data="{ isUploading: false, progress: 0 }"
    x-on:livewire-upload-start="isUploading = true"
    x-on:livewire-upload-finish="isUploading = false"
    x-on:livewire-upload-error="isUploading = false"
    x-on:livewire-upload-progress="progress = $event.detail.progress"
    >
    
    @foreach ($errors->all() as $error)
    <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
        {{$error}}
    </div>
    @endforeach

    <select wire:model="period_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-60 inline-block p-2.5 ">
        <option hidden>انتخاب...</option>
        @foreach ($periods as $period)
            <option value="{{ $period->id }}">
                از
                {{ verta($period->from)->format('Y/m/d') }}
                تا
                {{ verta($period->to)->format('Y/m/d') }}
            </option>
        @endforeach
      </select>

    <input wire:model="excel" class="w-1/2 mb-5 text-xs text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 " id="small_size" type="file">
    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 ">آپلود اکسل جامع (پاپ آپ باز)</button>
    
    <div x-show="isUploading">
        <progress max="100" x-bind:value="progress"></progress>
    </div>
    <div wire:loading>
        Processing...
    </div>
</form>