<div>
    @foreach ($errors->all() as $error)
    <div dir="ltr" class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 " role="alert">
        {{$error}}
    </div>
    @endforeach
    <form>
        <div class="mb-5">
            <input wire:model="note" placeholder="توضیحات" type="text" id="base-input" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 inline-block w-96 p-2.5 ">

            <select wire:model.lazy="user_id" class="w-52">
                <option selected>انتخاب کارشناس (همه)<option>
                @foreach ($support_agents as $support_agent)
                    <option value="{{ $support_agent->id }}">{{ $support_agent->name }}</option>
                @endforeach

            </select>

            <button wire:click="add" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2  focus:outline-none ">ثبت</button>
        </div>
    </form>

    <div class="relative overflow-x-auto">
        <table class="w-full text-sm text-center rtl:text-center text-gray-500 ">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        شناسه
                    </th>
                    <th scope="col" class="px-6 py-3">
                        کارشناس
                    </th>
                    <th scope="col" class="px-6 py-3">
                        توضیحات
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تایید سوپروایزر
                    </th>
                    <th scope="col" class="px-6 py-3">
                        تاریخ ایجاد
                    </th>
                    <th scope="col" class="px-6 py-3">
                        عملیات
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($exceptions as $exception)
                    <tr class="bg-white border-b ">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap ">
                            {{ $exception->id }}
                        </th>
                        <td class="px-6 py-4">
                            {{ $exception->user->name ?? 'همه' }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $exception->note }}
                        </td>
                        <td class="px-6 py-4">
                            @if($exception->verified == 'accepted')
                                تایید
                            @elseif($exception->verified == 'rejected')
                                رد
                            @else
                            <select wire:change="changeStatus({{ $exception->id }},$event.target.value)" class="w-72 text-center border-0 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
                                <option hidden>انتخاب...</option>
                                <option value="accepted">تایید</option>
                                <option value="rejected">رد</option>
                            </select>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            {{ verta($exception->created_at) }}
                        </td>
                        <td class="px-6 py-4">
                            <a href="#" class="text-red-600" wire:click="delete({{ $exception->id }})">حذف</a>
                        </td>
                    </tr>
                @endforeach
                
            </tbody>
        </table>
    </div>

</div>
