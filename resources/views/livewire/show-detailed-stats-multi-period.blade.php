<div class="max-w-lg mx-auto">

    <select wire:model.lazy="supervisor_id" id="supervisor_id" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
        <option hidden value="">انتخاب سوپروایزر...</option>
        <option value="0">همه سوپروایزرها</option>
        @foreach ($supervisors as $supervisor)
        <option value="{{ $supervisor->id }}">
            {{ $supervisor->name }}
        </option>
        @endforeach
    </select>

    <select wire:model.lazy="agent_id" id="agent_id" class="my-3 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
        <option hidden value="">انتخاب کارشناس...</option>
        <option value="0">همه کارشناسان</option>
        @foreach ($agents as $agent)
        <option value="{{ $agent->id }}">
            {{ $agent->name }}
        </option>
        @endforeach
    </select>

    <table class="w-full text-sm text-gray-500  text-center mt-5">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
            <tr>
                <th scope="col" class="px-6 py-3">
                    پارامتر سوپروایزر
                </th>
                <th scope="col" class="px-6 py-3">
                    میانگین نمره
                </th>
            </tr>
        </thead>
        <tbody>
    
        @foreach ($supervisor_parameters as $supervisor_parameter)
                <tr wire:key="{{ now() }}" class="bg-white border-b  hover:bg-gray-50 ">
                <td class="px-6 py-4">
                        {{ $supervisor_parameter->name }}
                </td>
                <td class="px-6 py-4">
                    <span>
                        @php
                        $value = round($supervisor_parameter->supervisor_scores->avg('value') / $supervisor_parameter->score * 100,2) ?? '-';
                        @endphp
                        <span class="{{ $value == 100 ? 'text-green-800' : 'text-red-800'}} }}" >{{ $value }}</span>
                        
                    </span>
                </td>
                </tr>
                @endforeach
            
        </tbody>
    </table>

    @foreach (['چت'=>'chat','تماس'=>'call'] as $key => $incoming_type)

        <table class="w-full text-sm text-gray-500  text-center mt-5">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        پارامتر {{ $key }}
                    </th>
                    <th scope="col" class="px-6 py-3">
                        میانگین نمره
                    </th>
                </tr>
            </thead>
            <tbody>
        
            @foreach ($parameters->where('incoming_type',$incoming_type) as $parameter)
                    <tr wire:key="{{ now() }}" class="bg-white border-b hover:bg-gray-50 ">
                    <td class="px-6 py-4">
                            {{ $parameter->name }}
                    </td>
                    <td class="px-6 py-4">
                        <span>
                            @php
                            $value = round($parameter->records->avg('pivot.value') / $parameter->score * 100,2) ?? '-';
                            @endphp
                            <span class="{{ $value == 100 ? 'text-green-800' : 'text-red-800'}} }}" >{{ $value }}</span>
                            
                        </span>
                    </td>
                    </tr>
                    @endforeach
                
            </tbody>
        </table>

      @endforeach
  </div>