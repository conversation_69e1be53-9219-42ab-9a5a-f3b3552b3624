<div>
    <form class="max-w-sm mx-auto">
  
      <div class="mb-5">
        
          <select wire:model.lazy="period_id" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
              <option hidden selected value="">انتخاب دوره...</option>
                  @foreach ($periods as $period)
                      <option value="{{ $period->id }}">از {{ verta($period->from)->formatDate() }} تا {{ verta($period->to)->formatDate() }}</option>
                  @endforeach
            </select>

        </div>

        <div class="mb-5">
        
          <select wire:model.lazy="platform" class="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ">
              <option hidden selected value="">انتخاب دوره...</option>
                      <option value="wallex">والکس</option>
                      <option value="wallgold">وال‌گلد</option>
            </select>

        </div>
      
    </form>
    
    <table class="max-w-sm mx-auto w-full text-sm text-gray-500  text-center">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 ">
          <tr>
              <th scope="col" class="px-6 py-3">
                  رتبه
              </th>
              <th scope="col" class="px-6 py-3">
                  سوپروایزر
              </th>
              @if(auth()->user()->role_id>=4)
                <th scope="col" class="px-6 py-3">
                    نمره کل faq
                </th>
                <th scope="col" class="px-6 py-3">
                    نمره کل email
                </th>
                <th scope="col" class="px-6 py-3">
                    نمره کل ticket
                </th>
            @endif
            <th scope="col" class="px-6 py-3">
                نمره کل رکوردها
            </th>
              
          </tr>
      </thead>
      <tbody>
  
          @foreach ($supervisors_score as $supervisor_score)
            @if(!($loop->index >= 3 and auth()->user()->id !== $supervisor_score['id'] and auth()->user()->role_id<4 ))
                <tr wire:key="{{ now() }}" class="bg-white border-b ">
                    <td class="px-6 py-4">
                            {{$loop->index + 1}}
                    </td>
                    
                    <td class="px-6 py-4 whitespace-nowrap">
                        {{ $supervisor_score['name'] }}
                        <span class="text-2xl">
                            @switch ($loop->index + 1)
                            @case(1)
                            🥇
                            @break
                            @case(2)
                            🥈
                            @break
                            @case(3)
                            🥉
                            @break
                            @default
                            @endswitch
                        </span>
                    </td>
                    @if(auth()->user()->role_id>=4)
                        <td class="px-6 py-4">
                            {{ $supervisor_score['faq'] }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $supervisor_score['email'] }}
                        </td>
                        <td class="px-6 py-4">
                            {{ $supervisor_score['ticket'] }}
                        </td>
                    @endif
                    <td class="px-6 py-4">
                        {{ $supervisor_score['average'] }}
                    </td>
                </tr>
              @endif
          @endforeach
          
      </tbody>
  
    </table>
  
  </div>
  
    