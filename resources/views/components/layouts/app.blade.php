<!DOCTYPE html>
<html dir="rtl" lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <title>{{ config('app.name', 'Laravel') }}</title>
        <link href="{{ asset('css/flowbite.min.css') }}"  rel="stylesheet" />
        {{-- <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.css"  rel="stylesheet" /> --}}

        {{-- <link rel="stylesheet" href="https://raw.githubusercontent.com/majidh1/JalaliDatePicker/refs/heads/main/dist/jalalidatepicker.min.css"> --}}
        {{-- <link rel="stylesheet" href="https://unpkg.com/@majidh1/jalalidatepicker/dist/jalalidatepicker.min.css"> --}}
        <link rel="stylesheet" href="{{ asset('css/jalalidatepicker.min.css') }}">

        @vite(['resources/css/app.css','resources/css/font.css', 'resources/js/alerts.js'])
        @stack('scripts')
        @livewireStyles
    </head>
    <body style="font-family:Vazir">
        @include('components.layouts.navbar')
        <hr>
        <div class="container mx-auto mt-5 mb-5">
            {{ $slot }}
        </div>
        <script src="{{ asset('js/flowbite.min.js') }}"></script>
        {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script> --}}

        {{-- <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script> --}}
        <script src="{{ asset('js/chart.js') }}" defer></script>
        {{-- <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script> --}}
        
        {{-- <script type="text/javascript" src="https://raw.githubusercontent.com/majidh1/JalaliDatePicker/refs/heads/main/dist/jalalidatepicker.min.js"></script> --}}
        {{-- <script type="text/javascript" src="https://unpkg.com/@majidh1/jalalidatepicker/dist/jalalidatepicker.min.js"></script> --}}
        <script type="text/javascript" src="{{ asset('js/jalalidatepicker.min.js') }}"></script>

        {{-- <script defer src="https://cloud.umami.is/script.js" data-website-id="66158d9a-d328-4aae-838b-79840aef2211"></script> --}}

        {{-- <script type="text/javascript" src="../jalalidatepicker.min.js"></script> --}}
        <script>
            // window.Chart = Chart
            jalaliDatepicker.startWatch();

            window.addEventListener('initFlowbite', event => {
                // pagination
                setTimeout(initFlowbite, 1000);
            })

            
            // document.addEventListener('DOMContentLoaded', initFlowbite);
            // document.addEventListener('livewire:load', initFlowbite);
            // document.addEventListener('livewire:update', initFlowbite);
        </script>
        @livewireScripts
    </body>
</html>
