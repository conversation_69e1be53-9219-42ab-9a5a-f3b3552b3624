

<nav class="bg-white border-gray-200 ">
    <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
      <a href="#" class="flex items-center space-x-3 rtl:space-x-reverse">
          <img src="https://positions.wallex.support/wallex.png" class="h-8" alt="Wallex Logo" />
          <span class="self-center text-2xl font-semibold whitespace-nowrap ">والکس QC</span>
      </a>
      <button data-collapse-toggle="navbar-dropdown" type="button" class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 " aria-controls="navbar-dropdown" aria-expanded="false">
          <span class="sr-only">Open main menu</span>
          <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
          </svg>
      </button>
      <div class="hidden w-full md:block md:w-auto" id="navbar-dropdown">
        <ul class="flex flex-col font-medium p-4 md:p-0 mt-4 border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white">
            
          <li>
            <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar12" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto">
              نمرات
              <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
              </svg>
          </button>
            <!-- Dropdown menu -->
            <div id="dropdownNavbar12" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
                <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">
  
                  <li>
                    <a href="{{ route('show-scores') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمرات سوپروایزر</a>
                  </li>

                  @if(auth()->user()->role_id>1)
                    <li>
                      <a href="{{ route('qc-supervisors-show-scores') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمرات سرپرست (سوپروایزر)</a>
                    </li>

                    <li>
                      <a href="{{ route('qc-qc-show-scores') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمرات سرپرست (qc)</a>
                    </li>
                  @endif

                </ul>
            </div>
        </li>


            <li>
                <a href="{{ route('show-records') }}" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 ">رکوردها</a>
            </li>
            
            <li>
              <a href="{{ route('show-objects') }}" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 ">اعتراضات</a>
          </li>


          <li>
            <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar10" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto">
              گزارشات
              <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
              </svg>
          </button>
            <!-- Dropdown menu -->
            <div id="dropdownNavbar10" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
                <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">
  
                  <li>
                    <a href="{{ route('show-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش عملکرد کارشناسان</a>
                  </li>

                  @if(auth()->user()->role_id>1)
                    
                    <li>
                      <a href="{{ route('show-stats') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش ماهیانه کارشناسان</a>
                    </li>

                    <li>
                      <a href="{{ route('show-parameter-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش نمرات بر حسب پارامترها</a>
                    </li>

                    <li>
                      <a href="{{ route('show-subject-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش نمرات بر حسب موضوعات</a>
                    </li>

                    <li>
                      <a href="{{ route('leaderboard') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">لیدربورد کارشناسان</a>
                    </li>
                    
                    <li>
                      <a href="{{ route('qc-qc-show-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش اول عملکرد تیم qc</a>
                    </li>

                    <li>
                      <a href="{{ route('show-qc-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش دوم عملکرد qc</a>
                    </li>

                    <li>
                      <a href="{{ route('show-hourly-monitor') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش ساعت کارکرد تیم qc</a>
                    </li>
                    
          
                    <li>
                      <a href="{{ route('qc-qc-show-leaderboard') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">لیدربورد تیم qc</a>
                    </li>

                    <li>
                      <a href="{{ route('qc-supervisors-show-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش اول عملکرد تیم سوپروایزر</a>
                    </li>

                    <li>
                      <a href="{{ route('show-supervisor-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش دوم عملکرد تیم سوپروایزر</a>
                    </li>

                    <li>
                      <a href="{{ route('qc-supervisors-show-leaderboard') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">لیدربورد اول تیم سوپروایزر</a>
                    </li>

                    <li>
                      <a href="{{ route('supervisor-scores') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">لیدربورد دوم تیم سوپروایزر</a>
                    </li>

                    <li>
                      <a href="{{ route('supervisor-records-leaderboard') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">لیدربورد سوم تیم سوپروایزر</a>
                    </li>

                    <li>
                      <a href="{{ route('weighted-subjects-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گزارش موضوعات وزن‌دار</a>
                    </li>

                  @endif
                </ul>
            </div>
        </li>

        <li>
          <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar11" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto">
            اطلاعیه‌ها
            <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
            </svg>
        </button>
          <!-- Dropdown menu -->
          <div id="dropdownNavbar11" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
              <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">

                <li>
                  <a href="{{ route('announcements-archive') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آرشیو اطلاعیه ها</a>
                </li>

                <li>
                  <a href="{{ route('announcements-create') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">ایجاد اطلاعیه</a>
                </li>
              </ul>
          </div>
      </li>

          <li>
              <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar1" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto ">
                جداول
                <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                </svg>
            </button>
              <!-- Dropdown menu -->
              <div id="dropdownNavbar1" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
                  <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">
                      <li>
                        <a href="{{ route('show-tickets') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمایش تیکت ها</a>
                      </li>
                      <li>
                        <li>
                          <a href="{{ route('deposit-without-memo') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">واریز بدون ممو</a>
                        </li>
                        <li>
                          <a href="{{ route('campaigns-show-list') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">کمپین ها</a>
                        </li>
                        <li>
                        <a href="{{ route('show-snapshots') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمایش اسنپ شات ها</a>
                      </li>
                      <li>
                        <a href="{{ route('show-data') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمایش دیتا</a>
                      </li>

                      <li>
                        <a href="{{ route('show-items') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمایش آیتم ها</a>
                      </li>
                      
                      <li>
                        <a href="{{ route('weighted-subjects') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">موضوعات وزن دار</a>
                      </li>
                    @if(auth()->user()->role_id>1)
                    
                      <li>
                        <a href="{{ route('show-users') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">کارشناسان</a>
                      </li>
                      <li>
                        <a href="{{ route('show-parameters') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">پارامترها</a>
                      </li>
                      <li>
                        <a href="{{ route('show-periods') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">دوره ها</a>
                      </li>
                      <li>
                        <a href="{{ route('show-settings') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">تنظیمات</a>
                      </li>
                      <li>
                        <a href="{{ route('exceptions') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">استثنائات</a>
                      </li>
                      

                      

                    @endif

                    @if(auth()->user()->role_id>2)
                    <li>
                      <a href="{{ route('exams-show-report') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمرات آزمون ماهانه</a>
                    </li>
                      <li>
                        <a href="{{ route('show-monitor') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">مانیتورینگ</a>
                      </li>
                      
                    @endif
                    
                  </ul>
              </div>
          </li>

          
          <li>
            <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar3" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto">
              گفتینو
              {{-- لایوچت --}}
              <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
              </svg>
          </button>
            <!-- Dropdown menu -->
            <div id="dropdownNavbar3" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
                <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">

                  <li>
                    {{-- <a href="{{ route('show-livechats') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمایش چت ها</a> --}}
                    <a href="{{ route('goftino-show-chats') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">نمایش چت ها</a>
                  </li>

                  <li>  
                    <a href="{{ route('challenging-records') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">چت های چالش برانگیز</a>
                  </li>

                  </li>
                  @if(auth()->user()->role_id>1)

                  <li>
                    <a href="{{ route('goftino-show-assignments') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">assignments report</a>
                  </li>

                  <li>
                    <a href="{{ route('goftino-show-transferred-chats') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">manually transferred</a>
                  </li>

                  <li>
                    <a href="{{ route('goftino-show-unassigned-chats') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">unassigned</a>
                  </li>

                  

                  <li>
                    <a href="{{ route('show-sensitive-words') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">گوش به زنگ</a>
                  </li>
                  {{-- <li>
                    <a href="{{ route('show-missed-chats') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">missed chats</a>
                  </li>
                  <li>
                    <a href="{{ route('show-inactive-transfers') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">inactive transfers</a>
                  </li>
                  <li>
                    <a href="{{ route('show-lost-connections') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">lost connections</a>
                  </li>
                  <li>
                    <a href="{{ route('show-queue-abandonment') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">queue abandonment</a>
                  </li>
                  <li>
                    <a href="{{ route('show-signed-out-transfers') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">signed-out transfers</a>
                    <a href="{{ route('show-chat-transfers') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">chat transfers</a>
                  </li>
                  <li>
                    <a href="{{ route('show-take-overs') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">take-overs</a>
                  </li>
                  <li>
                    <a href="{{ route('show-inactive-archive') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">inactive-archive</a>
                  </li> --}}
                  @endif
                </ul>
            </div>
        </li>
        @if(auth()->user()->role_id>2)
        
        <li>
          <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar13" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto">
            okr
            <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
            </svg>
        </button>
          <!-- Dropdown menu -->
          <div id="dropdownNavbar13" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
              <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">

                <li>
                  <a href="{{ route('show-qc-items') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">OKR qc items</a>
                </li>
                <li>
                  <a href="{{ route('show-qc-data') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">OKR qc data</a>
                </li>
                <li>
                  <a href="{{ route('show-supervisor-items') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">OKR supervisor items</a>
                </li>
                <li>
                  <a href="{{ route('show-supervisor-data') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">OKR supervisor data</a>
                </li>
                <li>
                  <a href="{{ route('show-okr-result') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">OKR results</a>
                </li>
                <li>
                  <a href="{{ route('show-okr-periods') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">OKR periods</a>
                </li>
              </ul>
          </div>
      </li>

      @endif
          
          @if(auth()->user()->role_id>1)
          <li>
              <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar2" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto ">
                اکسل
                <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                </svg>
            </button>
              <!-- Dropdown menu -->
              <div id="dropdownNavbar2" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
                  <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">
                    <li>
                      <a href="{{ route('upload-chat-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل لایوچت</a>
                    </li>
                    <li>
                      <a href="{{ route('upload-call-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل پارس لاجیک</a>
                    </li>
                    <li>
                      <a href="{{ route('upload-kyc-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل احراز</a>
                    </li>
                    <li>
                      <a href="{{ route('upload-outgoing-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل خروجی ها</a>
                    </li>
                    <li>
                      <a href="{{ route('upload-sls-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل SLS</a>
                    </li>
                    <li>
                      <li>
                        <a href="{{ route('upload-top10-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل top10</a>
                      </li>
                      <li>
                        <a href="{{ route('upload-incoming-call-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل تماس ورودی</a>
                      </li>
                      <li>
                        <a href="{{ route('upload-missed-call-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل میس کال</a>
                      </li>
                      <li>
                        <a href="{{ route('upload-nanowatch-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل نانوواچ </a>
                      </li>
                      <li>
                        <a href="{{ route('upload-hard-cases-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل پرونده های سخت </a>
                      </li>
                      <li>
                        <a href="{{ route('upload-comprehensive-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل جامع </a>
                      </li>

                      <li>
                        <a href="{{ route('upload-campaign-excel') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">آپلود اکسل کمپین </a>
                      </li>

                      <li>
                      <a href="{{ route('okr') }}" class="block px-4 py-2 hover:bg-gray-100  ">آپلود اکسل‌های okr</a>
                    </li>
                  </ul>
              </div>
          </li>
          <li>
            <a href="{{ route('pool') }}" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 ">استخر</a>
          </li>
          <li>
            <a href="{{ route('create-record') }}" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 ">اتاق کنترل</a>
          </li>
          @endif


          @if(auth()->user()->role_id>2)
        
        <li>
          <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar14" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto">
            متفرقه
            <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
            </svg>
        </button>
          <!-- Dropdown menu -->
          <div id="dropdownNavbar14" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 ">
              <ul class="py-2 text-sm text-gray-700 " aria-labelledby="dropdownLargeButton">

                <li>
                  <a href="{{ route('campaigns-second-list') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">کمپین وال‌گلد</a>
                </li>

                <li>
                  <a href="{{ route('market-health') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">market health</a>
                </li>

                <li>
                  <a href="{{ route('voice-to-text') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">TTS (AI)</a>
                </li>

                <li>
                  <a href="{{ route('crawler-slack') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">slack crawler</a>
                </li>

                <li>
                  <a href="{{ route('download-records',4) }}" class="block px-4 py-2 hover:bg-gray-100 0 ">دانلود اکسل رکوردها</a>
                </li>

                <li>
                  <a href="{{ route('download',4) }}" class="block px-4 py-2 hover:bg-gray-100 0 ">دانلود اکسل دوره ها</a>
                </li>

                <li>
                  <a href="{{ route('auto-calc') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">AI (auto-calc)</a>
                </li>

                <li>
                  <a href="{{ route('extract-data') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">AI (auto-extract)</a>
                </li>

                <li>
                  <a href="{{ route('metabase') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">metabase</a>
                </li>

                <li>
                  <a href="{{ route('online-chats') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">online chats</a>
                </li>
                
                <li>
                  <a href="{{ route('delete-null') }}" class="block px-4 py-2 hover:bg-gray-100 0 ">delete null</a>
                </li>

                <li>
                  <a href="{{ route('weighted-subjects-ratio',6) }}" class="block px-4 py-2 hover:bg-gray-100 0 ">weighted-subjects-ratio</a>
                </li>

              </ul>
          </div>
      </li>
      
      @endif

        </ul>
      </div>
    </div>
  </nav>
  