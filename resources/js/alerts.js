document.addEventListener('livewire:init', () => {
    Livewire.on('operation-successful', (event) => {
        alert('عملیات با موفقیت انجام شد.')
    });

    Livewire.on('operation-failed', (event) => {
        alert('عملیات ناموفق بود!')
    });

    Livewire.on('update', (event) => {
        alert('عملیات ناموفق بود!')
    });
    Livewire.hook('message.processed', (message, component) => {
        alert('test')
    });
 })

 document.addEventListener('livewire:load', function () {
    // Call your function after the Livewire component is loaded
    alert('test')
});

document.addEventListener('livewire:update', function () {
    // Call your function after the Livewire component is updated
    alert('test')
});

