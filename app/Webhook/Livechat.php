<?php

namespace App\Webhook;

use App\Jobs\ChangeCacheArray;
use App\Jobs\ProcessLivechatWebhook;
use App\Models\Livechat as Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;


class Livechat
{
    // https://platform.text.com/docs/docs/management/webhooks#incoming_event
    // https://platform.text.com/docs/messaging/agent-chat-api/data-structures#events
    // https://platform.text.com/docs/messaging/references/system-messages

    private $model;
    private $platform = 'wallex';
    private $chat_id;
    private $thread_id;
    private $token = "Y2Q4MjlmMTEtMzAwOS00ZWVmLTk3YjMtNmQ1N2U3MmVkYzA5OmRhbDpfcWRtTGYzckU2X01qa3llUmVaOTZURlJaZXc=";    // base64

    public function goftino()
    {
        
    }
    
    public function record(Request $request)
    {
        $event = $request->input('event',null);

        if($event)
        {
            $this->goftino();
            return;
        }

        $action = $request->input('action',null);

        $organizations = [
            '0523960d-5339-4f77-90f8-131ec088fdb4'=>'wallex',
            'e753e319-ab6b-4307-9269-703d33f8a0a6'=>'wallgold',
        ];
        $organization_id = $request->input('organization_id',null);

        $this->platform = $organizations[$organization_id];

        if($action=='chat_deactivated') {

            $this->chat_id = $request->input('payload.chat_id',null);
            $this->thread_id = $request->input('payload.thread_id',null);
            
            if($this->platform=='wallex')
            {
                $chat_id_thread_id = "{$this->chat_id}/{$this->thread_id}";
                ChangeCacheArray::dispatch('remove',$chat_id_thread_id);
            }
            
            $this->chat_deactivated($request);
        } else {
            // incoming_chat
            $chat_id = $request->input('payload.chat.id',null);
            $thread_id = $request->input('payload.chat.thread.id',null);

            if($this->platform=='wallex')
            {
                $chat_id_thread_id = "{$chat_id}/{$thread_id}";
                ChangeCacheArray::dispatch('add',$chat_id_thread_id,now());
            }
        }
    }

    public function chat_deactivated($request)
    {
        $user_id = $request->input('payload.user_id',null);

        $this->model = Model::firstOrCreate([
            'thread_id' => $this->thread_id,
            'chat_id' => $this->chat_id,
        ],[
            'platform'=>$this->platform,
            'user_id' => $user_id,
        ]);

        ProcessLivechatWebhook::dispatch($this->model->id)
        ->delay(now()->addMinutes(10));
    }

    public function list_archives($thread_id,$platform='wallex')
    {
        if($platform=='wallgold'){
            $this->token = "NTFjYjk0Y2QtYWIwNS00ZDJkLTgzZjUtZmMzOWUwMjcyNGI0OmRhbDpYNnZsTHJ6a2ZrT0NlMm5rY252MkRJanhOT0U=";
        }
        
        $url = "https://api.livechatinc.com/v3.5/agent/action/list_archives";

        $response = Http::withHeader('Authorization', "Basic " . $this->token)
        ->post($url,[
            'filters' => [
                'thread_ids' => [$thread_id]
            ] 
        ]);
        return $response;
    }
}