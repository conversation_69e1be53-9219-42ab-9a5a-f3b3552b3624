<?php
namespace App\AI\Extractor;

use App\Models\Goftino_unassigned_chat;
use App\Models\Stat;

class Chat_unassign extends Base
{
    public function index($period)
    {
        $goftino_unassigned_chats = Goftino_unassigned_chat::where('created_at',">=",$period->from)
        ->where('created_at',"<",$period->to);

        $data = [];

        foreach($this->users as $user)
        {
            $count = $goftino_unassigned_chats->clone()
            ->where('operator_id',$user->goftino_operator_id)->count();

            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 20,     // unassigned chats
                'value' => $count,
            ];
        }

        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }

    }
}