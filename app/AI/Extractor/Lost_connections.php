<?php
namespace App\AI\Extractor;

use App\Models\Lost_connection;
use App\Models\Stat;

class Lost_connections extends Base
{
    public function index($period)
    {
        $data=[];

        foreach($this->users as $user)
        {
            $count = Lost_connection::
            where('created_at',">=",$period->from)
            ->where('created_at',"<",$period->to)
            ->where('agent_removed',$user->email)->count();

            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 19,    // 
                'value' => $count,
            ];
        }

        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }
    }
}