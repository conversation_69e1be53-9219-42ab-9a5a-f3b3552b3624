<?php
namespace App\AI\Extractor;

use App\Models\Stat;
use App\Models\Ticket;

class Back_to_call extends Base
{
    public function index($period)
    {
        $data = [];
        foreach($this->users as $user)
        {
            $count = Ticket::where('support_agent_id',$user->id)
            ->where('creation_date',">=",$period->from)
            ->where('creation_date',"<",$period->to)
            ->where('subject','تماس خروجی جهت تکمیل اطلاعات')
            ->where('status',true)
            ->count();

            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 14,    // برگشت تیکت به علت نیاز به تکمیل اطلاعات
                'value' => $count,
            ];

        }
        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }
    }
}