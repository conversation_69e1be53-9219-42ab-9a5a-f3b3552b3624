<?php
namespace App\AI\Extractor;
use App\Models\Position_rest;
use App\Models\Position_user;
use App\Models\Stat;
use Carbon\Carbon;

class Position_logs extends Base
{
    public function index($period)
    {
        $data = [];
        $position_users = Position_user::where('role_id',1)
        ->where('isBlocked',false)
        ->get();
        
        $from = Carbon::parse($period->from);
        $to = Carbon::parse($period->to);
        $dates = [];
        for($date = $from->copy(); $date->lt($to); $date->addDay())
        {
            $dates[] = $date->copy();
        }
        
        foreach ($this->users as $user)
            {
                $position_user = $position_users->where('email',$user->email)->first();
                if(is_null($position_user)) {continue;}
    
                $shifts = Position_rest::where('user_id', $position_user->id)
                ->orderBy('started_at', 'asc')
                ->whereNull('queue_id')
                ->where('started_at', '>=', $from)
                ->where('started_at', '<', $to)
                ->get();
    
                $first_shift = $shifts->first();
                $last_shift = $shifts->last();
    
                $logs = Position_rest::where('user_id', $position_user->id)
                ->orderBy('started_at', 'asc')
                ->whereNotNull('queue_id')
                ->where('started_at', '>=', $from)
                ->where('started_at', '<', $to)
                ->get();
    
                $value = 0;
    
                foreach ($dates as $date)
                {
                    // $sum_shift = 0;
                    // $sum_other = 0;
                    $sum_rests = 0;
                    
                    foreach ($shifts->where('started_at',">=",$date)->where('started_at',"<",$date->copy()->addDay()) as $shift)
                    {
                        // $sum_shift += $shift->duration;
                        // $sum_other += $logs->where('started_at','>=',$shift->started_at)->where('ended_at','<',$shift->ended_at)->sum('duration');
                        $sum_rests += $logs->where('started_at','>=',$shift->started_at)->where('started_at','<',$shift->ended_at)->whereIn('queue_id', [5,6,7])->sum('duration');
                    }
                    // $sum_wait = $sum_shift - $sum_other;
                    if($sum_rests/60>60)
                    {
                        $value+=$sum_rests/60 - 60;
                    }
                }
                
                $data [] = [
                    'user_id' => $user->id,
                    'period_id' => $period->id,
                    'item_id' => 15,    // ثبت تردد و استراحت سایت پوزیشن ها	
                    'value' => $value,
                ];
    
            }
    
            if($data)
            {
                Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
            }
    
        }
}