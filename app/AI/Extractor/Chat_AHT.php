<?php
namespace App\AI\Extractor;

// use App\Models\Livechat;
use App\Models\Goftino_chat;
use App\Models\Stat;

class Chat_AHT extends Base
{
    public function index($period)
    {
        // $livechats = Livechat::where('created_at',">=",$period->from)
        // ->where('created_at',"<",$period->to);

        $goftino_chats = Goftino_chat::where('created_at',">=",$period->from)
        ->where('created_at',"<",$period->to);

        $data = [];

        foreach($this->users as $user)
        {
            $aht = $goftino_chats->clone()
            ->whereNotNull('assignment_date')
            ->whereNotNull('duration')
            ->where('duration','>',0)
            ->where('operator_id',$user->goftino_operator_id)->average('duration');

            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 5,     // تعداد چت
                'value' => round($aht/60,2),
            ];
        }

        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }
    }
}