<?php
namespace App\AI\Extractor;

use App\Models\Leave;
use App\Models\Position_user;
use App\Models\Stat;
use Carbon\Carbon;

class Leave_announcement extends Base
{
    public function index($period)
    {
        $data = [];
        $position_users = Position_user::where('role_id',1)
        ->where('isBlocked',false)
        ->get();

        foreach ($this->users as $user)
        {
            $position_user = $position_users->where('email',$user->email)->first();

            if(is_null($position_user)) {continue;}

            $leaves = Leave::where('user_id',$position_user->id)
            ->where('verified_by_supervisor',true)
            ->where('verified_by_head',true)
            ->where('from',">=",$period->from)
            ->where('from',"<",$period->to)
            ->get();

            $count = 0;
            foreach($leaves as $leave)
            {
                $created_at = Carbon::parse($leave->created_at);
                $from = Carbon::parse($leave->from);

                if(($created_at)->diffInHours($from) < 24)
                {
                    $count = $count + 1;
                }
            }

            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 17,     // مرخصی به موقع و حضور به موقع در محیط کار
                'value' => $count,
            ];
        }
        
        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }
    }
}