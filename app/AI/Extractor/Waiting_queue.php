<?php
namespace App\AI\Extractor;

use App\Models\Position_rest;
use App\Models\Position_user;
use App\Models\Stat;
use Carbon\Carbon;

class Waiting_queue extends Base
{
    public function index($period)
    {
    $data = [];
    $position_users = Position_user::where('role_id',1)
    ->where('isBlocked',false)
    ->get();
    
    $from = Carbon::parse($period->from);
    $to = Carbon::parse($period->to);
    $dates = [];
    for($date = $from->copy(); $date->lt($to); $date->addDay())
    {
        $dates[] = $date->copy();
    }
    
    foreach ($this->users as $user)
        {
            $position_user = $position_users->where('email',$user->email)->first();
            if(is_null($position_user)) {continue;}

            $shifts = Position_rest::where('user_id', $position_user->id)
            ->orderBy('started_at', 'asc')
            ->whereNull('queue_id')
            ->where('started_at', '>=', $from)
            ->where('started_at', '<', $to)
            ->get();

            if($shifts->count() == 0)
            {
                continue;
            }

            $first_shift = $shifts->first();
            $last_shift = $shifts->last();

            $logs = Position_rest::where('user_id', $position_user->id)
            ->orderBy('started_at', 'asc')
            ->whereNotNull('queue_id')
            ->where('started_at', '>=', $first_shift->started_at)
            ->where('started_at', '<', $last_shift->ended_at)
            ->get();

            $count = 0;

            foreach ($dates as $date)
            {
                $sum_shift = 0;
                $sum_other = 0;
                
                foreach ($shifts->where('started_at',">=",$date)->where('started_at',"<",$date->copy()->addDay()) as $shift)
                {
                    $sum_shift += $shift->duration;
                    $sum_other += $logs->where('started_at','>=',$shift->started_at)->where('ended_at','<',$shift->ended_at)->sum('duration');
                }
                $sum_wait = $sum_shift - $sum_other;
                if((in_array($position_user->shift_id,[1,4,5]) and $sum_wait/60 > 30) or (in_array($position_user->shift_id,[2,3,6]) and $sum_wait/60 > 35))
                {
                    $count=$count+1;
                }
            }
            
            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 16,     // مدت زمان انتظار در صف
                'value' => $count,
            ];

        }

        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }

    }
}