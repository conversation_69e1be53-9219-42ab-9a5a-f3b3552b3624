<?php
namespace App\AI\Extractor;

use App\Models\Stat;
use App\Models\Ticket;

class Wrong_SL extends Base
{
    public function index($period)
    {
        $data = [];
        foreach($this->users as $user)
        {
            $count = Ticket::where('support_agent_id',$user->id)
            ->where('creation_date',">=",$period->from)
            ->where('creation_date',"<",$period->to)
            ->where('subject','انتخاب گزینه ی "ارجاع جهت پیگیری" به اشتباه')
            ->where('status',true)
            ->count();

            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 13,    // انتخاب گزینه ارجاع به SLS به اشتباه	
                'value' => $count,
            ];

        }
        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }
    }
}