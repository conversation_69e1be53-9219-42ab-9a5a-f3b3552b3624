<?php
namespace App\AI\Extractor;

use App\Models\Goftino_chat;
use App\Models\Livechat;
use App\Models\Stat;

class Chat_count extends Base
{
    public function index($period)
    {
        // $livechats = Livechat::where('created_at',">=",$period->from)->where('created_at',"<",$period->to);
        $goftino_chats = Goftino_chat::where('created_at',">=",$period->from)->where('created_at',"<",$period->to);

        $data = [];

        foreach($this->users as $user)
        {
            // $count = $livechats->clone()->where('author_id',$user->email)->count();
            $count = $goftino_chats->clone()->where('operator_id',$user->goftino_operator_id)->count();
            $data [] = [
                'user_id' => $user->id,
                'period_id' => $period->id,
                'item_id' => 1,     // تعداد چت
                'value' => $count,
            ];
        }
        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
        }
    }
}