<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

use App\Models\Setting;

class AHT_chat extends Base
{
    public function index($period)
    {
        $chat_aht_stats = Stat::where('period_id', $period->id)->where('item_id', 5)->get();
        $chat_count_stats = Stat::where('period_id', $period->id)->where('item_id', 1)->get();

        foreach ($this->users as $user) {
            $aht = $chat_aht_stats->where('user_id', $user->id)->first()?->value;
            if(is_null($aht)) {
                continue;
            }

            $parameter_id = [35,103]; // پاسخگویی صحیح
            $records = $user->records()
            ->where('incoming_type', 'chat')
            ->where('records.incoming_date', '>=', $period->from)
            ->where('records.incoming_date', '<', $period->to)
            ->whereHas('parameters', function ($q) use ($parameter_id) {
                $q->whereIn('parameter_id', $parameter_id);
            })
            ->with(['parameters' => function ($q) use ($parameter_id) {
                $q
                ->whereIn('parameter_id', $parameter_id);
            }])->get();

            $totalValue = 0;
            $totalScore = 0;
            $totalCount = 0;
            foreach($records as $record) {
                foreach($record->parameters as $parameter) {
                    if(!is_null($parameter->pivot->value)) {
                        $totalValue += $parameter->pivot->value;
                        $totalScore += $parameter->score;
                        $totalCount++;
                    }
                }
            }

            // $value = $totalCount ? $totalValue / $totalCount : null;
            // $value = $value / $this->parameters->where('id', $parameter_id)->first()->score * 100;
            $value = $totalCount ? $totalValue / $totalScore * 100 : null;
            $qc_chat_score = $value;

            $user_chat_count = $chat_count_stats->where('user_id', $user->id)->first()?->value;
            $min_chat = Setting::where('name', 'min_chat')->first()->value;
            // aht in seconds
            if($user_chat_count <= $min_chat) {
                $value = null;
            } elseif($aht >= 8 and $aht <= 11) {
                $value = 6;
            } elseif((($aht >= 7 and $aht < 8) or ($aht > 11 and $aht <= 12)) and ($qc_chat_score >= 90 and $qc_chat_score <= 100)) {
                $value = 5;
            } elseif((($aht >= 7 and $aht < 8) or ($aht > 11 and $aht <= 12)) and ($qc_chat_score >= 85 and $qc_chat_score < 90)) {
                $value = 4;
            } else {
                $value = 0;
            }

            $parameter_id = 172;     // AHT چت
            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}