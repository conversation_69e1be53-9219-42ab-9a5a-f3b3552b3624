<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Choose_wrong_user extends Base
{
    public function index($period)
    {
        $wrong_user = Stat::where('period_id', $period->id)->where('item_id', 12)->get();
        $parameter_id = 46;     // انتخاب کاربر اشتباه
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {
            $user_wrong = $wrong_user->where('user_id', $user->id)->first()?->value;

            if(is_null($user_wrong)){continue;}

            if($user_wrong <= 1)
            {
                $value = 4;
            }
            elseif($user_wrong == 2 or $user_wrong==3)
            {
                $value = 3;
            }else
            {
                $value = 0;
            }
            
            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}