<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Waiting_queue extends Base
{
    public function index($period)
    {
        $waiting_queue = Stat::where('period_id', $period->id)->where('item_id', 16)->get();
        $parameter_id = 55;     // مدت زمان حضور در صف انتظار
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_queue = $waiting_queue->where('user_id', $user->id)->first()?->value;

            if(is_null($user_queue)){continue;}

            if($user_queue <= 2)
            {
                $value = 5;
            }
            else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}