<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Position_logs extends Base
{
    public function index($period)
    {

        $position_logs = Stat::where('period_id', $period->id)->where('item_id', 15)->get();
        $parameter_id = 148;     // ثبت تردد و استراحت crm و پوزیشن
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_logs = $position_logs->where('user_id', $user->id)->first()?->value;

            if(is_null($user_logs)){continue;}

            if($user_logs <= 60)
            {
                $value = 5;
            }
            elseif($user_logs>60 and $user_logs<=3*60)
            {
                $value = 3;
            }
            else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}