<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Choose_wrong_subject extends Base
{
    public function index($period)
    {
        $choose_wrong_subject = Stat::where('period_id', $period->id)->where('item_id', 10)->get();
        $parameter_id = 99;      // ثبت تیکت با موضوع اشتباه
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_wrong_subject = $choose_wrong_subject->where('user_id', $user->id)->first()?->value;
            
            if(is_null($user_wrong_subject)){continue;}

            if($user_wrong_subject <= 1)
            {
                $value = 5;
            }
            elseif($user_wrong_subject == 2 or $user_wrong_subject==3)
            {
                $value = 4;
            }else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}