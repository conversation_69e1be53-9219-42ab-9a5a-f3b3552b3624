<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Manual_nanowatch extends Base
{
    public function index($period)
    {
        $manual_nanowatch = Stat::where('period_id', $period->id)->where('item_id', 18)->get();
        $parameter_id = 179;         // ثبت فراموشی
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_manual = $manual_nanowatch->where('user_id', $user->id)->first()?->value;

            if(is_null($user_manual)){continue;}

            if($user_manual <= 2) {
                $value = 4;
            }
            elseif ($user_manual==3 or $user_manual==4){
                $value = 2;
            } else {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
            
        }
    }
}