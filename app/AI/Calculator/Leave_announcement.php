<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Leave_announcement extends Base
{
    public function index($period)
    {
        $leave_announcement = Stat::where('period_id', $period->id)->where('item_id', 17)->get();
        $parameter_id = 53;     // مرخصی - رعایت نظم حضور به موقع
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_leave = $leave_announcement->where('user_id', $user->id)->first()?->value;

            if(is_null($user_leave)){continue;}

            if($user_leave <= 2)
            {
                $value = 5;
            }
            else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}