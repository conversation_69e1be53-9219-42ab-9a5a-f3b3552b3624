<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Unassign_chat extends Base
{
    public function index($period)
    {
        $unassigned_chats = Stat::where('period_id', $period->id)->where('item_id', 20)->get();
        $parameter_id = 170;        // unassigned chats

        foreach ($this->users as $user)
        {

            $user_unassigned_chats = $unassigned_chats->where('user_id', $user->id)->first()?->value;
            
            if(is_null($user_unassigned_chats))
            {
                continue;
            }


            if($user_unassigned_chats <= 5)
            {
                $value = 3;
            }
            else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }

}