<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Ticketing_delay extends Base
{
    public function index($period)
    {
        $ticketing_delay_fl = Stat::where('period_id', $period->id)->where('item_id', 8)->get();
        $ticketing_delay_sl = Stat::where('period_id', $period->id)->where('item_id', 9)->get();
        
        $parameter_id = 178;      // زمان ایجاد پاپ آپ چت و تماس تا زمان ثبت آن
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user)
        {

            $user_fl = $ticketing_delay_fl->where('user_id', $user->id)->first()?->value;
            $user_sl = $ticketing_delay_sl->where('user_id', $user->id)->first()?->value;
            $both = $user_fl + $user_sl;
            
            if(is_null($user_fl) and is_null($user_sl))
            {
                continue;
            }


            if($both <= 50)
            {
                $value = 3;
            }
            elseif($both>50 and $both<=75)
            {
                $value = 2;
            }
            else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}
