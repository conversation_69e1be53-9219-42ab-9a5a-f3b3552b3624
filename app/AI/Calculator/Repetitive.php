<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Repetitive extends Base
{
    public function index($period)
    {
        $repetitive = Stat::where('period_id', $period->id)->where('item_id', 11)->get();
        $parameter_id = 175;     // ثبت تیکت با موضوع تکراری
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_repetitive = $repetitive->where('user_id', $user->id)->first()?->value;

            if(is_null($user_repetitive)){continue;}

            if($user_repetitive <= 2)
            {
                $value = 5;
            }
            elseif($user_repetitive == 3 or $user_repetitive==4)
            {
                $value = 3;
            }else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}