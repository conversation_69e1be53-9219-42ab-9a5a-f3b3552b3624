<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Back_to_call extends Base
{
    public function index($period)
    {
        $back_to_call = Stat::where('period_id', $period->id)->where('item_id', 14)->get();
        $parameter_id = 177;         // برگشت تیکت به علت نیاز به تکمیل اطلاعات
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_back = $back_to_call->where('user_id', $user->id)->first()?->value;

            if(is_null($user_back)){continue;}

            if($user_back <= 2)
            {
                $value = 6;
            }
            elseif($user_back == 3 or $user_back==4)
            {
                $value = 4;
            }else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}