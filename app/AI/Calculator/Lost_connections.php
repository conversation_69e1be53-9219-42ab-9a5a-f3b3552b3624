<?php

namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Lost_connections extends Base
{
    public function index($period)
    {
        $parameter_id = 127;

        $lost_records = Stat::where('period_id', $period->id)->where('item_id', 19)->get();
        $chat_count_stats = Stat::where('period_id', $period->id)->where('item_id', 1)->get();

        foreach ($this->users as $user) {

            $user_lost_records=$lost_records->where('user_id', $user->id)->first()?->value;

            if(is_null($user_lost_records)){continue;}

            $user_chat_count = $chat_count_stats->where('user_id', $user->id)->first()?->value;

            if(is_null($user_chat_count) or $user_chat_count == 0){continue;}

            $percent = $user_lost_records / $user_chat_count * 100;

            if($percent <= 5)
            {
                $value = 4;
            }
            elseif($percent <=10)
            {
                $value = 3;
            }
            else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }

    }
}