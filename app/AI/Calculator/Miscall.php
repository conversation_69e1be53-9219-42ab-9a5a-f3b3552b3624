<?php

namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;
use App\Models\Setting;
class Miscall extends Base
{
    public function index($period)
    {
        $miscall_stats = Stat::where('period_id', $period->id)->where('item_id', 3)->get();
        $call_count_stats = Stat::where('period_id', $period->id)->where('item_id', 2)->get();

        $parameter_id = 174;     // زنگ بدون پاسخ
        $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {
            $miscall = $miscall_stats->where('user_id', $user->id)->first()?->value;
            if(is_null($miscall)) {
                continue;
            }

            $user_call_count = $call_count_stats->where('user_id', $user->id)->first()?->value;
            $min_call = Setting::where('name', 'min_call')->first()->value;

            if($user_call_count <= $min_call) {
                $value = null;
            } elseif($miscall <= 2) {
                // $value = $score;
                $value = 4;
            } elseif($miscall==3 or $miscall==4) {
                $value = 2;
            } else {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}