<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Choose_wrong_SL extends Base
{
    public function index($period)
    {
        $wrong_sl = Stat::where('period_id', $period->id)->where('item_id', 13)->get();
        $parameter_id = 176;         // انتخاب گزینه ارجاع به sls به اشتباه
        // $score = $this->parameters->where('id', $parameter_id)->first()->score;

        foreach ($this->users as $user) {

            $user_wrong_sl = $wrong_sl->where('user_id', $user->id)->first()?->value;

            if(is_null($user_wrong_sl)){continue;}

            if($user_wrong_sl <= 1)
            {
                $value = 7;
            }
            elseif($user_wrong_sl == 2 or $user_wrong_sl==3)
            {
                $value = 5;
            }else
            {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}