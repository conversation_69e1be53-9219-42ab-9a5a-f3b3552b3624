<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;
use App\Models\Setting;

class AHT_call extends Base
{
    public function index($period)
    {
        $call_aht_stats = Stat::where('period_id', $period->id)->where('item_id', 6)->get();
        $call_count_stats = Stat::where('period_id', $period->id)->where('item_id', 2)->get();
        
        foreach ($this->users as $user) {
            $aht = $call_aht_stats->where('user_id', $user->id)->first()?->value;
            if(is_null($aht)) {
                continue;
            }

            $parameter_id = [17,105]; // پاسخگویی صحیح
            $records = $user->records()
            ->where('incoming_type', 'call')
            ->where('records.incoming_date', '>=', $period->from)
            ->where('records.incoming_date', '<', $period->to)
            ->whereHas('parameters', function ($q) use ($parameter_id) {
                $q->whereIn('parameter_id', $parameter_id);
            })
            ->with(['parameters' => function ($q) use ($parameter_id) {
                $q
                ->whereIn('parameter_id', $parameter_id);
            }])->get();

            $totalValue = 0;
            $totalScore = 0;
            $totalCount = 0;
            foreach($records as $record) {
                foreach($record->parameters as $parameter) {
                    if(!is_null($parameter->pivot->value)) {
                        $totalValue += $parameter->pivot->value;
                        $totalScore += $parameter->score;
                        $totalCount++;
                    }
                }
            }
            // $value = $totalCount ? $totalValue / $totalCount : null;
            // $qc_call_score = $value / $this->parameters->where('id', $parameter_id)->first()->score * 100;
            $value = $totalCount ? $totalValue / $totalScore * 100 : null;
            $qc_call_score = $value;

            $user_call_count = $call_count_stats->where('user_id', $user->id)->first()?->value;
            $min_call = Setting::where('name', 'min_call')->first()->value;
            // aht in seconds
            if($user_call_count <= $min_call) {
                $value = null;
            } elseif($aht >= 140 and $aht <= 210) {
                $value = 6;
            } elseif((($aht >= 100 and $aht < 140) or ($aht > 210 and $aht <= 250)) and ($qc_call_score >= 90 and $qc_call_score <= 100)) {
                $value = 5;
            } elseif((($aht >= 100 and $aht < 140) or ($aht > 210 and $aht <= 250)) and ($qc_call_score >= 85 and $qc_call_score < 90)) {
                $value = 4;
            } else {
                $value = 0;
            }

            $parameter_id = 171;     // AHT تماس
            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}