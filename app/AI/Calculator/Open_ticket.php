<?php
namespace App\AI\Calculator;

use App\Models\Stat;
use App\Models\Score;

class Open_ticket extends Base
{
    public function index($period)
    {
        $open_tickets_24 = Stat::where('period_id', $period->id)->where('item_id', 7)->get();
        
        $parameter_id = 173;     // تیکت باز کارشناس
        $score = $this->parameters->where('id', $parameter_id)->first()->score;
        
        foreach ($this->users as $user) {
            $user_open_tickets = $open_tickets_24->where('user_id', $user->id)->first()?->value;
            
            if(is_null($user_open_tickets)) {
                continue;
            }
            
            if($user_open_tickets <= 5)
            {
                // $value = $score;
                $value = 4;
            } elseif ($user_open_tickets == 6 or $user_open_tickets==7) {
                $value = 2;
            } else {
                $value = 0;
            }

            Score::updateOrCreate([
                'user_id' => $user->id,
                'period_id' => $period->id,
                'parameter_id' => $parameter_id,
            ], [
                'value' => $value,
            ]);
        }
    }
}