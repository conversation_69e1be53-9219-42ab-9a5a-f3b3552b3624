<?php

namespace App\Exports;

use App\Models\Announcement;
use App\Models\Period;
use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class ReactsExport implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $announcements;
    public function __construct(public string $platform,public int $period_id)
    {
        
    }

    public function collection()
    {
        switch($this->platform)
        {
            case 'wallex':
            // اطلاع رسانی والکس
            $channel='C044AHLCKCY';
            break;

            case 'wallgold':
            // اطلاع رسانی وال‌گلد
            $channel='C0826FMK266';
            break;
        }

        $period = Period::find($this->period_id);
        
        $this->announcements = Announcement::
        where('channel',$channel)
        ->where('created_at','>=',$period->from)
        ->where('created_at','<',$period->to)
        ->get();

        return User::
        where('status',true)
        ->where('platform',$this->platform)
        ->where('role_id',1)
        ->get();

    }

    public function headings(): array
    {
        return [
            'کارشناس',
            'تعداد',
            'درصد',
        ];
    }

    public function title(): string
    {
        return 'reacts';
    }
    
    public function map($user) : array
    {
        $count = 0;
        foreach($this->announcements as $announcement)
        {
            if(!is_null($announcement->reacted_by) and in_array($user->id,json_decode($announcement->reacted_by, true)))
            {
                $count = $count+1;
            }
        }

        $percent = 0;
        $total = $this->announcements->count();
        if($total)
        {
            $percent = round($count/$this->announcements->count()*100,2);
        }

        return [
            $user->name,
            $count,
            $percent,
        ];
    }

    
}
