<?php

namespace App\Exports;

use App\Models\Livechat;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class LivechatsExport implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function __construct(public Carbon $date)
    {
    }

    public function collection()
    {
        return Livechat::
        where('created_at','=>',$this->date)
        ->where('created_at','<',$this->date->addDays(1))
        ->get();
    }

    public function map($livechat): array
    {
        return [
            $livechat->id,
            $livechat->platform,
            $livechat->thread_id,
            $livechat->chat_id,
            $livechat->user_id,
            $livechat->FRT,
            $livechat->author_id,
            $livechat->customer_id,
            $livechat->admin_user_id,
            $livechat->last_message_by,
            $livechat->chat_duration,
            $livechat->queues_duration,
            $livechat->agents_count,
            $livechat->transfers_count,
            $livechat->agent_message_count,
            $livechat->customer_message_count,
            $livechat->called_by_name,
            $livechat->survey,
            $livechat->start_scenario,
            $livechat->chat_management,
            $livechat->offline,
            $livechat->ai,
            $livechat->ticket,
            $livechat->chatbot,
            $livechat->file,
            $livechat->comment,
            $livechat->chat_rated,
            $livechat->started_by_agent,
            $livechat->archived_method,
            $livechat->data,
            $livechat->flagged,
            $livechat->started_at,
            $livechat->created_at,
            $livechat->updated_at,
        ];
    }

    public function headings(): array
    {
        return [
            'id',
            'platform',
            'thread_id',
            'chat_id',
            
            'user_id',
            'FRT',
            'author_id',
            'customer_id',
            'admin_user_id',
            'last_message_by',
            'chat_duration',
            'queues_duration',
            'agents_count',
            'transfers_count',
            'agent_message_count',
            'customer_message_count',
            'called_by_name',
            'survey',
            'start_scenario',
            'chat_management',
            'offline',
            'ai',
            'ticket',
            'chatbot',
            'file',
            'comment',
            'chat_rated',
            'started_by_agent',
            'archived_method',
            'data',
            'flagged',
            
            'started_at',
            'created_at',
            'updated_at',
        ];
    }

    public function title(): string
    {
        return 'livechats';
    }
}
