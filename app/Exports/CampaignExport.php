<?php

namespace App\Exports;

use App\Models\Campaign;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class CampaignExport implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Campaign::all();
    }

    public function map($item): array
    {
        return [
            $item->admin_id,
            $item->called_at ? verta($item->called_at) : '-',
            $item->user?->name ?? '-',
            $item->call_status ?? '-',
            $item->will_buy ? 'بله' : 'خیر',
            $item->why_not_buy ?? '-',
            $item->why_register ?? '-',
            $item->city ?? '-',
            $item->how_familiar ?? '-',
            $item->description ?? '-',
        ];
    }

    public function headings(): array
    {
        return [
            'شناسه ادمین',
            'زمان آخرین تماس',
            'کارشناس تماس گیرنده',
            'وضعیت تماس',
            'قصد خرید در آینده',
            'توضیحات دلیل عدم خرید',
            'توضیحات دلیل ثبت نام',
            'شهر',
            'نحوه آشنایی',
            'سایر توضیحات',
        ];
    }

    public function title(): string
    {
        return 'campaign';
    }
}
