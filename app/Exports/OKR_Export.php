<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class OKR_Export implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    public $users;

    public function __construct(public array $data)
    {
        $this->users = User::all();
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->users
        ->where('status',true)
        ->where('role_id', 1);
    }

    public function map($support_agent): array
    {
        // general info.
        $name = $support_agent->name;
        $eyebeam_id = $support_agent->eyebeam_id;
        $shift = $support_agent->shift;
        $supervisor_name = $support_agent->supervisor_id ? $this->users->where('id',$support_agent->supervisor_id)->first()->name : '';

        // incoming call data
        $incoming_call = $this->data['incoming_call']->where('support_agent_name', $name)->first();
        $incoming_call_count = $incoming_call['Count'] ?? 0;
        $incoming_call_duration = $incoming_call['DurationBysecond'] ?? 0;
        $crm = 60;
        $wrap_up = 20;
        $incoming_call_duration_total = $incoming_call_duration ? $incoming_call_duration + $crm * $incoming_call_count + $wrap_up * $incoming_call_count : 0;

        // kyc data
        $kyc = $this->data['kyc']->where('support_agent_name', $name)->first();
        $kyc_count = $kyc['KYCcount'] ?? 0;
        $kyc_duration = $kyc['Total_AgentResponsetimebysecond'] ?? 0;
        $kyc_afterwork = $kyc_count ? 0 : 0;
        $kyc_duration_total = $kyc_duration ? $kyc_duration + $kyc_afterwork : 0;

        // chat data
        $chat = $this->data['chat']->where('support_agent_name', $name)->first();
        $chats_rated_good = $chat['chats_rated_good'] ?? 0;
        $chats_count = $chat['chats_count'] ?? 0;
        $Rated_Count = $chat['Rated_Count'] ?? 0;
        $chat_simultaneously = $chat['chat_simultaneously'] ?? 0;
        $workdurationbysecond = $chat['workdurationbysecond'] ?? 0;

        // outgoing call data
        $outgoing = $this->data['outgoing_call']->where('support_agent_name', $name)->first();
        $outgoing_counts = $outgoing['Counts'] ?? 0;
        $outgoing_duration = $outgoing['Durationbysecond'] ?? 0;
        $outgoing_afterwork = $outgoing_counts ? 60 : 0;
        $outgoing_duration_total = $outgoing_duration ? $outgoing_duration + $outgoing_afterwork : 0;

        // fata data
        $fata = $this->data['fata']->where('support_agent_name', $name)->first();
        $fata_letter_count = $fata['Cases_Counts'] ?? 0;
        $fata_afterwork = $fata_letter_count ? 15 * 60 : 0;
        $fata_duration = $fata_letter_count * $fata_afterwork;

        // account manager data
        $account_manager = $this->data['account_manager']->where('support_agent_name', $name)->first();
        $account_manager_count = $account_manager['Total_accountManager'] ?? 0;
        $duration = $account_manager_count ? 600 : 0;
        $account_manager_afterwork = $account_manager_count ? 120 : 0;
        $account_manager_duration = $account_manager_count ? $account_manager_count * ($duration + $account_manager_afterwork) : 0;

        // position log data
        $position_log = $this->data['position_log']->where('support_agent_name', $name)->first();
        $livechat_ticket = $position_log['livechat_ticket'] ?? 0;
        $email = $position_log['email'] ?? 0;
        $legal = $position_log['legal'] ?? 0;
        $supervisor_task = $position_log['supervisor_task'] ?? 0;
        $phinix = $position_log['phinix'] ?? 0;
        $sls_night_shift = $position_log['sls_night_shift'] ?? 0;
        $memo_deposit = $position_log['memo_deposit'] ?? 0;

        // working data
        $total = ($livechat_ticket + $email + $legal + $supervisor_task + $phinix + $sls_night_shift + $memo_deposit + $incoming_call_duration_total + $kyc_duration_total + $workdurationbysecond + $outgoing_duration_total + $fata_duration + $account_manager_duration) / 3600;

        // $working_days = $support_agent->working_days;
        // $working_hours = 8;
        $working_days = 1;
        $working_hours = ($position_log['working_hours'] ?? 0) / 3600;

        $leaves = ($position_log['leaves'] ?? 0) / 3600;
        $rests = ($position_log['rests'] ?? 0) / 3600;
        $act = ($position_log['act'] ?? 0) / 3600;
        $coach = ($position_log['coach'] ?? 0) / 3600;
        $meeting = ($position_log['meeting'] ?? 0) / 3600;
        $tutorial = ($position_log['tutorial'] ?? 0) / 3600;
        $office = ($position_log['office'] ?? 0) / 3600;
        $IT = ($position_log['IT'] ?? 0) / 3600;

        $availability = $working_hours - ($leaves + $rests + $act + $coach + $meeting + $tutorial + $office + $IT);
        $busy_percent = $availability ? $total / $availability : '-';

        /* output */
        return [
            // general info.
            $name,
            $eyebeam_id,
            $shift,
            $supervisor_name,

            // incoming call data
            $incoming_call_count,
            $incoming_call_duration,
            $crm,
            $wrap_up,
            $incoming_call_duration_total,
            $incoming_call_duration_total / 3600,

            // kyc data
            $kyc_count,
            $kyc_duration,
            $kyc_afterwork,
            $kyc_duration_total,
            $kyc_duration_total / 3600,

            // chat data
            $chats_rated_good,
            $chats_count,
            $Rated_Count,
            $chat_simultaneously,
            $workdurationbysecond,
            $workdurationbysecond / 3600,

            // outgoing call data
            $outgoing_counts,
            $outgoing_duration,
            $outgoing_afterwork,
            $outgoing_duration_total,
            $outgoing_duration_total / 3600,

            // fata data
            $fata_letter_count,
            $fata_afterwork,
            $fata_duration,
            $fata_duration / 3600,

            // account manager data
            $account_manager_count,
            $duration,
            $account_manager_afterwork,
            $account_manager_duration,
            $account_manager_duration / 3600,

            // position log data
            $livechat_ticket,
            $email,
            $legal,
            $supervisor_task,
            $phinix,
            $sls_night_shift,
            $memo_deposit,

            // working data
            $total,

            // $working_days,
            $working_hours,

            $leaves,
            $rests,
            $act,
            $coach,
            $meeting,
            $tutorial,
            $office,
            $IT,

            $availability,
            $busy_percent,


        ];
    }

    public function headings(): array
    {
        return [
            // general info.
            'کارشناس',
            'شماره eyebeam',
            'شیفت',
            'سوپروایزر',

            // incoming call data
            'تعداد تماس ورودی',
            'زمان (ثانیه)',
            'crm time',
            'wrap up',
            'زمان کلی (ثانیه)',
            'زمان کلی (ساعت) ماهیانه',

            // kyc data
            'تعداد احراز',
            'زمان (ثانیه)',
            'afterwork',
            'زمان کلی (ثانیه)',
            'زمان کلی (ساعت) ماهیانه',

            // chat data
            'chats_rated_good',
            'chats_count',
            'Rated_Count',
            'chat_simultaneously',
            'workdurationbysecond',
            'زمان کلی (ساعت) ماهیانه',

            // outgoing call data
            'تعداد تماس خروجی',
            'زمان (ثانیه)',
            'afterwork',
            'زمان کلی (ثانیه)',
            'زمان کلی (ساعت) ماهیانه',

            // fata data
            'تعداد مراجعه فتا',
            'afterwork',
            'زمان کلی (ثانیه)',
            'زمان کلی (ساعت) ماهیانه',

            // account manager data
            'تعداد مراجعه (اکانت منیجر)',
            'زمان',
            'afterwork',
            'زمان کلی (ثانیه)',
            'زمان کلی (ساعت) ماهیانه',

            // position log data
            'تیکت لایوچت',
            'ایمیل',
            'حقوقی',
            'تسک سوپروایزر',
            'فینیکس',
            'sls (شیفت شب)',
            'واریز بدون ممو',

            // working data
            'مجموع کل',

            // 'روز کاری',
            'ساعت کاری',

            'مرخصی',
            'استراحت',
            'act',
            'جلسه کوچ',
            'جلسه',
            'کلاس آموزشی',
            'اداری',
            'مشکل IT',

            'تایم در دسترس بودن کارشناس',
            'درصد اشتغال از تایم در دسترس بودن',

        ];
    }

    public function title(): string
    {
        return 'okr';
    }
}
