<?php

namespace App\Exports;

use App\Models\Period;
use App\Models\Record;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class RecordsExport implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    /**
    * @return \Illuminate\Support\Collection
    */

    public function __construct(public int $period_id)
    {
    }
    public function collection()
    {
        $period = Period::find($this->period_id);

        return Record::
        with('user')
        ->whereNotNull('feedback')->whereNotNull('message')
        ->where('feedback','<>','-')->where('message','<>','-')
        ->where('incoming_date', '>=', $period->from)
        ->where('incoming_date', '<', $period->to)
        ->get();
    }

    public function map($record): array
    {
        $values = [$record->user->name];
        $values[] =$record->identity;
        $values[] =$record->incoming_date;
        $values[] =$record->incoming_type;
        $values[] =$record->message;
        $values[] =$record->feedback;

        return $values;
    }

    public function headings(): array
    {
        $columns = ['کارشناس'];
        $columns[] = 'شناسه';
        $columns[] = 'تاریخ مکالمه';
        $columns[] = 'ورودی';
        $columns[] = 'توضیحات';
        $columns[] = 'فیدبک';
        return $columns;
    }
    public function title(): string
    {
        return 'records';
    }
}
