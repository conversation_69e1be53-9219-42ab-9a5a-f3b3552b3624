<?php

namespace App\Exports;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\Record;
use App\Models\Setting;
use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Illuminate\Database\Eloquent\Builder;
class PeriodsExport implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    public $period;
    public $scores;
    public $parameters;
    public $records;
    public $supervisors;
    public $supervisor_coeff;
    public $qc_coeff;
    public bool $weight;

    public function __construct(public int $period_id)
    {
        $this->supervisors = User::where('role_id',3)->get();

        $this->period = Period::find($this->period_id);

        $this->parameters = Parameter::where('incoming_type','supervisor')
        ->whereHas('supervisor_periods',function($q){
            $q->where('period_id', $this->period_id);
        })
        ->get();
        
        $this->records = Record::with(['qc_parameters','red_lines'])
        ->where('incoming_date', '>=', $this->period->from)
        ->where('incoming_date', '<', $this->period->to)
        ->get();

        $this->supervisor_coeff = Setting::where('name', 'supervisor_coeff')->first()->value;
        $this->qc_coeff = Setting::where('name', 'qc_coeff')->first()->value;
        $this->weight = (bool) Setting::where('name', 'weight_integration')->first()->value;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    
    public function collection()
    {
        return User::where('role_id',1)
        ->with(['supervisor_parameters' => function ($q) {
            $q->where('period_id', $this->period_id);
        }])
        ->where(function(Builder $query){
            $query->whereHas('supervisor_parameters',function($q){
                $q->where('period_id', $this->period_id);
            })->orWhereHas('records', function($q){
                $q->where('incoming_date', '>=', $this->period->from)
                ->where('incoming_date', '<', $this->period->to);
            });
        })
        ->get();
    }

    public function map($user): array
    {
        $values = [$user->name];
        $values[] = $user->personal_code;
        $values[] = $this->supervisors->where('id',$user->supervisor_id)->first()?->name;

        // calc total qc score
        $scores = [];
        $chat_scores = [];
        $call_scores = [];
        $kyc_scores = [];
        $outgoing_scores = [];
        $redline_scores = [];

        foreach($this->records->where('support_agent_id',$user->id)->where('incoming_type','chat')->where('ignore',false) as $record)
        {
            $sum = 0.0;
            $total = 0.0;
            foreach ($record->qc_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            if($sum) {

                $coefficient = 1;
                if($this->weight){$coefficient = $record->coefficient;}

                for($weight=1;$weight<=$coefficient;$weight++)
                {
                    $chat_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;  
                }
            }
        }
        
        foreach($this->records->where('support_agent_id',$user->id)->where('incoming_type','call')->where('ignore',false) as $record)
        {
            $sum = 0.0;
            $total = 0.0;
            foreach ($record->qc_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            if($sum) {
                $coefficient = 1;
                if($this->weight){$coefficient = $record->coefficient;}

                for($weight=1;$weight<=$coefficient;$weight++)
                {
                    $call_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;
                }

            }
        }

        foreach($this->records->where('support_agent_id',$user->id)->where('incoming_type','kyc')->where('ignore',false) as $record)
        {
            $sum = 0.0;
            $total = 0.0;
            foreach ($record->qc_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            if($sum)
                {
                    $kyc_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;
                }
        }

        foreach($this->records->where('support_agent_id',$user->id)->where('incoming_type','outgoing')->where('ignore',false) as $record)
        {
            $sum = 0.0;
            $total = 0.0;
            foreach ($record->qc_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            if($sum)
                {
                    $outgoing_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;
                }
        }

        foreach($this->records->where('support_agent_id',$user->id)->where('red',true) as $record)
        {
            $sum = 0.0;
            $total = 0.0;
            foreach ($record->red_lines as $red_line) {
                if(is_null($red_line->pivot->value)) {
                    continue;
                }
                $sum += $red_line->score;
                $total += $red_line->pivot->value;
            }
            if($sum)
                {
                    $redline_scores[] = $total;
                }
        }
        
        $chat_score = count($chat_scores) ? round(array_sum($chat_scores) / count($chat_scores),2) : '-';
        $chat_count = count($chat_scores);

        $call_score = count($call_scores) ? round(array_sum($call_scores) / count($call_scores),2) : '-';
        $call_count = count($call_scores);

        $kyc_score = count($kyc_scores) ? round(array_sum($kyc_scores) / count($kyc_scores),2) : '-';
        $kyc_count = count($kyc_scores);

        $outgoing_score = count($outgoing_scores) ? round(array_sum($outgoing_scores) / count($outgoing_scores),2) : '-';
        $outgoing_count = count($outgoing_scores);

        $redline_score = count($redline_scores) ? array_sum($redline_scores) : '-';
        $qc_score = count($scores) ? round(array_sum($scores) / count($scores),2) : '-';
        
        if(count($redline_scores))
        {
            if($redline_score < 10)
            {
                $final_qc_score = $qc_score - 2;
            }
            elseif($redline_score >= 10 and $redline_score <= 20)
            {
                $final_qc_score = $qc_score - 3;
            }
            elseif($redline_score > 20)
            {
                $final_qc_score = $qc_score - 5;
            }
        }
        else
        {
            $final_qc_score = $qc_score;
        }

        // calc total supervisor score
        $sum = 0.0;
        $total = 0.0;
        foreach($user->supervisor_parameters as $parameter) {
            if(is_null($parameter->pivot->value)) {
                continue;
            }
            $sum += $parameter->score;
            $total += $parameter->pivot->value;
        }
        $supervisor_score = $sum ? round($total / $sum * 100,2) : '-';

        // total agent score
        if($supervisor_score != '-' && $final_qc_score!= '-') {
            $final_score = ($final_qc_score * $this->qc_coeff + $supervisor_score * $this->supervisor_coeff) / 100;
            $final_score = round($final_score,2);
        } elseif($supervisor_score == '-' && $final_qc_score!= '-') {
            $final_score = $final_qc_score;
        } elseif($supervisor_score != '-' && $final_qc_score== '-') {
            $final_score = $supervisor_score;
        } else {
            $final_score = '-';
        }

        foreach($this->parameters as $parameter){
            $values[] = $user->supervisor_parameters->firstWhere('id',$parameter->id)?->pivot?->value ?? '-';
        }

        $values[] = $supervisor_score;

        $values[] = $chat_score;
        $values[] = $chat_count;

        $values[] = $call_score;
        $values[] = $call_count;

        $values[] = $kyc_score;
        $values[] = $kyc_count;

        $values[] = $outgoing_score;
        $values[] = $outgoing_count;

        $values[] = $redline_score;

        $values[] = $qc_score;
        $values[] = $final_qc_score;

        $values[] = $final_score;

        return $values;
    }

    public function headings(): array
    {
        $columns = ['کارشناس'];
        $columns[] = 'کد پرسنل';
        $columns[] = 'سوپروایزر';

        foreach($this->parameters as $parameter)
        {
            $columns[] = $parameter->name;
        }

        $columns[] = 'نمره کل سوپروایزر';

        $columns[] = 'نمره کل چت';
        $columns[] = 'تعداد کل چت';

        $columns[] = 'نمره کل تماس';
        $columns[] = 'تعداد کل تماس';

        $columns[] = 'نمره کل احراز';
        $columns[] = 'تعداد کل احراز';

        $columns[] = 'نمره کل خروجی';
        $columns[] = 'تعداد کل خروجی';

        $columns[] = 'نمره کل خطوط قرمز';

        $columns[] = 'نمره کل qc (بدون احتساب خط قرمز)';
        $columns[] = 'نمره کل qc (با احتساب خط قرمز)';
        
        $columns[] = 'نمره کل کارشناس';
        
        return $columns;

    }

    public function title(): string
    {
        return 'period';
    }
}
