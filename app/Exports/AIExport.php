<?php

namespace App\Exports;

use App\Models\AI_check;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class AIExport implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    public function __construct(public string $uuid)
    {
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return AI_check::
        where('uuid',$this->uuid)
        ->orderByDesc('id')
        ->take(1000)
        ->get();
    }

    public function map($item): array
    {
        $body = $item->body;
        $body = json_decode($body, true);
        $body = json_encode($body, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
        return [
            $item->chat_id,
            $body,
        ];
    }

    public function headings(): array
    {
        return [
            'chat_id',
            'output_json',
        ];
    }

    public function title(): string
    {
        return 'ai';
    }
}
