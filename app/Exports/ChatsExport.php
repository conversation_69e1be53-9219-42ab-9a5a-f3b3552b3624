<?php

namespace App\Exports;

use App\Models\Livechat;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class ChatsExport implements FromCollection, WithMapping, WithHeadings, WithTitle
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Livechat::
        whereNotNull('chat_rated')
        ->take(15000)
        ->get();
    }

    public function map($livechat): array
    {
        return [
            $livechat->platform,
            $livechat->thread_id,
            $livechat->chat_rated ? 'like' : 'dislike',
            $livechat->data,
            $livechat->started_at,
        ];
    }

    public function headings(): array
    {
        return [
            'platform',
            'chat_id',
            'chat_rated',
            'data',
            'started_at',
        ];
    }
    
    public function title(): string
    {
        return 'livechats';
    }
}
