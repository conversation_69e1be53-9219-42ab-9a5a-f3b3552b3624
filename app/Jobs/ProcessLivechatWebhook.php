<?php

namespace App\Jobs;

use App\Models\Chat_log;
use App\Models\Chat_transfer;
use App\Models\Inactive_archive;
use App\Models\Inactive_transfer;
use App\Models\Left_chat;
use App\Models\Livechat;
use App\Models\Lost_connection;
use App\Models\Missed_chat;
use App\Models\Queue_abandonment;
use App\Models\Sensitive_word;
use App\Models\Signed_out_transfer;
use App\Models\Take_over;
use App\Models\User;
use App\Social\Slack;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Throwable;
class ProcessLivechatWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $token = "Y2Q4MjlmMTEtMzAwOS00ZWVmLTk3YjMtNmQ1N2U3MmVkYzA5OmRhbDpfcWRtTGYzckU2X01qa3llUmVaOTZURlJaZXc=";    // base64
    private $model;
    public $tries = 2;
    /**
     * Create a new job instance.
     */
    public function __construct(public $livechat_id)
    {
        $this->onQueue('livechats');
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping(static::class)];
        // return [new WithoutOverlapping($this->livechat->id)];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $livechat = Livechat::find($this->livechat_id);
        $this->model = $livechat;
        
        if(is_null($livechat->data))
        {
            $response = $this->list_archives($livechat->thread_id,$livechat->platform);
            $livechat->data = $response->json();
            $data = $this->extract_data_from_response($response);
            $this->save_data($data);

            // sms
            ProcessTextMessage::dispatch($livechat->thread_id)->delay(now()->addMinutes(1));
        }

        // ai
        $list = Http::get('https://ai.busylance.ir/knowledge-base/prompts/list')->json();
        foreach($list as $uuid)
        {
            ProcessLivechatAutomation::dispatch($this->model->thread_id,$uuid)->delay(now()->addMinutes(1));
        }

        
    }

    private function list_archives($thread_id,$platform='wallex')
    {
        if($platform=='wallgold'){
            $this->token = "NTFjYjk0Y2QtYWIwNS00ZDJkLTgzZjUtZmMzOWUwMjcyNGI0OmRhbDpYNnZsTHJ6a2ZrT0NlMm5rY252MkRJanhOT0U=";
        }
        
        $url = "https://api.livechatinc.com/v3.5/agent/action/list_archives";

        $response = Http::withHeader('Authorization', "Basic " . $this->token)
        ->post($url,[
            'filters' => [
                'thread_ids' => [$thread_id]
            ] 
        ]);
        return $response;
    }

    public function extract_data_from_response($response)
    {
        $object = $response->object();
        // $found_chats = $object->found_chats;
        $chats = $object->chats;
        $chat = $chats[0];

        $agents = [];
        $transfers_count = 0;
        $customer_message_count = 0;
        $agent_message_count = 0;
        $customer_id = null;
        $users = [];
        
        foreach ($chat->users as $user)
        {
            $email = $user->email ?? '';    // wallex or wallgold
            if($user->type == 'agent' and str_contains($email,"@wall"))
            {
                $users [$user->name] = $email;
            }
            elseif($user->type == 'customer')
            {
                $customer_id = $user->id;
                $name_parts = explode(" ", $user->name);
            }
        }
        
        $name_parts[] = "آقای";
        $name_parts[] = "اقای";
        $name_parts[] = "خانم";
        $name_parts[] = "جناب";
        $name_parts[] = "سرکار";

        $how_to_scenarios = [
            "چطور میتونم کمکتون کنم",
            "چطور میتونم راهنماییتون کنم",
        ];

        $flag_words = [
            'اطلاعی ندارم',
            'همکارم شام هست',
            'همکارم نهار هست',
            'عرض کردم خدمتتون',
            'بالاتر گفتم',
            'شت کوین',
            'اوکی',
            'باشه',
            'آره',
            'نه',
            'عزیز',
            'گرامی',
            'ادبیات مناسب',
        ];

        $thread = $chat->thread;

        $user_ids = [];
        foreach($thread->user_ids as $thread_user_id)
        {
            if(str_contains($thread_user_id,'wall'))
            {
                $user_ids[] = $thread_user_id;
            }
        }

        $data['created_at'] = Carbon::parse($thread?->created_at)->timezone('Asia/Tehran');

        $events = $thread->events;

        $tags = $thread?->tags ?? [];
        
        $custom_variables = $thread->custom_variables ?? [];
        foreach ($custom_variables as $custom_variable)
        {
            if($custom_variable->key == 'user_id')
            {
                $admin_user_id = $custom_variable->value != '-' ? $custom_variable->value : null;
                $data['admin_user_id'] = $admin_user_id;
            }
        }

        $queued_at = $thread->queue->queued_at ?? null;
        $queues_duration = $thread->queues_duration ?? 0;
        $data['queues_duration'] = $queues_duration;

        $data['text'] = '';
        $first_agent_response = true;
        $pending_customer_question = true;
        $first_transfer = true;
        $first_response_datetime = null;
        $first_transfer_datetime = null;
        $transfer_datetime = null;
        $chat_ended_at = null;
        $last_message_sent_at = null;

        foreach($events as $event)
        {
            $created_at = $event->created_at ?? null;
            $form_type = $event->properties->lc2->form_type ?? null;
            $welcome_message = $event->properties->lc2->welcome_message ?? null;
            $type = $event->type ?? null;
            $system_message_type = $event->system_message_type ?? null;
            $author_id = $event->author_id ?? null;
            $custom_id = $event->custom_id ?? null;
            $initiator = $event->text_vars->initiator ?? null;
            $targets = $event->text_vars->targets ?? null;
            $content_type = $event->content_type ?? null;
            $agent_added = $event->text_vars->agent_added ?? null;
            $agent_removed = $event->text_vars->agent_removed ?? null;
            $agent = $event->text_vars->agent ?? null;
            $url = $event->url ?? null;
            // $template_id = $event->template_id ?? null;
            // $comment = $event->text_vars->comment ?? null;
            // $score = $event->text_vars->score ?? null;
            // $visibility = $event->visibility ?? null;       // all vs. agents
            // $client_id = $event->properties->source->client_id ?? null;

            $chatbot_ids = [
                env('wallex_chat_bot_id'),
                env('wallgold_chat_bot_id'),
                env('wallpay_chat_bot_id'),
                env('phinix_chat_bot_id'),
            ];

            $text = $event->text ?? '';
            if($text)
            {
                $data['text'] .= $text . "\n";
            }
            
            // Form or Filled form 
            if($form_type == 'prechat' or $type == 'filled_form'){continue;}
            elseif($form_type == 'postchat' or $type == 'filled_form'){continue;}
            // Rich message 
            elseif(str_starts_with($custom_id,"chatbot") or in_array($author_id,$chatbot_ids) or $type=='rich_message'){continue;}
            // System message
            elseif($type == 'system_message' and $system_message_type == 'customer_updated'){continue;}
            elseif($type == 'system_message' and $system_message_type == 'agent_left' and $agent)
            {
                Left_chat::create([
                    'livechat_id' => $this->model->id,
                    'agent' => $users[$agent],
                ]);
            }
            elseif($type == 'system_message' and $system_message_type == 'routing.assigned_other')
            {
                $transfer_datetime = Carbon::parse($created_at);
                $transfers_count = $transfers_count + 1;
            }
            elseif($type == 'system_message' and $system_message_type == 'manual_archived_agent')
            {
                $data['archived_method'] = $system_message_type;
                $chat_ended_at = Carbon::parse($created_at);
                if($transfer_datetime)
                {
                    $data['chat_duration'] = $chat_ended_at->diffInSeconds($first_transfer_datetime) - $queues_duration;
                }
            }
            elseif($type == 'system_message' and $system_message_type == 'routing.archived_inactive')
            {
                $data['archived_method'] = $system_message_type;
                $chat_ended_at = Carbon::parse($created_at);
                if($transfer_datetime)
                {
                    $data['chat_duration'] = $chat_ended_at->diffInSeconds($first_transfer_datetime) - $queues_duration;
                }

                if(isset($data['last_message_by']) and !str_contains($data['last_message_by'],"@wall"))
                {
                    Inactive_archive::create([
                        'livechat_id' => $this->model->id,
                    ]);
                }
                
            }
            elseif($type == 'system_message' and ($system_message_type == 'manual_archived_customer' or $system_message_type == 'archived_customer_disconnected' or $system_message_type == 'routing.archived_offline'))
            {
                $data['archived_method'] = $system_message_type;
                $chat_ended_at = Carbon::parse($created_at);
                if(isset($transfer_datetime))
                {
                    $data['chat_duration'] = $chat_ended_at->diffInSeconds($first_transfer_datetime) - $queues_duration;
                }

                if($queued_at)
                {
                    Queue_abandonment::create([
                        'livechat_id' => $this->model->id,
                    ]);
                }

                if(!is_null($last_message_sent_at) and $last_message_sent_at->diffInSeconds(Carbon::parse($created_at)) > 3*60)
                {
                    $data['chat_management'] = false;
                }
                
            }
            elseif($type == 'system_message' and $system_message_type == 'customer_banned')
            {
                $notification = "#customer_banned" . "\n";
                $notification .= "thread_id: " . $this->model->thread_id . "\n";
                $notification .= "agent: " . $agent;
                Slack::qc_alert($notification);
            }
            elseif($type == 'system_message' and $system_message_type == 'rating.chat_commented')
            {
                $data['comment'] = true;
            }
            elseif($type == 'system_message' and $system_message_type == 'rating.chat_rated')
            {
                $data['chat_rated'] = str_contains($text,'good') ? true : false;
            }
            elseif($type == 'system_message' and $system_message_type == 'rating.chat_rating_canceled')
            {
                $data['chat_rated'] = null;
            }
            elseif($type == 'system_message' and $system_message_type == 'chat_transferred')
            {
                $transfer_datetime = Carbon::parse($created_at);
                $transfers_count = $transfers_count + 1;

                if(($initiator == 'Wallex' or $initiator == 'Wallpay' or $initiator == 'Wallgold' )  and $first_transfer)
                {
                    $first_transfer = false;
                    $first_transfer_datetime = $transfer_datetime;
                }
                else
                {
                    if($initiator == $targets)
                    {
                        Chat_transfer::create([
                            'livechat_id' => $this->model->id,
                            'initiator' => $users[$initiator],
                            'targets' => $users[$targets],
                        ]);
                    }
                    else
                    {
                        Take_over::create([
                            'livechat_id' => $this->model->id,
                            'initiator' => $users[$initiator],
                            // 'targets' => $users[$targets],
                        ]);
                    }
                }
            }
            elseif($type == 'system_message' and $system_message_type == 'routing.assigned_disconnected' and $agent_added and $agent_removed)
            {
                $transfer_datetime = Carbon::parse($created_at);
                $transfers_count = $transfers_count + 1;

                Lost_connection::create([
                    'livechat_id' => $this->model->id,
                    'agent_added' => $users[$agent_added] ?? null,
                    'agent_removed' => $users[$agent_removed],
                ]);
            }
            elseif($type == 'system_message' and $system_message_type == 'routing.unassigned_disconnected' and $agent)
            {
                Lost_connection::create([
                    'livechat_id' => $this->model->id,
                    'agent_added' => null,
                    'agent_removed' => $users[$agent],
                ]);
            }
            elseif($type == 'system_message' and $system_message_type == 'routing.assigned_inactive' and $agent_added and $agent_removed)
            {
                $transfer_datetime = Carbon::parse($created_at);
                $transfers_count = $transfers_count + 1;

                Inactive_transfer::create([
                    'livechat_id' => $this->model->id,
                    'agent_added' => $users[$agent_added],
                    'agent_removed' => $users[$agent_removed],
                ]);
            }
            elseif($type == 'system_message' and $system_message_type == 'routing.assigned_signed_out')
            {
                $transfer_datetime = Carbon::parse($created_at);
                $transfers_count = $transfers_count + 1;

                Signed_out_transfer::create([
                    'livechat_id' => $this->model->id,
                    'agent_added' => $users[$agent_added],
                    'agent_removed' => $users[$agent_removed],
                ]);
            }
            elseif($welcome_message){continue;}

            // File 
            // Message 
            elseif($type == 'message' or $type == 'file')
            {
                if($type=='file')
                {
                    $data['file'] = true;
                }

                if($url and $content_type=='image/svg+xml')
                {
                    Slack::qc_notification("SVG file!\n"."https://my.livechatinc.com/archives/".$this->model->thread_id);
                }

                if(str_contains($author_id,"@wall"))
                {
                    $agent_message_count = $agent_message_count + 1;
                    array_push($agents,$author_id);

                    if($first_agent_response)
                    {
                        $first_agent_response = false;
                        $data['author_id'] = $author_id;
                        $first_response_datetime = Carbon::parse($created_at);
                        if(isset($transfer_datetime) and $queues_duration == 0)
                        {
                            $data['FRT'] = $first_response_datetime->diffInSeconds($transfer_datetime);
                        }
                        if($pending_customer_question and Str::contains($text,$how_to_scenarios) and str_contains($text,"سلام"))
                        {
                            $data['start_scenario'] = true;
                        }
                    }

                    if(str_contains($text,"نظر خود را"))
                    {
                        // نظرتون را
                        // نظر سنجی 
                        // نظرسنجی
                        $data['survey'] = true;
                    }

                    if(Str::contains($text,$flag_words))
                    {
                        $data['flagged'] = true;
                    }

                    foreach($name_parts as $name_part)
                    {
                        if(str_contains($text,$name_part) and strlen($name_part) > 2 and $name_part!='-')
                        {
                            $data['called_by_name'] = true;
                        }
                    }

                    if(!is_null($last_message_sent_at) and $last_message_sent_at->diffInSeconds(Carbon::parse($created_at)) > 3*60)
                    {
                        $data['chat_management'] = false;
                    }
                    $last_message_sent_at = Carbon::parse($created_at);
                }
                else
                {
                    $customer_message_count = $customer_message_count + 1;

                    if($pending_customer_question)
                    {
                        $pending_customer_question = false;
                    }
                    
                    if(!isset($data['last_message_by']) or $data['last_message_by'] != $author_id)
                    {
                        $last_message_sent_at = Carbon::parse($created_at);
                    }
                }

                $data['last_message_by'] = $author_id;                
                
            }
            // Custom 
            else
            {
                
            }
            
        }

        if($transfer_datetime and is_null($first_response_datetime) and is_null($queued_at))
        {
            if($data['chat_duration'] > 30)
            {
                $missed_chat = Missed_chat::create([
                    'livechat_id' => $this->model->id,
                ]);
            }

            if(count($user_ids)==1)
            {
                $missed_by = User::where('email',reset($user_ids))?->first();
                $slack_id = $missed_by?->slack_id;
                if($slack_id and $data['chat_duration'] > 30)
                {
                    $missed_chat->user_id = $missed_by->id;
                    $missed_chat->save();

                    Slack::pv("Missed chat: {$this->model->thread_id}",$slack_id);
                }
            }
            
        }

        if($data['text'])
        {
            $sensitivity = true;
            
            $insensitive_words = Sensitive_word::where('alert',true)->where('mode','excludes')->get();
            foreach($insensitive_words as $insensitive_word)
            {
                if(str_contains($data['text'],$insensitive_word->value))
                {
                    $sensitivity = false;
                    break;
                }
            }

            if($sensitivity)
            {
                $notify = '';
                $sensitive_words = Sensitive_word::where('alert',true)->where('mode','includes')->get();
                foreach($sensitive_words as $sensitive_word)
                {
                    if(str_contains($data['text'],$sensitive_word->value))
                    {
                        $notify .= trim($sensitive_word->value) . " : " . substr_count($data['text'],$sensitive_word->value) . "\n";
                        $critical = $sensitive_word->type == 'critical';
                    }
                }

                if(strlen($notify)>1)
                {
                    $notification = "#" . $this->model->platform . "\n";
                    $notification .= $critical ? "#Sensitive_words\n" : "#Social\n";

                    $notification .= $notify;
                    $notification .= "https://my.livechatinc.com/archives/" . $this->model->thread_id;

                    if($this->model->platform == 'wallgold')
                    {
                        if(now()->hour<16) {
                            $notification .= "\n<!subteam^S08E1C9EYVA>";
                        } else {
                            $notification .= "\n<!subteam^S08EMS39H5W>";
                        }
                    }
                    
                    if($critical) {
                        Slack::qc_notification($notification);
                    }
                    else {
                        Slack::qc_social($notification);
                    }
                }
            }
        }

        $data['agent_message_count'] = $agent_message_count;
        $data['customer_message_count'] = $customer_message_count;

        $data['agents_count'] = count(array_unique($agents));
        $data['transfers_count'] = $transfers_count;

        $data['offline'] = in_array('offline',$tags);

        if($data['offline'])
        {
            Slack::operation('new offline chat found: https://my.livechatinc.com/archives/' . $this->model->thread_id,$this->model->platform);
        }

        $data['api'] = in_array('api',$tags);
        $data['ai'] = in_array('ai',$tags);

        // all chats should at least have a chatbot tag
        $data['started_by_agent'] = count($tags)==0;    

        if($data['api'])
        {
            Slack::ticket_alert('new api ticket created: https://my.livechatinc.com/archives/' . $this->model->thread_id);
        }
        $data['chatbot'] = !in_array('chatbot-transfer',$tags);

        $data['customer_id'] = $customer_id;

        return $data;
    }
    
    private function save_data($data)
    {
        $this->model->FRT = $data['FRT'] ?? null;
        $this->model->author_id = $data['author_id'] ?? null;
        $this->model->last_message_by = $data['last_message_by'] ?? null;
        $this->model->chat_duration = $data['chat_duration'] ?? null;
        $this->model->called_by_name = $data['called_by_name'] ?? null;
        $this->model->survey = $data['survey'] ?? null;
        $this->model->start_scenario = $data['start_scenario'] ?? null;
        $this->model->chat_management = $data['chat_management'] ?? null;
        $this->model->admin_user_id = $data['admin_user_id'] ?? null;

        $this->model->flagged = $data['flagged'] ?? null;

        $this->model->queues_duration = $data['queues_duration'];
        $this->model->agents_count = $data['agents_count'];
        $this->model->transfers_count = $data['transfers_count'];
        $this->model->agent_message_count = $data['agent_message_count'];
        $this->model->customer_message_count = $data['customer_message_count'];

        $this->model->offline = $data['offline'] ?? null;
        $this->model->ticket = $data['api'] ?? null;
        $this->model->chatbot = $data['chatbot'] ?? null;
        $this->model->ai = $data['ai'] ?? null;
        
        $this->model->comment = $data['comment'] ?? null;
        $this->model->file = $data['file'] ?? null;
        $this->model->chat_rated = $data['chat_rated'] ?? null;
        $this->model->started_by_agent = $data['started_by_agent'] ?? null;

        $this->model->customer_id = $data['customer_id'] ?? null;

        $this->model->started_at = $data['created_at'] ?? null;

        $this->model->archived_method = $data['archived_method'] ?? null;

        $this->model->save();

        $counts = Livechat::where('created_at','>=',now()->subDay())
        ->where('customer_id',$this->model->customer_id)
        ->count();

        if($counts>=4)
        {
            $text = "";
            $text .= "platform: {$this->model->platform}\n";
            $text .= "count: {$counts}\n";
            $text .= "https://my.livechatinc.com/archives/{$this->model->thread_id}";
            Slack::cx_unit($text);
        }

        if($data['ai'])
        {
            
            $chat_logs = Chat_log::where('default_chat_id',$this->model->thread_id)->get();
    
            $text = "";

            foreach($chat_logs as $chat_log)
            {
                // $text .= "کاربر: " . $chat_log->message . "\n";
                // $text .= "دستیار هوشمند: " . $chat_log->response . "\n\n";
                $text .= "کاربر: " . $chat_log->question . "\n";
                $text .= "دستیار هوشمند: " . $chat_log->answer . "\n\n";
            }

            $text .= "https://my.livechatinc.com/archives/{$this->model->thread_id}";

            Http::post('*********************************************************************************',[
                'text'=>$text,
            ]);
            
        }
    }

    public function failed(?Throwable $exception): void
    {
        Slack::services_monitoring("Failed job: {$this->livechat_id}");
    }
}
