<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;

class ChangeCacheArray implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    
    /**
     * Create a new job instance.
     */
    public function __construct(public string $action,public string $key,public mixed $value = null)
    {
        //
    }

    public function middleware(): array
    {
        // Atomic Locks
        return [new WithoutOverlapping(static::class)];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $online_chat_list = cache('online_chat_list',[]);

        switch($this->action)
        {
            case 'remove':
                unset($online_chat_list[$this->key]);
            break;

            case 'add':
                $online_chat_list[$this->key] = $this->value;
            break;
        }

        cache(['online_chat_list'=>$online_chat_list]);
    }
}
