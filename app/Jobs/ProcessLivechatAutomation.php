<?php

namespace App\Jobs;

use App\Models\AI_check;
use App\Models\Livechat;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class ProcessLivechatAutomation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // ai

    /**
     * Create a new job instance.
     */
    public function __construct(public $thread_id,public $uuid)
    {
        $this->onQueue('automation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $livechat = Livechat::where('thread_id',$this->thread_id)->first();
        
        $url = "https://ai.busylance.ir/api/token/";
        $response = Http::
        post($url,[
            'username'=>env('ai_username'),
            'password'=>env('ai_password'),
        ]);
        $access_token = $response->object()->access;

        $url = "https://ai.busylance.ir/api/ollama/uuid/";

        $text = $this->get_text_from_chat($livechat);
        if($text=='')
        {
            return;
        }

        $response = Http::
        timeout(120)
        ->withHeaders(
            [
                'Authorization'=>"Bearer " . $access_token,
            ]
        )
        ->asMultipart()
        ->post($url,[
            'uuid' => $this->uuid,
            'user_message' => $text,
        ]);

        $body = $response->object()->message->content;
        $body = trim(str_replace(['```json','```'],'',$body));
        
        $body = json_decode($body, true);
        $body = json_encode($body);

        AI_check::create([
            'chat_id'=>$this->thread_id,
            'uuid'=>$this->uuid,
            'body'=>$body,
        ]);
    }

    public function get_text_from_chat($livechat)
    {
        $chatbot_ids = [
            env('wallex_chat_bot_id'),
            env('wallgold_chat_bot_id'),
            env('wallpay_chat_bot_id'),
            env('phinix_chat_bot_id'),
        ];
        $response = json_decode($livechat->data,true);
        $events = $response['chats'][0]['thread']['events'];
        $text = '';
        foreach($events as $event)
        {
            if($event['type']=='message')
            {
                if(str_contains($event['author_id'],'@wall')) {
                    $text .= "کارشناس:" . $event['text'] . "\n";
                } elseif (in_array($event['author_id'],$chatbot_ids)) {
                    $text .= "دستیار هوشمند:" . $event['text'] . "\n";
                } else {
                    $text .= "کاربر:" . $event['text'] . "\n";
                }
            }
        }
        return $text;
    }
}
