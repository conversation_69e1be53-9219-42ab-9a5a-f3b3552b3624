<?php

namespace App\Jobs;

use App\Models\Livechat;
use App\Models\TextMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class ProcessTextMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    // پیامک نظر سنجی

    /**
     * Create a new job instance.
     */
    public function __construct(public $thread_id)
    {
        $this->onQueue('sms');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $livechat = Livechat::where('thread_id',$this->thread_id)->first();
        $response = json_decode($livechat->data,true);
        $thread = $response['chats'][0]['thread'];

        // $national_code = $this->extract_national_code($thread);
        
        if($this->has_participated($thread))
        {
            return;
        }

        $answer = $this->extract_phone_number($thread);
        $phone_number = $this->convert_to_english_numbers($answer);
        $is_valid = $this->is_valid_phone_number($phone_number);
        
        if(!$is_valid)
        {
            return;
        }
        
        if($this->has_already_sent($phone_number))
        {
            return;
        }

        // $this->send_sms($phone_number);
        // TextMessage::create([
        //     'phone_number'=>$phone_number,
        // ]);
    }

    public function send_sms($phone_number)
    {
        $api_key = env('kavenegar_api');
        $sender = env('kavenegar_sender');

        $message = "سلام، جهت بهبود خدمات لطفا در نظرسنجی شرکت کنید\nتیم پشتیبانی والکس";
        $url = "https://api.kavenegar.com/v1/{$api_key}/sms/send.json";
        Http::
            timeout(30)
            ->get($url,
            [
                'receptor'=>urlencode($phone_number),
                'message'=>urlencode($message),
                'sender'=>urlencode($sender),
            ])->json();
    }

    public function is_valid_phone_number($phone_number)
    {
        if(ctype_digit($phone_number) and str_starts_with($phone_number,'09') and strlen($phone_number)==11)
        {
            return true;
        }
        return false;
    }

    public function convert_to_english_numbers($persian_numbers)
    {
        $persianDigits = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
        $arabicDigits  = ['٠','١','٢','٣','٤','٥','٦','٧','٨','٩'];
        $englishDigits = ['0','1','2','3','4','5','6','7','8','9'];

        $string = str_replace($persianDigits, $englishDigits, $persian_numbers);
        $string = str_replace($arabicDigits, $englishDigits, $string);

        return $string;
    }

    public function extract_national_code($thread)
    {
        $national_code = null;
        $custom_variables = $thread['custom_variables'];
        foreach($custom_variables as $custom_variable)
        {
            if($custom_variable['key']=='national_code')
            {
                $national_code = $custom_variable['value'];
            }
        }
        return $national_code;
    }

    public function extract_phone_number($thread)
    {
        $events = $thread['events'];
        foreach($events as $event)
        {
            if($event['type']=='filled_form')
            {
                $fields = $event['fields'];
                foreach($fields as $field)
                {
                    if(in_array($field['id'],['174876380736206121','17391927791290178']))   // شماره موبایل حساب کاربری
                    {
                        return $field['answer'];
                    }
                }
            }
        }
    }

    public function has_already_sent($phone_number)
    {

        $count = TextMessage::where('phone_number',$phone_number)
        ->where('created_at','>=',now()->subDay())
        ->count();

        return !!!$count;
    }

    public function has_participated($thread)
    {
        $events = $thread['events'];
        foreach($events as $event)
        {
            // if($event['type']=='system_message' and $event['system_message_type']=='rating.chat_rated')
            // {
            //     return true;
            // }
            
            // if($event['type']=='system_message' and $event['system_message_type']=='rating.chat_commented')
            // {
            //     return true;
            // }

            if($event['type']=='filled_form' and $event['form_type']=='postchat')
            {
                return true;
            }
        }
        return false;
    }
}
