<?php

namespace App\Jobs;

use App\Models\Slack_channel;
use App\Models\Slack_message;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\Middleware\WithoutOverlapping;

class CrawlSlackChannel implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // announcement bot
    public $api = "*********************************************************";

    
    public $channel;
    public $limit = 5;   // max

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->channel = Slack_channel::where('enabled',true)->first();
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping($this->channel->id)];
    }

    public function process_messages_or_replies($messages_or_replies)
    {
        $list = [];
        foreach ($messages_or_replies as $messages_or_reply)
        {
            if($this->should_continue($messages_or_reply))
            {
                continue;
            }
            $list[] = $this->process_message_or_reply($messages_or_reply);
        }

        return $list;

        // foreach($list as &$item)
        // {
        //     $item['updated_at'] = now();
        //     $item['created_at'] = now();
        // }
    }

    public function should_continue($message_or_reply)
    {
        $subtype = $message_or_reply?->subtype ?? null;
        // thread_broadcast
        return ($subtype == 'bot_message' or $subtype == 'tombstone' or $subtype=='channel_join' or $subtype == 'bot_add');
    }

    public function process_message_or_reply($message)
    {
            $item = [];

            $item['channel_id'] = $this->channel->id;

            // $username = $message?->username ?? null;
            // $bot_profile = $message?->bot_profile ?? null;
            // $bot_id = $message?->bot_id ?? null;
            // $app_id = $message?->app_id ?? null;
            // $trigger_id = $message?->trigger_id ?? null;
            // $display_as_bot = $message?->display_as_bot ?? null;
            
            // $client_msg_id = $message?->client_msg_id ?? null;
            // $type = $message?->type ?? 'message';

            $item['user'] = $message->user ?? null;
            // $parent_user_id = $message?->parent_user_id ?? null;

            $item['ts'] = $message->ts ?? null;

            $item['files'] = property_exists($message,'files');
            // $upload = $message?->upload ?? null;
            // $blocks = $message?->blocks ?? [];
            // $reactions = $message?->reactions ?? [];

            $item['text'] = (isset($message?->text) and strlen($message?->text)>0) ? $message?->text : null;
            
            // $team = $message?->team ?? null;    // T01K6BX1S87

            // $thread_ts = $message?->thread_ts ?? null;  // parent_ts
            // $root = $message?->root ?? null;    // parent_message
            $item['reply_count'] = $message->reply_count ?? 0;
            // $latest_reply = $message?->latest_reply ?? null;
            // $reply_users_count = $message?->reply_users_count ?? 0;
            // $reply_users = $message?->reply_users ?? [];

            // $is_locked = $message?->is_locked ?? null;
            // $subscribed = $message?->subscribed ?? null;
            return $item;
    }
    
    public function get_messages($after=null,$before=null,$cursor=null)
    {
        $body = [
            'channel'=>$this->channel->channel_id,
            'limit'=>$this->limit,
        ];

        if($after)
        {
            $body['oldest'] = $after;
        }

        if($before)
        {
            $body['latest'] = $before;
        }

        if($cursor)
        {
            $body['cursor'] = $cursor;
        }

        $response = Http::withToken($this->api)
        ->get('https://slack.com/api/conversations.history',$body)
        ->object();
        return $response;
    }

    public function get_thread_replies($ts)
    {
        $response = Http::withToken($this->api)
        ->get('https://slack.com/api/conversations.replies',[
            'channel'=>$this->channel->channel_id,
            'ts'=>$ts,
        ])->object();
        // $has_more = $response->has_more;
        return array_slice($response->messages, 1);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if($this->channel->crawling_direction == 'down') {
            $object = $this->get_messages(after:$this->channel->lastest_crawled_message);
        } else {
            $object = $this->get_messages(before:$this->channel->oldest_crawled_message);
        }
        
        // $has_more = $object?->has_more;
        // $pin_count = $object->pin_count;
        // $channel_actions_ts = $object->channel_actions_ts;
        // $channel_actions_count = $object->channel_actions_count;
        // $warnings = $object->warnings;
        // $next_cursor = $object->response_metadata?->next_cursor ?? null;

        $messages = $object->messages ?? [];
        
        if($messages)
        {
            $this->channel->oldest_crawled_message = is_null($this->channel->oldest_crawled_message) ? end($messages)->ts : min($this->channel->oldest_crawled_message,end($messages)->ts);

            $this->channel->lastest_crawled_message = is_null($this->channel->lastest_crawled_message) ? $messages[0]->ts : max($this->channel->lastest_crawled_message,$messages[0]->ts);
        }

        DB::transaction(function () use ($messages) {
            $list = $this->process_messages_or_replies($messages);    
            Slack_message::insertOrIgnore($list);
            $this->channel->save();
        });

        $slack_message = Slack_message::where('reply_count','>',0)
        ->whereNull('thread_crawled_at')
        ->first();

        if($slack_message)
        {
            $replies = $this->get_thread_replies($slack_message->ts);
            $list = $this->process_messages_or_replies($replies);
            $slack_message->thread_crawled_at = now();
            $slack_message->save();
        }

        self::dispatch()->delay(now()->addSeconds(30));
    }
}
