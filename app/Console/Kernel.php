<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        // $schedule->command('app:otc')->everyFiveMinutes()->withoutOverlapping();

        // $schedule->command('app:check-f1')->hourly();
        $schedule->command('app:check-f1')->dailyAt('08:00');
        $schedule->command('app:check-f1')->dailyAt('16:00');
        $schedule->command('app:check-f1')->dailyAt('23:00');

        // $schedule->command('app:long-chat')->everyFiveMinutes();
        $schedule->command('app:okr')->dailyAt('09:00');

        $schedule->command('app:chat-monitor')->everyThirtyMinutes();
        // $schedule->command('check:n8n')->hourly();

    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
