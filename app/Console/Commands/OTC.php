<?php

namespace App\Console\Commands;

use App\Social\Slack;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class OTC extends Command
{
    
        
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:otc';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $old = cache('otc_price', []);

        $otc_list = Http::acceptJson()
        ->get('https://api.wallex.ir/v1/otc/markets')
        ->collect('result');

        $batchSize = 50;
        $batches = $otc_list->chunk($batchSize);
        $text = '';
        $arbitrage_text = '';
        
        $otc_price = [];
        
        $spot = $this->get_spot_prices();
        if(count($spot)==0) { return; }
        
        foreach ($batches as $batch)
        {
            $responses = Http::pool(function ($pool) use ($batch) {

                $requests = [];
                foreach ($batch as $otc_item)
                {
                    $symbol = $otc_item['symbol'];
                    $requests[] = $this->get_price_pool($pool,$symbol,"BUY");
                    $requests[] = $this->get_price_pool($pool,$symbol,"SELL");
                }
                return $requests;
            });

            foreach($batch as $otc_item)
            {
                $symbol = $otc_item['symbol'];
    
                $buyResponse = $responses["{$symbol}_BUY"];
                $sellResponse = $responses["{$symbol}_SELL"];
    
                $buy = $buyResponse->successful() ? $buyResponse->collect('result.price')[0] ?? null : null;
                $sell = $sellResponse->successful() ? $sellResponse->collect('result.price')[0] ?? null : null;
    
                $diff = ($buy and $sell) ? round($buy / $sell * 100 - 100,2) : null;
    
                $otc_price[$symbol]['BUY'] = $buy;
                $otc_price[$symbol]['SELL'] = $sell;

                $otc_price[$symbol]['diff'] = $diff;
                $otc_price[$symbol]['checked_at'] = now();
                
                $last_alert_at = $old[$symbol]['last_alert_at'] ?? null;
                $otc_price[$symbol]['last_alert_at'] = $last_alert_at;

                // otc 
                if( ( $diff> 8 ) and (is_null($last_alert_at) or now()->diffInMinutes($last_alert_at) > 59))
                {
                    $otc_price[$symbol]['last_alert_at'] = now();
                    $text .= "Buy/Sell ratio of {$symbol} is {$diff}% [{$buy} vs. {$sell}]\n";
                }
                
                // spot
                if(isset($spot[$symbol]['BID']) and !is_null($buy) and $spot[$symbol]['BID'] > $buy)
                {
                    $difference = abs($spot[$symbol]['BID'] - $buy) / (($spot[$symbol]['BID'] + $buy)/2);
                    $difference *= 100;

                    if(($difference > 0.2 and str_ends_with($symbol,'USDT')) or ($difference > 0.3 and str_ends_with($symbol,'TMN')))
                    {
                        $difference = round($difference,2);
                        $arbitrage_text .= "{$symbol} OTC buy : $buy vs. spot bid : {$spot[$symbol]['BID']} ({$difference}% difference)\n";
                    }
                }
                
            }
        }
        
        $seconds = 61*60;
        cache(['otc_price' => $otc_price], $seconds);
        
        if($text){ Slack::otc_alert($text); }
        
        if($arbitrage_text) { Slack::otc_spot_alert($arbitrage_text); }

    }
    

    public function get_price_pool($pool,$symbol,$side)
    {
        return $pool->as("{$symbol}_{$side}")->acceptJson()
        ->withHeader("X-API-Key",env('WALLEX_X_API_KEY'))
        ->get('https://api.wallex.ir/v1/account/otc/price',[
            'symbol'=>$symbol,
            'side'=>$side,
        ]);
    }

    public function get_spot_prices()
    {
        // $old = cache('spot_price', []);
        $response = Http::get('https://api.wallex.ir/v1/markets');
        $symbols = [];
        if($response->successful())
        {
            $object = $response->object();
            $symbols = $object->result->symbols;
        }
        
        $spot_price = [];
        foreach ($symbols as $key => $item)
        {
            // bidPrice < askPrice
            $spot_price[$key]['ASK'] = $item->stats->askPrice;
            $spot_price[$key]['BID'] = $item->stats->bidPrice;
            
            // $last_alert_at = $old[$key]['last_alert_at'] ?? null;
            // $spot_price[$key]['last_alert_at'] = $last_alert_at;
        }
        // $seconds = 61*60;
        // cache(['spot_price' => $spot_price], $seconds);
        return $spot_price;
    }
}
