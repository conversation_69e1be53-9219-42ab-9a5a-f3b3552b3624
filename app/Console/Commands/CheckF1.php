<?php

namespace App\Console\Commands;
use App\Social\Slack;
use App\Models\F1_widget;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
class CheckF1 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-f1';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $list = [];
        $result = Http::get('https://api.wallex.ir/v1/chat/widget')->collect()['result'];
        $this->map_children($result,$list);

        $text = '';
        foreach($list as $item)
        {
            $text .= $item['announcement'] . "\n";
        }

        if($text)
        {
            $text = "اطلاعیه های فعال F1 عبارت اند از:\n\n" . $text;
            Slack::operation($text,'wallex');
        }

        // F1_widget::whereNotIn('f1_id',collect($list)->pluck('id')->toArray())->delete();

        // foreach ($list as $item)
        // {
        //     F1_widget::firstOrCreate(
        //         ['f1_id'=>$item['id']],
        //         ['announcement' => $item['announcement']]
        //     );
        // }

        // $announcements = F1_widget::where('created_at','<=',now()->subDay())
        // ->where(function($q){
        //     $q->whereNull('last_notification_at')
        //     ->orWhere('last_notification_at','<=',now()->subDay());
        // })
        // ->get();

        // foreach($announcements as $announcement)
        // {
        //     $text = "با گذشت 24 ساعت اطلاعیه زیر همچنان در F1 وجود دارد:" . "\n" . $announcement->announcement;
        //     Slack::operation($text,'wallex');
        //     $announcement->last_notification_at = now();
        //     $announcement->save();
        // }
    }

    public function map_children($result,&$list)
    {

        $exclude_list = [
            'به منظور واریز کارت به کارت،‌ کارت مورد نظر باید در حساب والکس شما ثبت شده باشد و به نام صاحب حساب باشد',
            'ارائه خدمات واریز ریالی به پلتفرم‌های رمزارزی، از جمله والکس، با محدودیت مواجه شده است',
            'پیرو فرایند تبدیل توکن ایاس به توکن والتا',
            'والکس از ابتدای فعالیت خود کلیه زیرساخت های لازم جهت حفظ و نگهداری دارایی کاربران را فراهم کرده است',
            'با توجه به تغییر شماره شباهای بانک تجارت، لطفاً برای واریز ریالی از این بانک، ابتدا شبای جدید خود را از شعبه دریافت کرده و در حساب والکس ثبت نمایید',
        ];

        $announcement = $result['data']['announcement'];
        $id = $result['id'];
        if($announcement)
        {
            $bool = true;
            foreach($exclude_list as $exclude_item) {
                if(str_contains($announcement,$exclude_item)){$bool=false;}
            }
            if($bool) {
                $list[]=[
                'id'=>$id,
                'announcement'=>$announcement,
                ];
            }
            
        }
        foreach($result['children'] as $item)
        {
            $this->map_children($item,$list);
        }
    }
}
