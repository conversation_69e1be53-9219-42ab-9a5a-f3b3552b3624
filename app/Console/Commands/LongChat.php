<?php

namespace App\Console\Commands;

use App\Jobs\ChangeCacheArray;
use App\Social\Slack;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
class LongChat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:long-chat';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $online_chat_list = Cache::get('online_chat_list',[]);
        foreach($online_chat_list as $chat_id_thread_id => $started)
        {
            if( $started->diffInMinutes(now()) > 60 ) {
                ChangeCacheArray::dispatch('remove',$chat_id_thread_id);
                $text = "این چت بیشتر از 60 دقیقه تا الان طول کشیده و هنوز باز است (البته ممکن است بخشی از آن queue باشد):\nhttps://my.livechatinc.com/chats/{$chat_id_thread_id}";
                // Slack::supervisor_action($text);
            }
        }
        Cache::put('online_chat_list',$online_chat_list);
    }
}
