<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Social\Slack;

class CheckN8N extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:n8n';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
                $status = 'nok';
        try {
            $status = Http::get('https://n8n.wallex.support/healthz/readiness')->json()['status'];
        } catch(\Exception $e) {}
        
        if($status!='ok')
        {
            Slack::services_monitoring('n8n is down!');
        }
    }
}
