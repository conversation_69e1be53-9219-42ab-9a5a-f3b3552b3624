<?php

namespace App\Console\Commands;

use App\Models\Goftino_chat;
use App\Models\Livechat;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Social\Slack;
use Carbon\Carbon;

class ChatMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:chat-monitor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */

    public function handle()
    {
        // $this->livechat_checkup('wallgold');
        // $this->livechat_checkup('wallex');

        // $this->livechat_checkup_status('wallgold',60);
        // $this->livechat_checkup_status('wallex',15);

        $this->goftino_checkup('wallgold');
        $this->goftino_checkup('wallex');

        $this->goftino_checkup_status('wallgold',60);
        $this->goftino_checkup_status('wallex',15);
    }

    public function livechat_checkup_status($platform,$minutes)
    {
        $count = Livechat::
        where('platform',$platform)
        ->where('created_at','>',now()->subMinutes($minutes))
        ->count();
        if($count == 0)
        {
            Slack::okr("در {$minutes} دقیقه اخیر هیچ چتی در سایت qc ثبت نشده است (اختلال یا خلوتی)",$platform);
        }
    }

    public function livechat_checkup($platform)
    {
        $key = now()->toDateString() . "-chat-surpass";
        $bool = Cache::get($key,false);
        if($bool)
        {
            return;
        }

        $yesterday = Livechat::
        where('platform',$platform)
        ->whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->count();

        $today = Livechat::
        where('platform',$platform)
        ->whereBetween('created_at',[Carbon::yesterday()->endOfDay(),now()])
        ->count();
        
        if($today>$yesterday)
        {
            Cache::put($key,true);
            Slack::okr('تعداد چت های امروز تا این لحظه از تعداد کل چت های دیروز پیشی گرفت',$platform);
        }
    }

    public function goftino_checkup($platform)
    {
        $key = now()->toDateString() . "-chat-surpass";
        $bool = Cache::get($key,false);
        if($bool)
        {
            return;
        }

        $yesterday = Goftino_chat::whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        })
        ->whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->count();

        $today = Goftino_chat::
        whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        })
        ->whereBetween('created_at',[Carbon::yesterday()->endOfDay(),now()])
        ->count();
        
        if($today>$yesterday)
        {
            Cache::put($key,true);
            Slack::okr('تعداد چت های امروز تا این لحظه از تعداد کل چت های دیروز پیشی گرفت',$platform);
        }

    }

    public function goftino_checkup_status($platform,$minutes)
    {
        
        if($platform=='wallgold' and now()->hour()<8 and now()->hour()>=0)
        {
            return;
        }

        if($platform=='wallgold' and now()->isFriday())
        {
            return;
        }
        
        $count = Goftino_chat::whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        })
        ->where('created_at','>',now()->subMinutes($minutes))
        ->count();
        
        if($count == 0)
        {
            Slack::okr("در {$minutes} دقیقه اخیر هیچ چتی در سایت qc ثبت نشده است (اختلال یا خلوتی)",$platform);
        }
    }
}
