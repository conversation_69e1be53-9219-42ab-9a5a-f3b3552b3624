<?php

namespace App\Console\Commands;

use App\Models\Goftino_assignment;
use App\Models\Goftino_chat;
use App\Models\Goftino_closed_chat;
use App\Models\Goftino_data;
use App\Models\Goftino_transfer;
use App\Models\Goftino_unassigned_chat;
use App\Models\Livechat;
use App\Social\Slack;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
class OKR extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:okr';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // $this->checkup_livechat('wallgold');
        // $this->checkup_livechat('wallex');

        $this->checkup_goftino('wallgold');
        $this->checkup_goftino('wallex');
    }

    public function checkup_livechat($platform)
    {
        $livechats = Livechat::
        whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->where('platform',$platform);

        $count = $livechats->clone()->count();
        $count_threshold = $livechats->clone()->where('chat_duration','>',30*60)->count();
        $count_assigned = $livechats->clone()->whereNotNull('author_id')->count();

        $count_offline = $livechats->clone()->where('offline','=',true)->count();
        $count_ai = $livechats->clone()->where('ai','=',true)->count();
        $count_ticket = $livechats->clone()->where('ticket','=',true)->count();
        $count_not_transferred = $livechats->clone()->where('chatbot','=',true)->count();

        $count_unique = $livechats->clone()->distinct('customer_id')->count('customer_id');
        
        $count_archived_by_agent = $livechats->clone()->where('archived_method','manual_archived_agent')
        ->where('ticket',false)
        ->where(function($q){
            $q->where('survey','<>',true)
            ->orWhereNull('survey');
        })
        ->count();
        
        $count_comment = $livechats->clone()->where('comment',true)->count();
        $count_good = $livechats->clone()->where('chat_rated',true)->count();
        $count_bad = $livechats->clone()->where('chat_rated',false)->count();

        $aht = $livechats->clone()
        ->where('chat_duration','>',0)
        ->average('chat_duration');
        $aht = round($aht,2);

        $min_chat_duration = $livechats->clone()
        ->where('chat_duration','>',0)
        ->min('chat_duration');
        $min_chat_duration = round($min_chat_duration,2);

        $max_chat_duration = $livechats->clone()
        ->where('chat_duration','>',0)
        ->max('chat_duration');
        $max_chat_duration = round($max_chat_duration,2);

        // frt
        $min_frt =  $livechats->clone()
        ->where('FRT','>',0)
        ->min('FRT');
        $min_frt = round($min_frt,2);

        $max_frt = $livechats->clone()
        ->where('FRT','>',0)
        ->max('FRT');
        $max_frt = round($max_frt,2);

        $average_frt = $livechats->clone()
        ->where('FRT','>',0)
        ->average('FRT');
        $average_frt = round($average_frt,2);

        // queue
        $queue_count = $livechats->clone()->where('queues_duration','>',0)->count();

        $max_queue = $livechats->clone()
        ->max('queues_duration');
        $max_queue = round($max_queue,2);

        $average_queue = $livechats->clone()
        ->where('queues_duration','>',0)
        ->average('queues_duration');
        $average_queue = round($average_queue,2);
        
        $missed_chat = $livechats->clone()->whereHas('missed_chat')->count();
        $queue_abandonment = $livechats->clone()->whereHas('queue_abandonment')->count();
        $inactive_transfer = $livechats->clone()->whereHas('inactive_transfer')->count();

        $inactive_archived = $livechats->clone()->whereHas('inactive_archived')
        ->where('ticket',false)
        ->count();

        $lost_connection = $livechats->clone()->whereHas('lost_connection')->count();
        $signed_out_transfer = $livechats->clone()->whereHas('signed_out_transfer')->count();


        $text = "#{$platform}\n";
        $text.= "⚙️ Chat\n";
        $text.= "count : {$count}\n";
        $text.= "count (assigned to agent) : {$count_assigned}\n";
        $text.= "count (offline) : {$count_offline}\n";
        $text.= "count (ai) : {$count_ai}\n";
        $text.= "count (api ticket) : {$count_ticket}\n";
        $text.= "count (not transferred) : {$count_not_transferred}\n";
        $text.= "count (unique customers) : {$count_unique}\n";
        $text.= "count (closed by agent) : {$count_archived_by_agent}\n";
        $text.= "count (comment) : {$count_comment}\n";
        $text.= "count (rated good) : {$count_good}\n";
        $text.= "count (rated bad) : {$count_bad}\n";
        
        $text.= "count (chat duration > 30) : {$count_threshold}\n\n";

        $text.= "min chat duration : {$min_chat_duration}\n";
        $text.= "average chat duration : {$aht} \n";
        $text.= "max chat duration : {$max_chat_duration}\n";
        $thread_id = $livechats->clone()->orderByDesc('chat_duration')->first()->thread_id;
        $text.= "max chat duration : https://my.livechatinc.com/archives/{$thread_id}\n\n";

        $text.= "min FRT : {$min_frt}\n";
        $text.= "average FRT : {$average_frt}\n";
        $text.= "max FRT : {$max_frt}\n";
        $thread_id = $livechats->clone()->orderByDesc('FRT')->first()->thread_id;
        $text.="max FRT : https://my.livechatinc.com/archives/{$thread_id}\n\n";

        $text.= "queue count : {$queue_count}\n";
        $text.= "average queue duration : {$average_queue}\n";
        $text.= "max queue duration : {$max_queue}\n";

        $thread_id = $livechats->clone()->orderByDesc('queues_duration')->first()->thread_id;
        $text.= "max queue duration : https://my.livechatinc.com/archives/{$thread_id}\n\n";

        $text.= "missed_chat : {$missed_chat}\n";
        $text.= "queue_abandonment : {$queue_abandonment}\n";
        $text.= "inactive_transfer : {$inactive_transfer}\n";
        $text.= "inactive_archived : {$inactive_archived}\n";
        $text.= "lost_connection : {$lost_connection}\n";
        $text.= "signed_out_transfer : {$signed_out_transfer}\n";

        Slack::okr($text,$platform);
    }

    public function checkup_goftino($platform)
    {
        
        $goftino_chats = Goftino_chat::
        whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        });

        $count_unique = Goftino_data::
        whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->where('platform',$platform)->count();

        $one_time_chat_id_count = $goftino_chats->clone()
        ->select('chat_id')
        ->groupBy('chat_id')
        ->havingRaw('COUNT(chat_id) = 1')
        ->get()
        ->count();

        $compound_duration = $goftino_chats->clone()
        ?->select('chat_id', DB::raw('SUM(duration) as total_duration'))
        ?->groupBy('chat_id')
        ?->orderByDesc('total_duration')
        ?->limit(1)
        ?->first();

        $total_duration = $compound_duration->total_duration ?? '0';
        $total_duration = round($total_duration/60,0);
        $compound_chat_id = $compound_duration?->chat_id;

        $goftino_transfers = Goftino_transfer::
        whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        })->count();

        $goftino_closed_chats = Goftino_closed_chat::
        whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        })->count();

        $goftino_unassigned_chats = Goftino_unassigned_chat::
        whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        })->count();

        $goftino_assigned_chats = Goftino_assignment::
        whereBetween('created_at',[Carbon::yesterday()->startOfDay(),Carbon::yesterday()->endOfDay()])
        ->whereHas('data',function($q)use($platform){
            $q->where('platform',$platform);
        })->count();

        $count = $goftino_chats->clone()->count();

        $repetition = $goftino_chats->clone()
        ?->select('chat_id', DB::raw('COUNT(*) as repetition_count'))
        ?->groupBy('chat_id')
        ?->orderByDesc('repetition_count')
        ?->first();

        $repetition_count = $repetition?->repetition_count ?? 0;
        $repetition_chat_id = $repetition?->chat_id;

        $rated_chats = $goftino_chats->clone()->whereNotNull('rating')->count();
        $average_rating = $goftino_chats->clone()->whereNotNull('rating')->avg('rating');
        $average_rating = round($average_rating,2);
        
        $average_duration = $goftino_chats->clone()->avg('duration');
        $average_duration = round($average_duration/60,0);
        $max_duration = $goftino_chats->clone()->max('duration');
        $max_duration = round($max_duration/60,0);
        $duration_chat_id = $goftino_chats->clone()->orderByDesc('duration')?->first()?->chat_id;

        $text = "#{$platform}\n";
        $text.= "⚙️ Chat\n";
        $text.= "count : {$count}\n";
        $text.= "unique users count : {$count_unique}\n";
        $text.= "one-time chat_id count : {$one_time_chat_id_count}\n\n";

        $text.= "max one-user chat_id repetition : {$repetition_count}\n";
        $text.= "corresponding chat_id : {$repetition_chat_id}\n\n";

        $text.= "rated chats count : {$rated_chats}\n";
        $text.= "average rating : {$average_rating}\n\n";

        $text.= "average duration : {$average_duration} minutes\n";
        $text.= "max chat duration : {$max_duration} minutes\n";
        $text.= "corresponding chat_id : {$duration_chat_id}\n\n";

        $text.= "max compound chat duration : {$total_duration} minutes\n";
        $text.= "corresponding chat_id : {$compound_chat_id}\n\n";
        
        $average_queue = $goftino_chats->clone()->whereNotNull('queue')->avg('queue');
        $average_queue = round($average_queue/60,0);
        $max_queue = $goftino_chats->clone()->max('queue');
        $max_queue = round($max_queue/60,0);
        $queue_chat_id = $goftino_chats->clone()->orderByDesc('queue')?->first()?->chat_id;

        $text.= "average queue : {$average_queue} minutes\n";
        $text.= "max queue duration : {$max_queue} minutes\n";
        $text.= "corresponding chat_id : {$queue_chat_id}\n\n";

        $text.= "manually transferred chats count : {$goftino_transfers}\n";
        $text.= "automatically unassigned chats count: {$goftino_unassigned_chats}\n";
        $text.= "automatically closed chats count: {$goftino_closed_chats}\n";
        $text.= "automatically assigned chats count: {$goftino_assigned_chats}\n";

        // $this->info($text);
        Slack::okr($text,$platform);

    }

}
