<?php

namespace App\Http\Controllers;

use App\Models\Livechat;
use Illuminate\Http\Request;

class ExportLivechatController extends Controller
{
    public function index()
    {
        $livechats = Livechat::
        latest()
        ->whereNotNull('data')
        ->select(['data'])
        ->take(request()->get('take',10))
        ->where('platform',request()->get('platform','wallex'))
        ->get();
        
        return $livechats;
        dd($livechats);

    }
}
