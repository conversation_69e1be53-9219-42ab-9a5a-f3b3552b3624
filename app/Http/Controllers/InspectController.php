<?php

namespace App\Http\Controllers;

use App\Models\Parameter;
use App\Models\Parameter_record;
use App\Models\Record;
use App\Models\User;
use Illuminate\Http\Request;

class InspectController extends Controller
{
    public function index()
    {
        $data = request()->all();
        $email = $data['email'];
        $user_id = User::where('email',$email)->first()->id;

        $subjects = Record::
        where('support_agent_id',$user_id)
        ->where('created_at','>=',now()->subDays(8))
        ->distinct()->pluck('incoming_subject')->toArray();

        $parameters = Parameter::where('name','پاسخگویی صحیح')->where('active',true)->get();
        $list = [];

        foreach($subjects as $subject)
        {
            $sum_grade = 0;
            $count = 0;
            foreach($parameters as $parameter)
            {
                $score = $parameter->score;
                $parameter_records = Parameter_record::where('parameter_id',$parameter->id)
                    ->whereHas('record',function($q)use($user_id,$subject){
                        $q->where('incoming_subject',$subject)
                        ->where('created_at','>=',now()->subDays(8))
                        ->where('support_agent_id',$user_id);
                    })->get();
                
                foreach($parameter_records as $parameter_record)
                {
                    $sum_grade+=$parameter_record->value/$score;
                    $count+=1;
                }
            }
            if($count==0)
            {
                continue;
            }
            $list[$subject] = round($sum_grade/$count*100,2);
        }

        $list = collect($list)->sort();
        
        return response(['list'=>$list]);
    }
}
