<?php

namespace App\Livewire;

use App\Models\Exam_stat;
use App\Models\Parameter;
use App\Models\Period;
use App\Models\Record;
use App\Models\Setting;
use App\Models\User;
use Livewire\Component;

class ShowReport extends Component
{
    public $users;
    public $periods;

    public int $support_agent_id = 0 ;

    public $supervisor_coeff;
    public $qc_coeff;

    public $parameters;
    public $chat_scores = [];
    public $chat_count = [];
    public $call_scores = [];
    public $call_count = [];
    public $kyc_scores = [];
    public $outgoing_scores = [];
    public $kyc_count = [];
    public $supervisor_scores = [];
    public $qc_scores = [];
    public $redline_scores = [];
    public $final_qc_scores = [];
    public $final_scores = [];
    public $parameters_scores = [];
    public bool $weight;
    public $exam_stats;
    
    protected $rules = [
        'support_agent_id' => 'required|exists:users,id',
    ];

    public function mount()
    {
        $role_id = auth()->user()->role_id;

        $show_period = Setting::where('name', 'show_period')->first()->value;

        $this->users = User::when(in_array($role_id,[1]), function ($q) {
            $q->where('id', auth()->user()->id);
        })
        ->where('status', true)
        ->whereIn('role_id', [1,2])
        ->get();

        $this->supervisor_coeff = Setting::where('name', 'supervisor_coeff')->first()->value;
        $this->qc_coeff = Setting::where('name', 'qc_coeff')->first()->value;
        
        $this->periods = Period::
        where('id',">",4)
        ->when($role_id == 1, function ($query) use ($show_period) {
            $query
            ->where('id', '<=', $show_period);
        })
        ->get();

        

        $this->parameters = Parameter::whereIn('incoming_type', ['call','chat','kyc','outgoing'])
        // ->where('active',true)
        ->get();
        
        $this->weight = (bool) Setting::where('name', 'weight_integration')->first()->value;
    }

    public function updated()
    {
        $this->validate();

        $this->exam_stats = Exam_stat::where('user_id',$this->support_agent_id)->get();

        foreach($this->periods as $period) {
            $records = Record::with(['qc_parameters','red_lines'])
            ->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->where('support_agent_id', $this->support_agent_id)
            ->get();

            // calc total qc score
            $scores = [];
            $chat_scores = [];
            $call_scores = [];
            $kyc_scores = [];
            $outgoing_scores = [];
            $redline_scores = [];

            $parameters_sum = [];
            $parameters_count = [];
            
            foreach($records->where('incoming_type', 'chat')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                if($sum) {
                    $coefficient = 1;
                    if($this->weight){$coefficient = $record->coefficient;}

                    for($weight=1;$weight<=$coefficient;$weight++)
                    {
                        $chat_scores[] = $total / $sum * 100;
                        $scores[] = $total / $sum * 100;
                    }

                }
            }

            foreach($records->where('incoming_type', 'call')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;
                }
                if($sum) {
                    $coefficient = 1;
                    if($this->weight){$coefficient = $record->coefficient;}

                    for($weight=1;$weight<=$coefficient;$weight++)
                    {
                        $call_scores[] = $total / $sum * 100;
                        $scores[] = $total / $sum * 100;
                    }
                }
            }

            foreach($records->where('incoming_type', 'kyc')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                if($sum) {
                    $kyc_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;
                }
            }

            foreach($records->where('incoming_type', 'outgoing')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                if($sum) {
                    $outgoing_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;
                }
            }

            foreach($records->where('red', true) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->red_lines as $red_line) {
                    if(is_null($red_line->pivot->value)) {
                        continue;
                    }
                    $sum += $red_line->score;
                    $total += $red_line->pivot->value;
                }
                if($sum) {
                    $redline_scores[] = $total;
                }
            }

            $this->chat_scores[$period->id] = count($chat_scores) ? round(array_sum($chat_scores) / count($chat_scores), 2) : '-';
            $this->call_scores[$period->id] = count($call_scores) ? round(array_sum($call_scores) / count($call_scores), 2) : '-';
            $this->kyc_scores[$period->id] = count($kyc_scores) ? round(array_sum($kyc_scores) / count($kyc_scores), 2) : '-';
            $this->outgoing_scores[$period->id] = count($outgoing_scores) ? round(array_sum($outgoing_scores) / count($outgoing_scores), 2) : '-';
            
            $this->qc_scores[$period->id] = count($scores) ? round(array_sum($scores) / count($scores), 2) : '-';
            
            $this->redline_scores[$period->id] = count($redline_scores) ? array_sum($redline_scores) : '-';
            
            if(count($redline_scores))
            {
                if($this->redline_scores[$period->id] < 10)
                {
                    $this->final_qc_scores[$period->id] = $this->qc_scores[$period->id] - 2;
                }
                elseif($this->redline_scores[$period->id] >= 10 and $this->redline_scores[$period->id] <= 20)
                {
                    $this->final_qc_scores[$period->id] = $this->qc_scores[$period->id] - 3;
                }
                elseif($this->redline_scores[$period->id] > 20)
                {
                    $this->final_qc_scores[$period->id] = $this->qc_scores[$period->id] - 5;
                }
            }
            else
            {
                $this->final_qc_scores[$period->id] = $this->qc_scores[$period->id];
            }

            // calc parameters' score (first method)
            // $qc_parameters = $records->pluck('qc_parameters')->flatten();
            // foreach($this->parameters as $parameter)
            // {
            //     $list = $qc_parameters
            //     ->filter(function ($qc_parameter) use ($parameter) {
            //         $bool = $qc_parameter->id == $parameter->id && !is_null($qc_parameter->pivot->value);
            //         return $bool;
            //     });
            //     $average=$list->avg('pivot.value');

            //     $this->parameters_scores[$period->id][$parameter->id] = round($average / $parameter->score * 100,2);
            // }

            // calc parameters' score (second method)
            foreach($parameters_sum as $key => $value) {
                $average = $value / $parameters_count[$key];
                $this->parameters_scores[$period->id][$key] = round($average / $this->parameters->where('id', $key)->first()->score * 100, 2);
            }
            
            // calc total supervisor score
            $sum = 0.0;
            $total = 0.0;

            $user = User::with(['supervisor_parameters' => function ($q) use ($period) {
                $q->wherePivot('period_id', $period->id);
            }])
            ->where('id', $this->support_agent_id)->first();

            foreach($user->supervisor_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            $this->supervisor_scores[$period->id] = $sum ? round($total / $sum * 100, 2) : '-';

            // total agent score
            if($this->supervisor_scores[$period->id] != '-' && $this->final_qc_scores[$period->id] != '-') {
                $this->final_scores[$period->id] = ($this->final_qc_scores[$period->id] * $this->qc_coeff + $this->supervisor_scores[$period->id] * $this->supervisor_coeff) / 100;
                $this->final_scores[$period->id] = round($this->final_scores[$period->id], 2);
            } else {
                $this->final_scores[$period->id] = '-';
            }
        }

        $this->dispatch('reload',final_scores:$this->final_scores);
    }

    public function render()
    {
        return view('livewire.show-report');
    }
}
