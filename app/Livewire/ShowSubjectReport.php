<?php

namespace App\Livewire;

use App\Models\Parameter;
use App\Models\Parameter_record;
use App\Models\Period;
use App\Models\Record;
use App\Models\User;
use Livewire\Component;

class ShowSubjectReport extends Component
{
    public $platform = 'wallex';
    public $list = [];

    public $periods = [];
    public $period_id = 0;

    public $parameters = [];
    public $parameter_id = 0;

    public function mount()
    {
        $this->periods = Period::where('id','>',12)->get();
        $this->parameters = Parameter::where('active',true)->get();
    }

    public function updated()
    {
        if($this->parameter_id and $this->period_id)
        {
            $this->calc();
        }
    }

    public function calc()
    {
        $platform_user_ids = User::where('platform',$this->platform)->get()->pluck('id')->toArray();

        $subjects = Record::
        whereIn('support_agent_id',$platform_user_ids)
        ->where('incoming_date','>=',$this->periods->where('id',$this->period_id)->first()->from)
        ->where('incoming_date','<',$this->periods->where('id',$this->period_id)->first()->to)
        ->distinct()->pluck('incoming_subject')->toArray();
        
        $parameter = Parameter::where('active',true)
        ->where('id',$this->parameter_id)
        ->first();

        foreach ($subjects as $subject)
        {
            $sum_grade = 0;
            $count = 0;
            foreach($this->parameters->where('name',$parameter->name) as $parameter)
            {
                $score = $parameter->score;
                $parameter_records = Parameter_record::where('parameter_id',$parameter->id)
                ->whereHas('record',function($q)use($subject){
                    $q->where('incoming_subject',$subject)
                    ->where('incoming_date','>=',$this->periods->where('id',$this->period_id)->first()->from)
                    ->where('incoming_date','<',$this->periods->where('id',$this->period_id)->first()->to);
                })->get();

                foreach($parameter_records as $parameter_record)
                {
                    $sum_grade+=$parameter_record->value/$score;
                    $count+=1;
                }
            }
            if($sum_grade==0)
            {
                continue;
            }
            $this->list[$subject] = round($sum_grade/$count*100,2);
        }
        
        $this->list = collect($this->list)->sort();
    }

    public function render()
    {
        return view('livewire.show-subject-report');
    }
}
