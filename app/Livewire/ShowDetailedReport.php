<?php

namespace App\Livewire;

use App\Models\Parameter;
use Livewire\Component;

use App\Models\Period;
use App\Models\Record;

class ShowDetailedReport extends Component
{
    public $parameters_scores;
    public $parameters;
    public $support_agent_id;

    public function mount($user_id,$period_id)
    {
        if(auth()->user()->role_id == 1 and auth()->user()->id != $user_id)
        {
            abort(403);
        }
        $this->support_agent_id = $user_id;
        
        $parameters_sum = [];
        $parameters_count = [];

        $period = Period::find($period_id);

        $this->parameters = Parameter::
        whereHas('records',function($q) use ($period,$user_id){
            $q
            ->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->where('support_agent_id', $user_id);
        })
        ->get();

        $records = Record::with(['qc_parameters'])
            ->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->where('support_agent_id', $user_id)
            ->get();

            foreach($records->where('incoming_type', 'chat') as $record) {
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                
            }

            foreach($records->where('incoming_type', 'call') as $record) {
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;
                }
                
            }

            foreach($records->where('incoming_type', 'kyc') as $record) {
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;
                }
            }


            foreach($parameters_sum as $key => $value) {
                $average = $value / $parameters_count[$key];
                $this->parameters_scores[$key] = round($average / $this->parameters->where('id', $key)->first()->score * 100, 2);
            }
    }
    public function render()
    {
        return view('livewire.show-detailed-report');
    }
}
