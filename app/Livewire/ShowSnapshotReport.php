<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\Snapshot;
use Livewire\Component;

class ShowSnapshotReport extends Component
{
    public $snapshot;
    public $period;
    public $qc;
    public $supervisor;
    public $total;

    public function mount($id)
    {
        $snapshot = Snapshot::find($id);
        $this->period = Period::find($snapshot->period_id);

        $data = json_decode($snapshot['data']);

        $qc=[];
        $supervisor=[];
        $total=[];

        foreach($data->users as $user)
        {
            if($user->final_qc_score!='-') {
                $qc[]= (float) $user->final_qc_score;
            }
            
            if($user->supervisor_score!='-') {
                $supervisor[]=(float) $user->supervisor_score;
            }

            if($user->final_score!='-') {
                $total[]=(float) $user->final_score;
            }
        }

        // dd($qc,$supervisor,$total);

        $this->qc = count($qc) ? array_sum($qc) / count($qc) : '-';
        $this->supervisor = count($supervisor) ? array_sum($supervisor) / count($supervisor) : '-';
        $this->total = count($total) ? array_sum($total) / count($total) : '-';

    }

    public function render()
    {
        return view('livewire.show-snapshot-report');
    }
}
