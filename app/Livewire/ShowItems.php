<?php

namespace App\Livewire;

use App\Models\Item;
use Livewire\WithPagination;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;

class ShowItems extends Component
{
    use WithPagination;
    public $title;
    public $description;

    public function changeTitle($id,$title)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Item::find($id);
        $item->title = $title;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function changeDescription($id,$description)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Item::find($id);
        $item->description = $description;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function changeStatus($id,$status)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Item::find($id);
        $item->status = $status;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function delete($id)
    {
        if (! Gate::allows('delete-item')) {
            abort(403);
        }
        Item::find($id)->delete();

        $this->dispatch('operation-successful');
    }
    public function add()
    {
        if (! Gate::allows('add-item')) {
            abort(403);
        }

        Item::create([
            'title' => $this->title,
            'description' => $this->description,
        ]);
        $this->dispatch('operation-successful');
    }
    
    public function render()
    {
        $items = Item::paginate(100);
        
        return view('livewire.show-items',compact('items'));
    }
}
