<?php

namespace App\Livewire;

use App\Models\Livechat as ModelsLivechat;
use App\Models\Parameter;
use App\Models\Pool;
use App\Models\User;
use App\Models\Record;
use App\Models\Setting;
use App\Models\Weighted_subject;
use App\Models\goftino_message;
use App\Models\Goftino_assignment;
use App\Social\Slack;
// use App\Webhook\Livechat;
use Livewire\Component;
use Hek<PERSON>inasser\Verta\Verta;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
class CreateRecord extends Component
{

    public $start;

    public string $identity = '';
    public bool $registered = false;
    public string $incoming_type = '';
    public string $incoming_date;
    public int $support_agent_id = 0;
    public string $incoming_subject;
    public string $recorded_subject;
    public string $message;
    public string $feedback;
    public bool $red = false;
    public bool $draft = false;
    public $challenging = false;
    public $ignore = false;
    public string $crm_identity='';
    public int $coefficient;
    public string $daily_coach='';
    public $chat_text = '';
    public ?string $platform = null;
//    public $users;
    public string $agentSearch = '';
    public $qc_parameters = [];
    public $scores = [];

    public $red_lines = [];
    public $red_scores = [];
    public $chatTimeline = [];


    protected $queryString = ['identity','support_agent_id','incoming_type','incoming_date','message'];

    public $record;

    protected $rules = [
        'identity' => 'required|string',
        'incoming_type' => 'required|in:chat,call,kyc,outgoing,faq,email,ticket',
        'incoming_date' => 'required|string',
        'support_agent_id' => 'required|integer|exists:users,id',
        'incoming_subject' => 'required|string',
        'recorded_subject' => 'required|string',
        'message' => 'string|nullable',
        'feedback' => 'string|nullable',
        'red' => 'required|boolean',
        'draft' => 'required|boolean',
        'challenging' => 'required|boolean',
        'ignore' => 'required|boolean',
        'crm_identity' => 'required|string',
        'coefficient' => 'required|integer',
        'scores' => 'required|array',
        'daily_coach'=>'string|nullable',
        // 'red_scores' => 'required|array',
    ];

    public function mount()
    {
        $this->start = now();
        // $this->users = User::whereIn('role_id', [1,2])->get();
//        $this->users = User::all();

        if(isset($_GET['identity']))
        {
            $this->identity = urldecode($_GET['identity']);
        }
        if(isset($this->identity)) {
            $record = Record::where('identity', $this->identity)->first();
            if($record) {
                redirect()->route('update-record', ['identity' => $this->identity]);
            }
        }

        if ($this->identity) {
            $poolRecord = Pool::where('identity', $this->identity)->first();
            if ($poolRecord) {
                $this->platform = $poolRecord->platform;
            }
            $record = Record::where('identity', $this->identity)->first();
            if($record) {
                redirect()->route('update-record', ['identity' => $this->identity]);
            }
        }

        if (isset($this->support_agent_id)) {
            $agent = \App\Models\User::find($this->support_agent_id);
            if ($agent) {
                $this->agentSearch = $agent->name;
            }
        }

        if(isset($this->incoming_type))
        {
            $this->updatedIncomingType();
        }

        if(isset($this->message))
        {
            $this->message = strip_tags($this->message);
        }

    }

    public function updatedIncomingType(): void
    {
        $this->qc_parameters = Parameter::where('incoming_type', $this->incoming_type)
        ->where('active',true)
        ->get();

        $this->scores = [];
        foreach($this->qc_parameters as $qc_parameter) {
            // ارتباط موثر
            // default
            // چت
            if ( $qc_parameter->id==26 ) {
                $this->scores[$qc_parameter->id] = 0;
            } else {
                $this->scores[$qc_parameter->id] = $qc_parameter->score;
            }
        }

        if($this->incoming_type == 'chat') {
            $red = 'red_chat';
        } elseif($this->incoming_type == 'call') {
            $red = 'red_call';
        } elseif($this->incoming_type == 'outgoing') {
            $red = 'red_outgoing';
        } else {
            $this->red=false;
            $red = null;
        }

        $this->red_lines = Parameter::where('incoming_type', $red)
        ->where('active',true)
        ->get();
        $this->red_scores = [];
        foreach($this->red_lines as $red_line) {
            $this->red_scores[$red_line->id] = null;
        }
    }

    public function changeScore($qc_parameter_id, $score_value)
    {
        if(empty($score_value) and $score_value != 0) {
            $score_value = null;
        }
        $this->scores[$qc_parameter_id] = $score_value;
    }

    public function updatedRed()
    {
        $record = Record::where('identity', $this->identity)->first();
        if($this->red and $record) {
            foreach($record->red_lines as $red_line) {
                $this->red_scores[$red_line->id] = $red_line->pivot->value;
            }
        } elseif($record) {
            foreach($record->red_lines as $red_line) {
                $this->red_scores[$red_line->id] = null;
            }
        }
    }

    public function changeRedScore($red_line_id, $red_score_value)
    {
        if(empty($red_score_value) and $red_score_value != 0) {
            $red_score_value = null;
        }
        $this->red_scores[$red_line_id] = $red_score_value;
    }

    public function updatedIncomingSubject()
    {
        $weighted_subject = Weighted_subject::where('subject',trim($this->incoming_subject))->first();
        if($weighted_subject)
        {
            $this->coefficient=$weighted_subject->weight;
        }else{
            $this->coefficient=1;
        }
    }

//    public function save()
//    {
//
//        $validated = $this->validate();
//
//        $validated['incoming_date'] = Verta::parse($validated['incoming_date'])->toCarbon();
//
//        $lock_date = Setting::where('name', 'lock_records_date')->first()->value;
//        $lock_date = Verta::parse($lock_date)->toCarbon();
//
//        if($validated['incoming_date'] <= $lock_date) {
//            $this->dispatch('operation-failed');
//            $this->addError('custom', 'Incoming date is already locked.');
//            return;
//        }
//
//        $validated['qc_agent_id'] = auth()->user()->id;
//
//        unset($validated['scores']);
//        unset($validated['red_scores']);
//
//        $data = [];
//        foreach ($this->qc_parameters as $qc_parameter) {
//            $data[$qc_parameter->id] = ['value' => $this->scores[$qc_parameter->id]];
//        }
//
//        foreach ($this->red_lines as $red_line) {
//            $data[$red_line->id] = ['value' => $this->red_scores[$red_line->id]];
//        }
//
//        $validated['trace_time'] = now()->diffInSeconds($this->start);
//
//        $pool = Pool::where('identity', $this->identity)->first();
//        if(!is_null($pool) and $pool->qc_agent_id) {
//            if (! Gate::allows('create-record-from-pool', $pool)) {
//                abort(403);
//            }
//        }
//
//        $record = Record::where('identity',$this->identity)->first();
//
//        if(!is_null($record))
//        {
//            if (! Gate::allows('create-record', $record)) {
//                abort(403);
//            }
//        }
//
//        DB::transaction(function () use ($validated, $data,$pool) {
//            $this->record = Record::updateOrCreate(['identity' => $this->identity], $validated);
//
//            $this->record->parameters()->sync($data);
//
//            if(!is_null($pool)) {
//                $pool->delete();
//            }
//        });
//
//        $red_user = $this->users->where('id',$this->support_agent_id)->first();
//        if(!$this->registered and $this->red and $red_user->role_id==1)
//        {
//            $text = "#red_line" . "\n";
//            $text .= "کارشناس: " . $red_user->name . "\n";
//            $text .= "https://qc.wallex.support/show/records?search=" . $this->identity . "\n";
//            // $text .= "<!channel>";
//
//            switch($this->users->where('id',$this->support_agent_id)->first()?->platform)
//            {
//                case 'wallgold':
//                    $text .= "<!subteam^S08DZ56V4NN>";  // wallgold
//                break;
//
//                default:
//                    $text .= "<!subteam^S07Q7BLM2SD>";  // wallex
//                break;
//            }
//
//
//            $thread_ts = Slack::qc_alert($text);
//            $this->record->thread_ts = $thread_ts;
//            $this->record->save();
//        }
//
//        if(!$this->registered and $this->daily_coach)
//        {
//            $slack_id = $this->users->where('id',$this->support_agent_id)->first()?->slack_id;
//            $text = "لینک رکورد : https://qc.wallex.support/show/records?search={$this->identity}\n";
//            $text .= "فیدبک کارشناس qc : " . $this->daily_coach;
//            Slack::pv($text,$slack_id);
//        }
//
//        $this->registered = true;
//        $this->dispatch('operation-successful');
//    }


    public function save()
    {
        $validated = $this->validate();

        $validated['incoming_date'] = Verta::parse($validated['incoming_date'])->toCarbon();

        $lock_date = Setting::where('name', 'lock_records_date')->first()->value;
        $lock_date = Verta::parse($lock_date)->toCarbon();

        if($validated['incoming_date'] <= $lock_date) {
            $this->dispatch('operation-failed');
            $this->addError('custom', 'Incoming date is already locked.');
            return;
        }

        $validated['qc_agent_id'] = auth()->user()->id;

        unset($validated['scores']);
        unset($validated['red_scores']);

        $data = [];
        foreach ($this->qc_parameters as $qc_parameter) {
            $data[$qc_parameter->id] = ['value' => $this->scores[$qc_parameter->id]];
        }

        foreach ($this->red_lines as $red_line) {
            $data[$red_line->id] = ['value' => $this->red_scores[$red_line->id]];
        }

        $validated['trace_time'] = now()->diffInSeconds($this->start);

        $pool = Pool::where('identity', $this->identity)->first();
        if(!is_null($pool) and $pool->qc_agent_id) {
            if (! Gate::allows('create-record-from-pool', $pool)) {
                abort(403);
            }
        }

        $record = Record::where('identity',$this->identity)->first();

        if(!is_null($record))
        {
            if (! Gate::allows('create-record', $record)) {
                abort(403);
            }
        }

        DB::transaction(function () use ($validated, $data,$pool) {
            $this->record = Record::updateOrCreate(['identity' => $this->identity], $validated);
            $this->record->parameters()->sync($data);
            if(!is_null($pool)) {
                $pool->delete();
            }
        });


        $selected_agent = \App\Models\User::find($this->support_agent_id);

        // $red_user = $this->users->where('id',$this->support_agent_id)->first();
        if(!$this->registered and $this->red and $selected_agent and $selected_agent->role_id==1)
        {
            $text = "#red_line" . "\n";
            $text .= "کارشناس: " . $selected_agent->name . "\n";
            $text .= "https://qc.wallex.support/show/records?search=" . $this->identity . "\n";
            // $text .= "<!channel>";


            // switch($this->users->where('id',$this->support_agent_id)->first()?->platform)
            switch($selected_agent?->platform)
            {
                case 'wallgold':
                    $text .= "<!subteam^S08DZ56V4NN>";  // wallgold
                    break;

                default:
                    $text .= "<!subteam^S07Q7BLM2SD>";  // wallex
                    break;
            }

            $thread_ts = Slack::qc_alert($text);
            $this->record->thread_ts = $thread_ts;
            $this->record->save();
        }

        if(!$this->registered and $this->daily_coach)
        {
            // $slack_id = $this->users->where('id',$this->support_agent_id)->first()?->slack_id;
            $slack_id = $selected_agent?->slack_id;
            $text = "لینک رکورد : https://qc.wallex.support/show/records?search={$this->identity}\n";
            $text .= "فیدبک کارشناس qc : " . $this->daily_coach;
            Slack::pv($text,$slack_id);
        }

        $this->registered = true;
        $this->dispatch('operation-successful');
    }

    public function inspect()
    {
        if ($this->incoming_type !== 'chat' || empty($this->identity)) {
            $this->chatTimeline = [];
            return;
        }

        $messages = \App\Models\Goftino_message::where('chat_id', $this->identity)
            ->with('user')
            ->get()->each(function ($item) {
                $item->event_type = 'message';
            });

        $assignments = \App\Models\Goftino_assignment::where('chat_id', $this->identity)
            ->with('user')
            ->get()->each(function ($item) {
                $item->event_type = 'assignment';
            });

        $this->chatTimeline = $messages
            ->concat($assignments)
            ->sortBy(function ($item) {
                return $item->date ?? $item->created_at;
            })
            ->values()
            ->toArray();
    }

    public function next()
    {
        $random = Pool::
        where('platform',auth()->user()->platform)
        ->where('qc_agent_id',auth()->user()->id)->inRandomOrder()->first();
        $data = [];
        if($random)
        {
            $data['support_agent_id'] = $random->support_agent_id;
            $data['identity'] = $random->identity;
            $data['incoming_type'] = $random->incoming_type;
            $data['incoming_date'] = is_null($random->incoming_date) ? null : verta($random->incoming_date)->formatDate();
            $data['message'] = ($random->incoming_type=='kyc') ? $random->note . "\nTime: " . verta($random->incoming_date)->format('H:i:s') : $random->note;
        }

        return redirect()->route('create-record',$data);
    }
    public function render()
    {
        $usersQuery = \App\Models\User::query();

        $usersQuery->where('name', 'like', '%' . $this->agentSearch . '%');

        return view('livewire.create-record', [
            'users' => $usersQuery->orderBy('name')->get(),
        ]);
    }

    public function selectAgent($id, $name)
    {
        $this->support_agent_id = $id;
        $this->agentSearch = $name;
    }

}
