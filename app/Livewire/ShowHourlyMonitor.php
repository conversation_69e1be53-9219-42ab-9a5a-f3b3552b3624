<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use <PERSON>k<PERSON><PERSON><PERSON>\Verta\Verta;

class ShowHourlyMonitor extends Component
{
    public $users=[];
    public $from='';
    public $to='';

    public function mount()
    {
        $this->updated();
    }

    public function updated()
    {
        $this->users = User::where('role_id',2)
        ->where('status',true)
        ->withCount([
            'qc_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                });
            },
            
            'qc_records as h0_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 0');
            },
            'qc_records as h1_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 1');
            },
            'qc_records as h2_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 2');
            },
            'qc_records as h3_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 3');
            },
            'qc_records as h4_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 4');
            },
            'qc_records as h5_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 5');
            },
            'qc_records as h6_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 6');
            },
            'qc_records as h7_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 7');
            },
            'qc_records as h8_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 8');
            },
            'qc_records as h9_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 9');
            },
            'qc_records as h10_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 10');
            },
            'qc_records as h11_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 11');
            },
            'qc_records as h12_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 12');
            },
            'qc_records as h13_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 13');
            },
            'qc_records as h14_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 14');
            },
            'qc_records as h15_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 15');
            },
            'qc_records as h16_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 16');
            },
            'qc_records as h17_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 17');
            },
            'qc_records as h18_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 18');
            },
            'qc_records as h19_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 19');
            },
            'qc_records as h20_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 20');
            },
            'qc_records as h21_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 21');
            },
            'qc_records as h22_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 22');
            },
            'qc_records as h23_records'=>function($q){
                $q->when($this->from,function($q){
                    $q->where('created_at','>=',Verta::parse($this->from)->toCarbon());
                })
                ->when($this->to,function($q){
                    $q->where('created_at','<',Verta::parse($this->to)->toCarbon());
                })
                ->whereRaw('HOUR(created_at) = 23');
            },
        ])
        ->get();
    }

    public function render()
    {
        
        return view('livewire.show-hourly-monitor');
    }
}
