<?php

namespace App\Livewire;

use App\Models\Sensitive_word;
use Livewire\Component;

class ShowSensitiveWords extends Component
{
    public $value;
    public function delete($id)
    {
        Sensitive_word::find($id)->delete();
        $this->dispatch('operation-successful');
    }

    public function toggle($id)
    {
        $sensitive_word = Sensitive_word::find($id);
        $sensitive_word->alert = !$sensitive_word->alert;
        $sensitive_word->save();
        $this->dispatch('operation-successful');
    }

    public function toggle_sensitivity($id)
    {
        $sensitive_word = Sensitive_word::find($id);
        $sensitive_word->mode = $sensitive_word->mode == 'includes' ? 'excludes' : 'includes';
        $sensitive_word->save();
        $this->dispatch('operation-successful');
    }

    public function change_value($id,$value)
    {
        $sensitive_word = Sensitive_word::find($id);
        $sensitive_word->value = $value;
        $sensitive_word->save();
        $this->dispatch('operation-successful');
    }

    public function add()
    {
        Sensitive_word::create([
            'value'=>$this->value,
        ]);
        $this->dispatch('operation-successful');

    }

    public function render()
    {
        $sensitive_words = Sensitive_word::all();
        
        return view('livewire.show-sensitive-words',compact('sensitive_words'));
    }
}
