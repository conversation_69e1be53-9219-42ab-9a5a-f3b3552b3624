<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Period;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class ShowPeriods extends Component
{

    public ?string $from;
    public ?string $to;

    protected $rules = 
    [
        'from' => 'required|string',
        'to' => 'required|string',
    ];

    public function insert()
    {
        $validated = $this->validate();

        $period = Period::create([
            'from' => verta::parse($validated['from'])->toCarbon(),
            'to' => verta::parse($validated['to'])->toCarbon(),
        ]);

        $this->dispatch('operation-successful');
    }

    public function change_start($period_id,$value)
    {
        $period = Period::find($period_id);
        $period->from = Verta::parse($value)->toCarbon();
        $period->save();
        $this->dispatch('operation-successful');
    }
    
    public function change_end($period_id,$value)
    {
        $period = Period::find($period_id);
        $period->to = Verta::parse($value)->toCarbon();
        $period->save();
        $this->dispatch('operation-successful');
    }
    public function delete($period_id)
    {
        Period::find($period_id)->delete();
        $this->dispatch('operation-successful');
    }
    public function render()
    {
        // $periods = Period::paginate(12);
        $periods = Period::all();
        return view('livewire.show-periods', compact('periods'));
    }
}
