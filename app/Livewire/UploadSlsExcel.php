<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ParslogicImport;
use App\Models\Ticket;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class UploadSlsExcel extends Component
{
    use WithFileUploads;
    public $excel;
    public $support_agents;

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'excel' => 'extensions:xlsx,xls|max:1024', // 1MB Max
        ]);

        $records = $this->process($validated['excel']);

        if($records)
        {
            foreach ($records as &$record) {
                $record['created_at'] = now();
                $record['updated_at'] = now();
            }

            Ticket::insertOrIgnore($records);
            $this->dispatch('operation-successful');
            return redirect()->route('show-tickets');
        }
        else
        {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new ParslogicImport(), $file);
        $array = $array[0];
        array_shift($array);

        $subjects = [
            "انتخاب موضوع اشتباه",
            "قابل پیگیری تکراری",
            "تماس خروجی جهت تکمیل اطلاعات",
        ];

        $records = [];

        foreach ($array as $record) {
            $name = str()->replace(' - ', ' ', $record[1]);

            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return (str_contains($user->name,$name) or str_contains($user->nickname,$name));
                // return ($user->name == $name or $user->nickname == $name);
            })->first();

            $datetime = $record[4];
            $creation_date = Verta::parse($datetime)->toCarbon();

            $subject = $record[7];
            $id = $record[9];
            $wrong_user = $record[14];
            $wrong_SL = $record[15];
            if($support_agent) {
                if(in_array($subject,$subjects))
                {
                    $item = [
                        'crm_id' => $id,
                        'support_agent_id' => $support_agent->id,
                        'subject'=> $subject,
                        'creation_date'=> $creation_date,
                    ];
                    array_push($records, $item);
                }
                if($wrong_user)
                {
                    $item = [
                        'crm_id' => $id,
                        'support_agent_id' => $support_agent->id,
                        'subject'=> 'انتخاب کاربر اشتباه',
                        'creation_date'=> $creation_date,
                    ];
                    array_push($records, $item);
                }
                if($wrong_SL)
                {
                    $item = [
                        'crm_id' => $id,
                        'support_agent_id' => $support_agent->id,
                        'subject'=> 'انتخاب گزینه ی "ارجاع جهت پیگیری" به اشتباه',
                        'creation_date'=> $creation_date,
                    ];
                    array_push($records, $item);
                }

            }else{continue;}
        }

        return $records;
    }

    public function render()
    {
        return view('livewire.upload-sls-excel');
    }
}
