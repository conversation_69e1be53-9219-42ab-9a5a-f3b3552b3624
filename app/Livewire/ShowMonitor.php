<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class ShowMonitor extends Component
{
    // qc actions (dashboard)
    public $qc_agents;
    public $support_agents;

    public string $from_date = '';
    public string $to_date = '';
    public $filter = false;
    public $field;
    public function mount()
    {
        $this->updatedFilter();
    }

    public function updatedFilter()
    {
        if($this->filter) {
            $this->field = 'created_at';
        } else {
            $this->field = 'incoming_date';
        }
    }

    public function get_qc_agents_stats()
    {
        $this->qc_agents = User::where('role_id', 2)
        ->whereHas('qc_records', function ($query) {
            $query
            ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
        })
        ->withAvg([
            'qc_records as average_trace_time' =>
                function ($query) {
                    $query
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as average_chat_trace_time' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'chat')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as average_call_trace_time' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'call')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as average_kyc_trace_time' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'kyc')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as average_outgoing_trace_time' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'outgoing')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as average_faq_trace_time' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'faq')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as average_ticket_trace_time' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'ticket')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as average_email_trace_time' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'email')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            ], 'trace_time')
        ->withCount([
            'qc_records' =>
                function ($query) {
                    $query
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as chat_qc_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'chat')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as call_qc_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'call')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as kyc_qc_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'kyc')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as outgoing_qc_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'outgoing')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'qc_records as objects' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'qc_records as objects_pending' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'pending')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'qc_records as objects_accepted' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'accepted')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'qc_records as objects_accepted_partial' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'accepted_partial')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'qc_records as objects_rejected' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'rejected')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
        ])
        ->get();
    }

    public function get_support_agents_stats()
    {
        $this->support_agents = User::where('role_id', 1)
        ->where('status', true)
        ->whereHas('records', function ($query) {
            $query
            ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
        })
        ->withCount([
            'records' =>
                function ($query) {
                    $query
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'records as chat_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'chat')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'records as call_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'call')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'records as kyc_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'kyc')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'records as outgoing_records' =>
                function ($query) {
                    $query
                    ->where('incoming_type', 'outgoing')
                    ->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                    ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                },
            'records as objects' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'records as reobjects' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status_change_count','>',1)
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'records as objects_pending' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'pending')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'records as objects_accepted' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'accepted')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'records as objects_accepted_partial' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'accepted_partial')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
            'records as objects_rejected' =>
                function ($query) {
                    $query
                    ->when($this->field=='incoming_date',function($q){
                        $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                        ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                    
                    })
                    ->whereHas('object',function($q){
                        $q->where('status', 'rejected')
                        ->when($this->field=='created_at',function($q){
                            $q->when($this->from_date, function ($q) {$q->where($this->field, '>=', Verta::parse($this->from_date)->toCarbon());})
                            ->when($this->to_date, function ($q) {$q->where($this->field, '<', Verta::parse($this->to_date)->toCarbon());});
                        });
                    });
                },
        ])
        ->get();
    }

    public function render()
    {

        $this->get_qc_agents_stats();
        $this->get_support_agents_stats();

        return view('livewire.show-monitor');
    }
}
