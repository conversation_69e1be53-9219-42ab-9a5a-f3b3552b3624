<?php

namespace App\Livewire;

use App\Imports\kycImport;
use Livewire\Component;
use App\Models\Pool;
use App\Models\User;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Gate;

class UploadKycExcel extends Component
{
    use WithFileUploads;
    public $excel;
    public $platform = 'wallex';

    public function save()
    {
        if (! Gate::allows('upload-qc')) {
            abort(403);
        }
        
        $validated = $this->validate([
            'excel' => 'max:5120', // 1MB Max
        ]);
        $records = $this->process($validated['excel']);
        if($records) {
            foreach ($records as &$record) {
                $record['created_at'] = now();
                $record['updated_at'] = now();
            }
            Pool::insertOrIgnore($records);
            $this->dispatch('operation-successful');
            return redirect()->route('pool');
        } else {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new kycImport(), $file);
        $array = $array[0];
        array_shift($array);

        $users = User::all();
        
        $support_agents = $users->where('role_id', 1)
        // ->where('platform',$this->platform)
        ->where('weight', '<>', 0);

        $qc_agents = $users->where('role_id', 2)
        // ->where('platform',$this->platform)
        ->where('platform','wallex')
        ->where('trace_kyc', '<>', 0);

        $records = [];

        foreach ($array as $record) {

            $name = $record[9];     // DoneByAdminName
            $support_agent = $support_agents
            ->filter(function($user) use ($name) {
                return str_contains($user->name,$name) or str_contains($user->nickname,$name);
            })
            ->first();

            if($support_agent)
            {

                $item = [
                    'identity' => $record[0] . "#" . $record[3],       // userid
                    'incoming_date' => \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($record[8]),    // ActionDateTime
                    'support_agent_id' => $support_agent->id,
                    'incoming_type' => 'kyc',
                    'note' => 'NEW : ' . $record[5],
                    'platform'=>'wallex',
                    // 'platform'=>$this->platform,
                ];

                array_push($records, $item);
            }
            
        }
        $target = $qc_agents->sum('trace_kyc');
        $min = min($target, count($records));
        shuffle($records);
        return array_slice($records, 0, $min);
    }

    public function render()
    {
        return view('livewire.upload-kyc-excel');
    }
}
