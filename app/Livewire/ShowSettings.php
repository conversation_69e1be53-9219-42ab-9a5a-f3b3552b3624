<?php

namespace App\Livewire;

use App\Models\Setting;
use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;

class ShowSettings extends Component
{
    public string $name = "";
    public string $value = "";


    protected $rules = [
        "name" => "required|string",
        "value" => "required|string",
    ];

    public function add()
    {
        if (! Gate::allows('add-setting')) {
            // abort(403);
        }

        Setting::create([
            'name' => $this->name,
            'value' => $this->value,
        ]);
        $this->dispatch('operation-successful');
    }

    public function change_value($id, $value)
    {
        $setting = Setting::find($id);
        $setting->value = $value;
        
        if($setting->name=='chat_limit')
        {
            $token = env('LIVECHAT_API_TOKEN',"Y2Q4MjlmMTEtMzAwOS00ZWVmLTk3YjMtNmQ1N2U3MmVkYzA5OmRhbDpfcWRtTGYzckU2X01qa3llUmVaOTZURlJaZXc=");
            $agents = Http::withHeader('Authorization', "Basic " . $token)
            ->post("https://api.livechatinc.com/v3.5/configuration/action/list_agents",[
                'fields'=>[
                    'max_chats_count',
                ],
            ]);
            
            // batch
            $requests = [];
            foreach($agents->object() as $agent)
            {
                $id = $agent->id;
                if(!str_ends_with($id,'@wallex.net') or in_array($id,User::where('status',true)->where('shift','شب')->get()->pluck('email')->toArray())) { continue; }
                $requests[] = [
                    'id'=>$id,
                    'name'=>$agent->name,
                    'role'=>$agent->role,
                    'max_chats_count'=>intval($value),
                    'login_status'=>'not accepting chats',
                ];

                $response = Http::withHeader('Authorization', "Basic " . $token)
                ->acceptJson()
                ->post('https://api.livechatinc.com/v3.5/configuration/action/update_agent',[
                    'id'=>$id,
                    'name'=>$agent->name,
                    'role'=>$agent->role,
                    'max_chats_count'=>intval($value),
                    'login_status'=>'not accepting chats',
                ]);
                // sleep(0.1);
            }
            // $response = Http::withHeader('Authorization', "Basic " . $token)
            // ->acceptJson()
            // ->post('https://api.livechatinc.com/v3.5/configuration/action/batch_update_agents',[
            //     'requests'=>$requests,
            // ]);
            // dd($response);
        }
        $setting->save();
        $this->dispatch('operation-successful');
    }

    public function change_note($id, $value)
    {
        $setting = Setting::find($id);
        $setting->note = $value;
        $setting->save();
        $this->dispatch('operation-successful');
    }

    public function delete($id)
    {
        if (! Gate::allows('delete-setting')) {
            // abort(403);
        }
        // Setting::find($id)->delete();
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        $settings = Setting::all();
        return view('livewire.show-settings', compact('settings'));
    }
}
