<?php

namespace App\Livewire\Exams;

use App\Models\Exam_stat;
use App\Models\Period;
use App\Models\User;
use Livewire\Component;

class ShowReport extends Component
{
    public $periods = [];
    public $period_id = 0;
    public $users = [];

    public function mount()
    {
        $this->periods = Period::where('id','>',5)->get();
        $this->users = User::where('role_id',1)->where('status',true)->get();
    }

    public function change($user_id,$value)
    {
        $exam_stat = Exam_stat::where('period_id',$this->period_id)
        ->where('user_id',$user_id)
        ->first();

        if($value=='') {
            Exam_stat::where('period_id',$this->period_id)->where('user_id',$user_id)->delete();
        } else {
            Exam_stat::updateOrCreate([
                'user_id'=>$user_id,
                'period_id'=>$this->period_id
            ],[
                'value'=>$value,
            ]);
        }
        $this->dispatch('operation-successful');
    }

   public function render()
    {
        $exam_stats = Exam_stat::where('period_id',$this->period_id)->get();

        return view('livewire.exams.show-report',compact('exam_stats'));
    }
}
