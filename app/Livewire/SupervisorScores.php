<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\User;
use Livewire\Component;

class SupervisorScores extends Component
{
    public $users = [];
    public $periods = 0;
    public $supervisors = [];
    public $supervisors_score = [];


    public function mount()
    {
    }

    public function updated()
    {
        $this->supervisors = User::where('role_id',3)->get();

        $this->users = [];

        $periods = Period::latest()->limit($this->periods)
        ->whereHas('snapshot')
        ->with('snapshot')
        ->get();

        $support_agents = [];

        foreach($periods as $period)
        {
            $data = collect(json_decode($period->snapshot['data']));
            $users = $data['users'];

            foreach($users as $user_id => $user)
            {
                $final_score = $user->final_score;
                $final_qc_score = $user->final_qc_score;
                $supervisor_score = $user->supervisor_score;
                
                if($final_score != "-") {
                    $support_agents[$user_id][$period->id]['final_score'] = $final_score;
                }

                if($final_qc_score != "-") {
                    $support_agents[$user_id][$period->id]['final_qc_score'] = $final_qc_score;
                }

                if($supervisor_score != "-") {
                    $support_agents[$user_id][$period->id]['supervisor_score'] = $supervisor_score;
                }
            }
        }

        $support_agents = collect($support_agents);
        $agents = User::whereIn('id',$support_agents->keys())->get();
        
        $this->supervisors_score = [];
        foreach($this->supervisors as $supervisor)
        {
            $sub = $agents->where('supervisor_id',$supervisor->id);

            $final_score = $support_agents->filter(function($value,$key) use ($sub){
                return in_array($key,$sub->pluck('id')->toArray());
            })->flatten(1)->pluck('final_score')->average();
            
            $final_qc_score = $support_agents->filter(function($value,$key) use ($sub){
                return in_array($key,$sub->pluck('id')->toArray());
            })->flatten(1)->pluck('final_qc_score')->average();

            $supervisor_score = $support_agents->filter(function($value,$key) use ($sub){
                return in_array($key,$sub->pluck('id')->toArray());
            })->flatten(1)->pluck('supervisor_score')->average();

            // if($final_score == 0 and $final_qc_score==0 and $supervisor_score==0){continue;}

            $this->supervisors_score[] = 
            [
                'name' => $supervisor->name,
                'final_score' => round($final_score,2),
                'final_qc_score' => round($final_qc_score,2),
                'supervisor_score' => round($supervisor_score,2),
            ];

            $this->supervisors_score = collect($this->supervisors_score)->sortByDesc('supervisor_score');
        }

    }
    
    public function render()
    {
        return view('livewire.supervisor-scores');
    }
}
