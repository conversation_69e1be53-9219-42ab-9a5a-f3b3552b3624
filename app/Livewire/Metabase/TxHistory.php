<?php

namespace App\Livewire\Metabase;

use Livewire\Component;
use Illuminate\Support\Facades\Http;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class TxHistory extends Component
{
    public $national_code = '';
    public $from = '';
    public $to = '';
    public $email = '';

    public function mount()
    {
        
    }

    public function get_email()
    {
        $email = Http::get("https://userreportapi.wallex.network/get_email?national_code={$this->national_code}")->object();
        return $email;
    }

    public function email_file()
    {
        if($this->national_code and strlen($this->national_code)==10 and $this->from and $this->to)
        {
            $from = Verta::parse($this->from)->toCarbon()->format('Y-m-d');
            $to = Verta::parse($this->to)->toCarbon()->format('Y-m-d');
            $this->email = $this->get_email();

            // $response = Http::timeout(180)
            // ->get("https://userreportapi.wallex.network/get_trans?national_code={$this->national_code}&from={$from}%2000:00:00&to={$to}%2000:00:00");
            // $tempPath = storage_path('app/temp_file.tmp');
            // file_put_contents($tempPath, $response->body());
            // $response = Http::attach('file', file_get_contents($tempPath), 'report.xlsx')
            // ->post('https://n8n.wallex.support/webhook/d70e0f61-7a19-4ce2-9471-fa393ca80eda', [
            //     'email' => $this->email,
            //     'from' => $from,
            //     'to' => $to,
            // ]);
        }
    }

    public function render()
    {
        return view('livewire.metabase.tx-history');
    }
}
