<?php

namespace App\Livewire\Health;

use Livewire\Component;
use Illuminate\Support\Facades\Http;

class Market extends Component
{
    public $filter = '';
    public $buy_status = '0';
    public $sell_status = '0';

    public function mount()
    {
    }

    public function OTC()
    {
        $otc_list = Http::acceptJson()
        ->get('https://api.wallex.ir/v1/otc/markets')
        ->collect('result');

        $otc_price = collect(cache('otc_price', []));
        
        $counter = 1;

        $data = collect();

        foreach($otc_list as $otc_item)
        {
            if($this->filter=='' or str_contains($otc_item['symbol'],strtoupper($this->filter)))
            {
                $data->push([
                    'id'=>$counter, // fake
                    'symbol'=>$otc_item['symbol'],
                    'buyStatus'=>$otc_item['buyStatus'],
                    'sellStatus'=>$otc_item['sellStatus'],
                    'BUY'=>$otc_price[$otc_item['symbol']]['BUY'] ?? '-',
                    'SELL'=>$otc_price[$otc_item['symbol']]['SELL'] ?? '-',
                    'diff'=>$otc_price[$otc_item['symbol']]['diff'] ?? '-',
                    'checked_at'=>isset($otc_price[$otc_item['symbol']]['checked_at']) ? verta($otc_price[$otc_item['symbol']]['checked_at']) : '-',
                ]);
                $counter+=1;
            }
        }

        return $data->sortByDesc('diff')
        ->when($this->buy_status,function($collection){
            return $collection->where('buyStatus',$this->buy_status);
        })
        ->when($this->sell_status,function($collection){
            return $collection->where('sellStatus',$this->sell_status);
        });
    }

    public function freshData($id)
    {
        $item = $this->OTC()->where('id',$id)->first();
        $buy = $this->get_price($item['symbol'],'BUY');
        $sell = $this->get_price($item['symbol'],'SELL');
        $diff = ($buy and $sell) ? round($buy / $sell * 100 - 100,2) : null;

        $otc_price = cache('otc_price', []);

        $otc_price[$item['symbol']]['BUY'] = $buy;
        $otc_price[$item['symbol']]['SELL'] = $sell;
        $otc_price[$item['symbol']]['diff'] = $diff;
        $otc_price[$item['symbol']]['checked_at'] = now();

        $seconds = 61*60;
        cache(['otc_price' => $otc_price], $seconds);

        $this->dispatch('operation-successful');
    }

    public function get_price($symbol,$side)
    {
        $X_API_Key = env('WALLEX_X_API_KEY');
        
        $response = Http::acceptJson()
        ->withHeader("X-API-Key",$X_API_Key)
        ->get('https://api.wallex.ir/v1/account/otc/price',[
            'symbol'=>$symbol,
            'side'=>$side,
        ]);
        return $response->collect('result.price')[0] ?? null;
    }

    public function render()
    {
        $data = $this->OTC();

        return view('livewire.health.market',[
            'data'=>$data,
        ]);
    }
}
