<?php

namespace App\Livewire\Ai;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Http;

class Voice2text extends Component
{
    // https://www.persianspeech.com/dashboard/user-info
    // https://github.com/nevisa-team/nevisa-doc
    // https://stt.lexemeai.com/swagger/

    use WithFileUploads;
    public $file;
    public $voice;
    public $text = '';
    public $sleep = 4;  // seconds

    public function process()
    {
        // $this->process_first();
        // $this->process_second();
        // $this->process_third();
        // $this->process_fourth();
        $this->process_fifth();
    }

    // https://github.com/nevisa-team/nevisa-doc
    public function process_first()
    {
        
        $file = $this->get_file();

        $auth_token = HTTP::post('https://accounting.persianspeech.com/account/login',[
            'username_or_phone_or_email'=>'***********',
            'password'=>'53Namme1375!',
        ])->object()->data->token;

        $response = HTTP::
        attach('file',file_get_contents($file->getRealPath()),$file->getClientOriginalName())
        ->post('https://api.persianspeech.com/recognize-file',[
            'auth_token'=>$auth_token,
            'api_key'=>env('NEVISA_API_KEY'),
        ])->object();

        // $progress_url = $response->progress_url;
        $task_id = $response->task_id;

        // $size = $file->getSize() / (1024*1024);
        // $seconds = max(1,$size);
        
        
        $response = Http::get("https://api.persianspeech.com/celery-progress/{$task_id}/")->object();
        while($response->state == "PROGRESS")
        {
            sleep($this->sleep);
            $response = Http::get("https://api.persianspeech.com/celery-progress/{$task_id}/")->object();
        }
        $this->text = $response?->result?->transcription?->text;
        
    }

    // https://stt.lexemeai.com/swagger/
    public function process_second()
    {
        
        $file = $file = $this->get_file();

        $access = Http::post('https://stt.lexemeai.com/users/login/',[
            'username'=>'testuser',
            'password'=>'testpass',
        ])->object()->access;


        $stt_result_key = Http::
        withToken($access)
        ->attach('file',file_get_contents($file->getRealPath()),$file->getClientOriginalName())
        ->post('https://stt.lexemeai.com/stt_file/upload/')->object()->stt_result_key;
        
        $response = Http::
        withToken($access)
        ->get("https://stt.lexemeai.com/stt_file/result/{$stt_result_key}")->object();

        while($response->stt_result_status == 'pending')
        {
            sleep($this->sleep);
            $response = Http::
            withToken($access)
            ->get("https://stt.lexemeai.com/stt_file/result/{$stt_result_key}")->object();
        }

        $this->text = $response->stt_result_script;
    }

    // assemblyai
    public function process_third()
    {
        $file = $this->get_file();
        $upload_url = Http::withHeaders([
            'Content-type'=>'application/octet-stream',
            'Authorization'=>env('ASSEMBLYAI_API_KEY'),
        ])
        ->attach('file',file_get_contents($file->getRealPath()),$file->getClientOriginalName())
        ->post('https://api.assemblyai.com/v2/upload')->object()->upload_url;

        $id = Http::
        withHeaders([
            'content-type'=>'application/json',
            'Authorization'=>env('ASSEMBLYAI_API_KEY'),
        ])
        ->post('https://api.assemblyai.com/v2/transcript',[
            'audio_url'=>$upload_url,
            'language_code'=>'fa',
            'speech_model'=>'nano',
        ])
        ->object()->id;

        while(true)
        {
            $response = Http::withHeaders([
                'content-type'=>'application/json',
                'Authorization'=>env('ASSEMBLYAI_API_KEY'),
            ])
            ->get("https://api.assemblyai.com/v2/transcript/{$id}")->object();
            if($response->status == 'completed')
            {
                $this->text = $response->text;
                break;
            }elseif($response->status == 'error')
            {
                dd($response);
            }
            sleep($this->sleep);
        }
    }

    // metisai (openai)

    public function process_fourth()
    {
        $file = $this->get_file();
        $response = Http::withHeaders([
            // 'Content-type'=>'multipart/form-data',
            'Authorization'=>env('metisai'),
        ])
        ->attach('file',file_get_contents($file->getRealPath()),$file->getClientOriginalName())
        ->post('https://api.metisai.ir/openai/v1/audio/transcriptions',[
            'model'=>'whisper-1',
        ])->object();
        $this->text = $response->text;
        dump($response);
    }

    public function process_fifth()
    {
        $apiKey = env('aistudio');

        $file = $this->get_file();
        $uploadUrl = "https://generativelanguage.googleapis.com/upload/v1beta/files?key={$apiKey}";

        $response = Http::
        // timeout(60)
        attach('file',file_get_contents($file->getRealPath()),$file->getClientOriginalName())
        ->post($uploadUrl);
        
        $uri = $response->object()->file->uri;

        $payload = [
            'contents' => [
                [
                    'role' => 'user',
                    'parts' => [
                        [
                            'fileData' => [
                                'fileUri' => $uri,
                                'mimeType' => 'audio/mpeg',
                            ],
                        ],
                    ],
                ],
                [
                    'role' => 'model',
                    'parts' => [
                        [
                            'text' => '```json\n{"conversation": [{"speaker": "...","text": "..."}]}```',
                        ],
                    ],
                ],
                // [
                //     'role' => 'user',
                //     'parts' => [
                //         [
                //             'text' => '',
                //         ],
                //     ],
                // ],
            ],
            'systemInstruction' => [
                'role' => 'user',
                'parts' => [
                    [
                        'text' => "```
                        فایل صوتی که آپلود شده مکالمات تلفنی بین دو نفر هستند
                        زبان مکالمه فارسی است
                        لطفا متن گفتگو ارسال کن
                        ولی قبل از ارسال جملات را ویرایش کن و اصلاحات لازم را انجام بده (مثلا نگارشی و املایی و معنایی)
                        ```",
                    ],
                ],
            ],
            'generationConfig' => [
                'temperature' => 1,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 8192,
                'responseMimeType' => 'application/json',
            ],
        ];

        $response = Http::
        post("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key={$apiKey}",$payload);
        $conversation = json_decode($response->object()->candidates[0]->content->parts[0]->text,true);
        foreach($conversation as $item)
        {
            $this->text .= $item['text'] . " ";
        }
    }

    public function get_file()
    {
        $validated = $this->validate([
            'file' => 'required|max:5120', // 5MB Max
        ]);
        
        return $validated['file'];
    }
    

    public function render()
    {
        return view('livewire.ai.voice2text');
    }
}
