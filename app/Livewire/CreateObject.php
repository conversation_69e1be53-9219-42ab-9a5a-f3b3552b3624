<?php

namespace App\Livewire;

use App\Models\Comment;
use App\Models\Objection;
use App\Models\Record;
use App\Models\Setting;
use App\Social\Slack;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use <PERSON>k<PERSON><PERSON>ser\Verta\Verta;

class CreateObject extends Component
{
    public string $identity;
    protected $queryString = ['identity'];
    public $objection;
    public $comments;
    public $channel_id = '';
    public $message;

    protected $rules = [
        'message' => 'required|string',
    ];

    public function mount()
    {
        $this->identity = urldecode($_GET['identity']);
    }

    public function delete($id)
    {
        $comment = Comment::find($id);
        if (! Gate::allows('delete-comment',$comment)) {
            abort(403);
        }
        $comment->delete();
        $this->dispatch('operation-successful');
    }

    public function save()
    {
        $this->validate();

        $record = Record::where('identity', $this->identity)->firstOrFail();

        $this->channel_id = $record?->user?->slack_id;
        $min_objection_date = Setting::where('name','min_objection_date')->first()->value;
        $min_objection_date = Verta::parse($min_objection_date)->toCarbon();

        $max_objection_date = Setting::where('name','max_objection_date')->first()->value;
        $max_objection_date = Verta::parse($max_objection_date)->toCarbon();

        if($record->incoming_date < $min_objection_date or now() > $max_objection_date)
        {
            $this->addError('custom', 'موعد ثبت اعتراض گذشته است.');
            return;
        }

        $this->objection = Objection::firstOrCreate([
            'record_id' => $record->id,
        ],[
            'status_change_count'=>0,
        ]);

        $this->objection->status = 'pending';
        
        DB::transaction(function () {
            Comment::create(
                [
                    'object_id' => $this->objection->id,
                    'user_id' => auth()->user()->id,
                    'value' => $this->message,
                    ]
                );
            $this->objection->save();
        });
        
        $this->message = "";
        $this->dispatch('operation-successful');
    }

    public function changeStatus($status)
    {
        if (! Gate::allows('change-status')) {
            abort(403);
        }

        $this->objection->status = $status;
        if(is_null($this->objection->status_change_count)) {
            $this->objection->status_change_count=1;
        } else {
            $this->objection->status_change_count+=1;
        }
        $this->objection->save();
        $text = "وضعیت اعتراض آپدیت شد";
        $text .= "\nhttps://qc.wallex.support/create/object?identity={$this->identity}";
        Slack::pv($text,$this->channel_id);
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        $record = Record::where('identity', $this->identity)->firstOrFail();
        $this->objection = Objection::where('record_id', $record->id)->first();
        $this->comments = [];
        if($this->objection) {
            $this->comments = Comment::where('object_id', $this->objection->id)->get();
        }

        return view('livewire.create-object');
    }
}
