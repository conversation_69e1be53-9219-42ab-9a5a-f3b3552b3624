<?php

namespace App\Livewire;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\Record;
use App\Models\User;
use Livewire\Component;

class ShowQcReport extends Component
{
    public $periods;
    public $parameters;
    public $users;
    public $qc_agent_id;

    public $qc_scores_done_by_them;
    public $qc_scores_done_on_them;

    public $objects;

    protected $rules = [
        'qc_agent_id' => 'required|exists:users,id',
    ];

    public function mount()
    {
        $this->periods = Period::where('id','>=',6)->get();

        $this->parameters = Parameter::whereIn('incoming_type', ['call','chat'])
        // ->where('active',true)
        ->get();

        $this->users = User::where('role_id',2)->get();

    }

    public function updated()
    {
        $this->validate();

        foreach($this->periods as $period) {

            // 
            $records_done_by_qc_team = Record::with(['qc_parameters'])
            ->where('identity','not like','%#qc')
            ->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->where('support_agent_id', $this->qc_agent_id)
            ->get();

            $this->qc_scores_done_by_them[$period->id] = $this->calc_score($records_done_by_qc_team);

            // 
            $records_done_on_qc_team = Record::with(['qc_parameters'])
            ->where('identity','like','%#qc')
            ->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->where('support_agent_id', $this->qc_agent_id)
            ->get();

            $this->qc_scores_done_on_them[$period->id] = $this->calc_score($records_done_on_qc_team);

            // 
            $acceptedObjects = Record::where('incoming_date',">=",$period->from)
            ->where('incoming_date',"<",$period->to)
            ->where('qc_agent_id',$this->qc_agent_id)
            ->whereHas('object', function ($q) {$q->where('status', 'accepted');})
            ->count();

            $records_handled_by_agent = Record::where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->where('qc_agent_id', $this->qc_agent_id)
            ->count();

            $this->objects[$period->id] = $records_handled_by_agent ? round($acceptedObjects / $records_handled_by_agent*100,2) : '-';
        }

    }

    public function calc_score($records)
    {
        $scores = [];
        $parameters_sum = [];
        $parameters_count = [];

        foreach($records->where('incoming_type', 'chat')->where('ignore',false) as $record) {
            $sum = 0.0;
            $total = 0.0;
            foreach ($record->qc_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;

                $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

            }
            if($sum) {
                $scores[] = $total / $sum * 100;
            }
        }

        foreach($records->where('incoming_type', 'call')->where('ignore',false) as $record) {
            $sum = 0.0;
            $total = 0.0;
            foreach ($record->qc_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;

                $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;
            }
            if($sum) {
                $scores[] = $total / $sum * 100;
            }
        }

        return count($scores) ? round(array_sum($scores) / count($scores), 2) : '-';
    }

    public function render()
    {
        return view('livewire.show-qc-report');
    }
}
