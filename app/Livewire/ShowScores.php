<?php

namespace App\Livewire;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\Score;
use App\Models\Setting;
use App\Models\User;
use Hek<PERSON>inasser\Verta\Facades\Verta;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;

class ShowScores extends Component
{
    public $users;
    public ?int $user_id = null;
    public $user;

    public $parameters;
    public $periods;
    public ?int $period_id = null;
    public $scores = [];
    public $notes = [];

    protected $rules = [
        'scores' => 'required|array',
        'notes' => 'required|array',
        'user_id' => 'required|exists:users,id',
        'period_id' => 'required|exists:periods,id',
    ];

    public function mount()
    {
        $this->users = User::when(auth()->user()->role_id == 1, function ($q) {
            $q->where('id', auth()->user()->id);
        })
        ->where('status',true)
        ->where('role_id', 1)
        ->get();

        $show_period = Setting::where('name', 'show_period')->first()->value;

        $this->periods = Period::when(auth()->user()->role_id == 1, function ($q) use ($show_period) {
            $q->where('id', "<=", $show_period);
        })
        ->get();
        // $this->periods = Period::take(24)->get();
    }

    public function updatedPeriodId()
    {

        !is_null($this->user_id) && $this->search();
    }

    public function updatedUserId()
    {
        !is_null($this->period_id) && $this->search();
    }

    public function search()
    {
        $this->user = User::with(['supervisor_parameters' => function ($q) {
            $q->wherePivot('period_id', $this->period_id);
        }])
        ->where('id', $this->user_id)->first();

        $this->parameters = Parameter::where('incoming_type', 'supervisor')
        ->where(function ($query) {
            $query->where('active', true)
                  ->orWhereIn('id', $this->user->supervisor_parameters->pluck('id')->toArray());
        })
        ->get();


        $this->scores = [];
        $this->notes = [];
        foreach ($this->parameters as $parameter) {
            $this->scores[$parameter->id] = null;
            $this->notes[$parameter->id] = null;
        }

        foreach($this->user->supervisor_parameters as $parameter) {
            $this->scores[$parameter->id] = $parameter->pivot->value;
            $this->notes[$parameter->id] = $parameter->pivot->note;
        }
    }

    public function changeScore($parameter_id, $score_value)
    {
        if(empty($score_value) and $score_value != 0) {
            $score_value = null;
        }
        $this->scores[$parameter_id] = $score_value;
    }

    public function changeNote($parameter_id, $note)
    {
        if(empty($note)) {
            $note = null;
        }
        $this->notes[$parameter_id] = $note;
    }

    public function save()
    {
        $agent = $this->users->where('id', $this->user_id)->first();

        if ($agent->supervisor_id and !Gate::allows('update-score', $agent)) {
            abort(403);
        }

        $validated = $this->validate();

        $lock_period = Setting::where('name', 'lock_period')->first()->value;

        if($this->period_id <= $lock_period) {
            $this->dispatch('operation-failed');
            $this->addError('custom', 'Editing of this period has been locked.');
            return;
        }

        foreach ($this->parameters as $parameter) {
            Score::updateOrCreate(
                [
                    'parameter_id' => $parameter->id,
                    'period_id' => $this->period_id,
                    'user_id' => $this->user_id,
                ],
                [
                    'value' => $this->scores[$parameter->id],
                    'note' => $this->notes[$parameter->id],
                ]
            );
        }
        $this->dispatch('operation-successful');
    }

    public function render()
    {

        return view('livewire.show-scores');
    }
}
