<?php

namespace App\Livewire;

use App\Imports\LivechatImport;
use App\Models\Goftino_chat;
use App\Models\Goftino_data;
use App\Models\Pool;
use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Gate;
use Hek<PERSON><PERSON>ser\Verta\Verta;
use Carbon\Carbon;
class UploadChatExcel extends Component
{
    use WithFileUploads;
    public $excel;
    public int $manual = 0;
    public $platform = 'wallex';

    public $from_date='';
    public $to_date='';

    public function livechat()
    {
        if (! Gate::allows('upload-qc')) {
            abort(403);
        }

        $validated = $this->validate([
            'excel' => 'extensions:xlsx|max:4096', // 1MB Max
        ]);
        $records = $this->process($validated['excel']);
        if($records) {
            // $records = $records[0];
            foreach ($records as &$record) {
                $record['created_at'] = now();
                $record['updated_at'] = now();
            }
            Pool::insertOrIgnore($records);
            $this->dispatch('operation-successful');
            return redirect()->route('pool');
        } else {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new LivechatImport(), $file);
        $array = $array[0];
        array_shift($array);
        $users = User::all();
        
        $support_agents = $users->where('role_id', 1)
        ->where('platform',$this->platform)
        ->where('weight', '<>', 0);
        
        $qc_agents = $users->where('role_id', 2)
        ->where('platform',$this->platform)
        ->where('trace_chat', '<>', 0);

        $records = [];

        foreach ($array as $record) {
            $duration = $record[5];
            if($duration < 200) {
                continue;
            }

            $email = $record[11];   // last operator id
            // $email = $record[21];   // operator 2 id
            $support_agent = $support_agents->firstWhere('email', $email);

            if(is_null($support_agent)) {
                continue;
            }

            // wallgold ai
            // if($support_agent->email == 'fa3864328c724b838336adea6d705f5b' and ($record[40] == 'offline' or $record[40]=='ai')) {

            // }
            
            $item = [
                'identity' => $record[0],
                'incoming_date' => \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($record[1]),
                'support_agent_id' => $support_agent->id,
                'incoming_type' => 'chat',
                'platform'=>$this->platform,
            ];

            array_push($records, $item);
        }

        $collection = collect($records);
        $support_agents = $support_agents->whereIn('id',$collection->pluck('support_agent_id')->all());
        $total_weigth = $support_agents->sum('weight');
        $target = $qc_agents->sum('trace_chat');
        
        $result = [];

        foreach($support_agents as $support_agent) {
            $count = ceil($support_agent->weight / $total_weigth * $target);
            $data = $collection->where('support_agent_id', $support_agent->id);
            $data_count = $data->count();
            if($count > $data_count)
            {
                $this->manual += $count - $data_count;
                $count = $data_count;
            }

            $random = $data->random($count);

            foreach($random->all() as $selected) {
                array_push($result, $selected);
            }
        }
        return $result;
    }

    

    public function goftino()
    {
        if (! Gate::allows('upload-qc')) {
            abort(403);
        }
        $users = User::all();
        
        $support_agents = $users->where('role_id', 1)
        ->where('platform',$this->platform)
        ->where('weight', '<>', 0);
        $qc_agents = $users->where('role_id', 2)
        ->where('platform',$this->platform)
        ->where('trace_chat', '<>', 0);

        $goftino_unique_chats = Goftino_chat::
        // where('created_at','>',now()->subDay())
        // whereBetween('created_at', [now()->subDays(1)->startOfDay(), Carbon::yesterday()->endOfDay()])  // edit
        whereBetween('created_at', [Verta::parse($this->from_date)->toCarbon()->startOfDay(), Verta::parse($this->to_date)->toCarbon()->endOfDay()])
        ->with('data')
        ->whereHas('data',function($q){
            $q->where('platform',$this->platform);
        })
        ->whereHas('user',function($q){
            $q->where('status',1)
            ->where('role_id',1);
        })
        ->distinct('chat_id')
        ->where('duration','>',0)
        ->get();

        $records = [];
        foreach ($goftino_unique_chats as $chat)
        {
            $item = [
                'identity' => $chat->chat_id,
                'incoming_date' => $chat->created_at,
                'support_agent_id' => $users->where('goftino_operator_id',$chat->operator_id)->first()->id,
                'incoming_type' => 'chat',
                'platform'=>$this->platform,
            ];

            array_push($records, $item);
        }
        $collection = collect($records);
        $support_agents = $support_agents->whereIn('id',$collection->pluck('support_agent_id')->all());
        $total_weigth = $support_agents->sum('weight');
        $target = $qc_agents->sum('trace_chat');
        $result = [];

        foreach($support_agents as $support_agent) {
            $count = ceil($support_agent->weight / $total_weigth * $target);
            $data = $collection->where('support_agent_id', $support_agent->id);
            $data_count = $data->count();
            if($count > $data_count)
            {
                $this->manual += $count - $data_count;
                $count = $data_count;
            }

            $random = $data->random($count);

            foreach($random->all() as $selected) {
                array_push($result, $selected);
            }
        }

        if($result) {
            foreach ($result as &$record) {
                $record['created_at'] = now();
                $record['updated_at'] = now();
            }
            Pool::insertOrIgnore($result);
            $this->dispatch('operation-successful');
            return redirect()->route('pool');
        } else {
            $this->dispatch('operation-failed');
        }
    }

    public function render()
    {
        return view('livewire.upload-chat-excel');
    }
}
