<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\Ticket;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON><PERSON><PERSON>ser\Verta\Verta;
use Illuminate\Support\Facades\Gate;

class ShowTickets extends Component
{
    use WithPagination;
    public $support_agents;
    public $support_agent_id;
    public $supervisors = [];
    public $supervisor_id;
    public $from_date = '';
    public $to_date = '';
    public $subject = '';
    public $status = '';
    public $periods;
    public $period_id;

    public function mount()
    {
        $this->support_agents = User
        ::when(auth()->user()->role_id === 1,function($q){
            $q->where('id',auth()->user()->id);
        })
        ->where('role_id',1)->get();

        $this->supervisors = User
        ::where('role_id',3)->get();

        $this->periods = Period::all();
    }

    public function delete($id)
    {
        if (! Gate::allows('delete-ticket')) {
            abort(403);
        }
        Ticket::find($id)->delete();
    }

    public function change ($id,$status)
    {
        $ticket = Ticket::find($id);
        if (! Gate::allows('change-ticket',$ticket)) {
            // abort(403);
        }
        $ticket->status = $status;
        $ticket->updated_by=auth()->user()->id;
        $ticket->save();
    }

    public function render()
    {
        $tickets = Ticket::
        when(auth()->user()->role_id === 1 ,function($q){
            $q->where('support_agent_id',auth()->user()->id)
            ->where('status','<>',false);
        })
        ->when($this->support_agent_id , function($q){
            $q->where('support_agent_id',$this->support_agent_id);
        })
        ->when($this->supervisor_id , function($q){
            $q->whereRelation('user','supervisor_id',$this->supervisor_id);
        })
        ->when($this->from_date, function ($q) {
            $q->where('creation_date', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date, function ($q) {
            $q->where('creation_date', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->when($this->period_id, function ($q) {
            $q->where('creation_date', '>=', $this->periods->where('id',$this->period_id)->first()->from)
            ->where('creation_date', '<', $this->periods->where('id',$this->period_id)->first()->to);
        })
        ->when($this->subject, function ($q) {
            $q->where('subject', '=', $this->subject);
        })
        ->when(in_array($this->status,[0,1]), function ($q) {
            $q->where('status', '=', $this->status);
        })
        ->when($this->status=='null', function ($q) {
            $q->whereNull('status');
        })
        ->paginate(100);

        return view('livewire.show-tickets',compact('tickets'));
    }
}
