<?php

namespace App\Livewire\Announcements;

use App\Models\Announcement;
use App\Models\Category;
use App\Models\User;
use App\Social\Slack;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;
use <PERSON>k<PERSON>inasser\Verta\Verta;
class Archive extends Component
{
    public $users = [];
    public $user_id;

    public $categories = [];
    public $category_id;

    public $search = '';
    public $channel = '';

    public $from_date = '';
    public $to_date = '';

    public $event = false;

    public function mount()
    {
        $this->users = User::where('status',true)->where('role_id','>',1)->get();
        $this->categories = Category::all();
    }

    public function delete($id)
    {
        $announcement = Announcement::find($id);
        if(!Gate::allows('delete-announcement',$announcement)){
            abort(403);
        }

        Slack::announcement_delete($announcement);
        $announcement->delete();

        $this->dispatch('operation-successful');
    }

    public function get_reacts($id)
    {
        $announcement = Announcement::find($id);
        $response = Slack::get_reacts($announcement);
        $reaction_users = [];
        $reactions = $response->message->reactions ?? [];
        foreach($reactions as $reaction)
        {
            $reaction_users = array_unique(array_merge($reaction_users,$reaction->users));
        }

        $platform = '';
        switch($announcement->channel)
        {
            case 'C044AHLCKCY':
                $platform = 'wallex';
            break; 

            case 'C0826FMK266':
                $platform = 'wallgold';
            break; 
        }

        $users = User::
        when($platform,function($q) use ($platform){
            $q->where('platform',$platform);
        })
        ->where('status',true)
        ->whereIn('role_id',[1,2,3])
        ->whereIn('slack_id',$reaction_users)
        ->select('id')->get();

        $announcement->reacted_by = $users->pluck('id')->toArray();
        $announcement->save();

        $not_users = User::
        when($platform,function($q) use ($platform){
            $q->where('platform',$platform);
        })
        ->where('status',true)
        ->whereIn('role_id',[1,2,3])
        ->whereNotIn('slack_id',$reaction_users)
        ->select('name')->get();

        dump($not_users->pluck('name')->toArray());
    }
    
    public function render()
    {
        $announcements = Announcement::
        when($this->user_id,function($q){
            $q->where('user_id',$this->user_id);
        })
        ->when($this->category_id,function($q){
            $q->where('category_id',$this->category_id);
        })
        ->when($this->search,function($q){
            $q->where(function($query){
                $query
                ->where('subject','LIKE',"%{$this->search}%")
                ->orWhere('body','LIKE',"%{$this->search}%")
                ->orWhere('id','LIKE',"%{$this->search}%");
            });
        })
        ->when($this->channel,function($q){
            $q->where('channel',$this->channel);
        })
        ->when($this->from_date,function($q){
            $q->where('created_at','>=',Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at','<',Verta::parse($this->to_date)->toCarbon());
        })
        ->latest()
        ->when(in_array(auth()->user()->role_id,[1,2,3]) and auth()->user()->platform=='wallgold',function($q){
            $q->whereIn('channel',['C0826FMK266','C0823UKN4CC','C08EYMKQWKU']);
        })
        ->when(in_array(auth()->user()->role_id,[1,2,3]) and auth()->user()->platform=='wallex',function($q){
            $q->whereIn('channel',['C044AHLCKCY','C04LB9KH83X','C03UUD0135X']);
        })
        ->when($this->event,function($q){
            $q->where('event',true);
        })
        ->paginate(100);

        return view('livewire.announcements.archive',compact('announcements'));
    }
}
