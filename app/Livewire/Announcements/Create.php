<?php

namespace App\Livewire\Announcements;

use App\Models\Announcement;
use App\Models\Category;
use App\Social\Slack;
use Livewire\WithFileUploads;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;

class Create extends Component
{
    use WithFileUploads;
    public $channel = "C0889QPAMPD";

    public string $subject = "";

    public string $tree = "";

    public string $body = "";

    public string $url = "";

    public $category_id;
    public $categories = [];

    public bool $slack = true;
    public bool $bale = true;
    public bool $mention = false;
    public bool $event = false;
    public $files = [];

    protected $rules = [
        'subject'=>'required|string',
        'body'=>'required|string',
        'tree'=>'string',
        'slack'=>'boolean',
        'mention'=>'boolean',
        'event'=>'boolean',
        'category_id'=>'required|exists:categories,id',
        'files'=>'array',
        'files.*'=>'file|max:5120',
        'url'=>'url',
    ];

    public function mount()
    {
        $this->categories = Category::all();
    }
    
    public function create()
    {
        $this->validate();
        
        if(!Gate::allows('create-announcement')){
            abort(403);
        }

        $file_id_list = [];
        
        if($this->slack or sizeof($this->files))
        {
            $now = verta(now())->formatDate();
            $option = $this->categories->where('id',$this->category_id)->first()->option;
            $name = auth()->user()->name;
            
            $text = "طبقه بندی: {$option}\n\n";
            $text .= "ارسال شده توسط: {$name}\n\n";
            $text .= "تاریخ: {$now}\n\n";
            $text .= "*{$this->subject}*\n\n";
            $text .= "{$this->body}";

            if($this->url)
            {
                $text .= "\n\n<{$this->url}|لینک مرتبط>";
            }
            if($this->mention)
            {
                //  <!here>
                // <@U02SRHL4A6P>
                // <!subteam^S05U05FTGEM>
                $text .= "\n\n<!channel>";
            }
            $ts = Slack::announcement($text,$this->channel);
            $file_id_list = Slack::announcement_attachments($this->files,$ts,$this->channel);
        }

        if($this->bale and in_array($this->channel,['C044AHLCKCY','C0826FMK266']))
        {
            $now = verta(now())->formatDate();
            $option = $this->categories->where('id',$this->category_id)->first()->option;
            $name = auth()->user()->name;

            $text = "طبقه بندی: {$option}\n\n";
            $text .= "ارسال شده توسط: {$name}\n\n";
            $text .= "تاریخ: {$now}\n\n";
            $text .= "*{$this->subject}*\n\n";
            $text .= "{$this->body}";

            if($this->url)
            {
                $text .= "\n\n[لینک مرتبط]({$this->url})";
            }
            if($this->mention)
            {
                // 
            }

            switch($this->channel)
            {
                // اطلاع رسانی والکس
                case 'C044AHLCKCY':
                $chat_id='5249760993';
                break;

                // اطلاع رسانی وال گلد
                case 'C0826FMK266':
                $chat_id='4938624464';
                break;
            }
            Http::post('https://tapi.bale.ai/bot225578726:j1QdhvTVy7i3CvyhorQESs9vaXSIcO5LOONeg1MU/sendMessage',[
                'chat_id'=>$chat_id,
                'text'=>$text,
            ]);
        }


        $announcement = Announcement::create([
            'subject' => $this->subject,
            'body' => $this->body,
            'user_id' => auth()->user()->id,
            'slack'=>$this->slack or sizeof($this->files),
            'mention'=>$this->mention,
            'thread_ts'=>$ts ?? null,
            'channel'=>$this->channel,
            'category_id'=>$this->category_id,
            'tree'=>$this->tree ?: null,
            'event'=>$this->event,
            // 'files'=>sizeof($this->files)>0,
            'files'=>empty($file_id_list) ? null : json_encode($file_id_list),
            'url'=>$this->url ?: null,
        ]);

        // metabase
        $timestamp = $announcement->created_at->timestamp;
        if($this->event)
        {
            $question_id_list = [2342];
            foreach($question_id_list as $question_id)
            {
                // http://*************:9100/insert_event
                $endpoint = "http://*************:4400/insert_event";
                Http::post($endpoint,[
                    'name'=>trim($this->subject) . " ({$timestamp})",
                    'description'=>mb_convert_encoding(substr($this->body, 0, 255),'UTF-8','UTF-8'),
                    'question_id'=>$question_id,
                    'timestamp'=>now()->format('Y-m-d'),
                ]);
            }
        }
        
        $this->dispatch('operation-successful');
        // return redirect()->route('announcements-archive');
    }

    public function render()
    {
        return view('livewire.announcements.create');
    }
}
