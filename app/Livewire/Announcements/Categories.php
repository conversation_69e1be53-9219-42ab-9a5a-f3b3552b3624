<?php

namespace App\Livewire\Announcements;

use App\Models\Category;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;

class Categories extends Component
{
    public $filter = false;

    public function create()
    {

    }
    public function delete($id)
    {
        if(!Gate::allows('delete-category'))
        {
            abort(403);
        }
    }
    public function toggle($id)
    {

    }
    public function render()
    {
        $categories = Category::latest()->get();
        return view('livewire.announcements.categories');
    }
}
