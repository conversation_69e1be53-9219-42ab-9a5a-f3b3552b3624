<?php

namespace App\Livewire\Announcements;

use App\Models\Announcement;
use App\Models\Category;
use App\Social\Slack;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
// use Livewire\WithFileUploads;

class Edit extends Component
{
    public Announcement $announcement;
    // use WithFileUploads;

    public string $subject = "";

    public ?bool $event;

    public string $tree = "";

    public string $body = "";

    public string $url = "";

    public $created_at = "";

    public $category_id;
    public $categories = [];

    public function mount()
    {
        $this->categories = Category::all();

        $this->category_id = $this->announcement->category_id;
        $this->tree = $this->announcement->tree ?? '';
        $this->subject = $this->announcement->subject;
        $this->body = $this->announcement->body;
        $this->event = $this->announcement->event;
        $this->created_at = $this->announcement->created_at;
    }

    public function convert()
    {
        $timestamp = $this->announcement->created_at->timestamp;
        $question_id_list = [2342];
        foreach($question_id_list as $question_id)
        {
            $endpoint = "http://*************:9100/insert_event";
            $r = Http::post($endpoint,[
                
                'name'=>trim($this->subject) . " ({$timestamp})",
                'description'=>mb_convert_encoding(substr($this->body, 0, 255),'UTF-8','UTF-8'),
                'question_id'=>$question_id,
                'timestamp'=>$this->announcement->created_at->format('Y-m-d'),
                // 'timestamp'=>Carbon::parse($this->created_at)->format('Y-m-d'),
            ]);
        }
        $this->announcement->event = true;
        $this->announcement->save();
        $this->dispatch('operation-successful');
    }

    protected $rules = [
        'subject'=>'required|string',
        'body'=>'required|string',
        'tree'=>'string',
        'category_id'=>'required|exists:categories,id',
        'url'=>'url',
    ];

    public function save()
    {
        $validated = $this->validate();
        
        if(!Gate::allows('create-announcement')){
            abort(403);
        }

        $now = verta(now())->formatDate();
        $option = $this->categories->where('id',$this->category_id)->first()->option;
        $name = auth()->user()->name;

        $text = "طبقه بندی: {$option}\n\n";
        $text .= "ویرایش شده توسط: {$name}\n\n";
        $text .= "تاریخ: {$now}\n\n";
        $text .= "*{$this->subject}*\n\n";
        $text .= "{$this->body}";

        if($this->announcement->url)
        {
            $text .= "\n\n<{$this->announcement->url}|لینک مرتبط>";
        }

        if($this->announcement->mention)
        {
            $text .= "\n\n<!channel>";
        }

        Slack::announcement_update($text,$this->announcement);
        
        $validated['updated_by'] = auth()->user()->id;
        $this->announcement->update($validated);
        
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        return view('livewire.announcements.edit');
    }
}
