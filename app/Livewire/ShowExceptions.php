<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Exception;
use App\Models\User;
use Illuminate\Support\Facades\Gate;

class ShowExceptions extends Component
{
    // استثئات
    // تذکرات
    // رخداد ها

    public string $note = '';
    public $user_id;
    public $support_agents;

    protected $rules = [
        'note' => 'required',
        'user_id' => 'nullable|integer',
    ];
    
    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->where('status',true)->get();
    }

    public function add()
    {
        $this->validate();

        Exception::create([
            // 'user_id'=>auth()->user()->id,
            'user_id'=>$this->user_id ?? null,
            'note'=>$this->note,
        ]);
        $this->dispatch('operation-successful');
    }

    public function delete($exception_id)
    {
        if (!Gate::allows('delete-exception')) {
            abort(403);
        }
        $exception = Exception::find($exception_id);
        $exception->delete();
        $this->dispatch('operation-successful');
    }

    public function changeStatus($exception_id,$status)
    {
        if (!Gate::allows('verify-exception')) {
            abort(403);
        }
        $exception = Exception::find($exception_id);
        $exception->verified = $status;
        $exception->updated_by = auth()->user()->id;

        $exception->save();
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        $exceptions = Exception::
        when($this->user_id,function($q){
            $q->where('user_id',$this->user_id);
        })
        ->latest()
        ->paginate(50);
        
        return view('livewire.show-exceptions',compact('exceptions'));
    }
}
