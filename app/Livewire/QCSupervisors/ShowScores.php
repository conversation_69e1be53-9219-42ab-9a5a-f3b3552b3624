<?php

namespace App\Livewire\QCSupervisors;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\Score;
use App\Models\User;
use Livewire\Component;

class ShowScores extends Component
{

    public $parameters;
    public $period_id;
    public $user_id;
    public $user;
    public $periods;
    public $users;
    public $scores = [];
    public $notes = [];

    public function mount()
    {
        $this->users = User::where('role_id',3)
        ->when(auth()->user()->role_id == 3, function ($q) {
            $q->where('id', auth()->user()->id);
        })
        ->get();
        $this->periods = Period::where('id','>',8)->get();
    }

    protected $rules = [
        'scores' => 'required|array',
        'notes' => 'required|array',
        'user_id' => 'required|exists:users,id',
        'period_id' => 'required|exists:periods,id',
    ];

    public function updatedPeriodId()
    {

        !is_null($this->user_id) && $this->search();
    }

    public function updatedUserId()
    {
        !is_null($this->period_id) && $this->search();
    }

    public function search()
    {
        $this->user = User::with(['supervisor_parameters' => function ($q) {
            $q->wherePivot('period_id', $this->period_id);
        }])
        ->where('id', $this->user_id)->first();
        
        $this->parameters = Parameter::where('incoming_type', 'supervisor_head')
        ->where(function ($query) {
            $query->where('active', true)
                  ->orWhereIn('id', $this->user->supervisor_parameters->pluck('id')->toArray());
        })
        ->get();

        $this->scores = [];
        $this->notes = [];
        foreach ($this->parameters as $parameter) {
            $this->scores[$parameter->id] = null;
            $this->notes[$parameter->id] = null;
        }

        foreach($this->user->supervisor_parameters as $parameter) {
            $this->scores[$parameter->id] = $parameter->pivot->value;
            $this->notes[$parameter->id] = $parameter->pivot->note;
        }
    }

    public function changeScore($parameter_id, $score_value)
    {
        if(empty($score_value) and $score_value != 0) {
            $score_value = null;
        }
        $this->scores[$parameter_id] = $score_value;
    }

    public function changeNote($parameter_id, $note)
    {
        if(empty($note)) {
            $note = null;
        }
        $this->notes[$parameter_id] = $note;
    }

    public function save()
    {
        $validated = $this->validate();

        foreach ($this->parameters as $parameter) {
            Score::updateOrCreate(
                [
                    'parameter_id' => $parameter->id,
                    'period_id' => $this->period_id,
                    'user_id' => $this->user_id,
                ],
                [
                    'value' => $this->scores[$parameter->id],
                    'note' => $this->notes[$parameter->id],
                ]
            );
        }
        $this->dispatch('operation-successful');
    }
    

    public function render()
    {
        return view('livewire.q-c-supervisors.show-scores');
    }
}
