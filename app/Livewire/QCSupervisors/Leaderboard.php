<?php

namespace App\Livewire\QCSupervisors;

use Livewire\Component;

use App\Models\Period;
use App\Models\User;

class Leaderboard extends Component
{
    public $users;
    public $periods = 0;
    public $scores = [];

    public function mount()
    {
        $this->users = User::where('role_id',3)->get();
    }

    public function updated()
    {
        $periods = Period::latest()->limit($this->periods)->get();

        $users = User::where('role_id',3)->with(['supervisor_parameters' => function ($q) use ($periods) {
            $q->wherePivotIn('period_id',$periods->pluck('id')->all());
        }])
        ->get();
        $scores = [];

        foreach($users as $user)
        {
            $sum = 0;
            $total = 0;
            foreach($user->supervisor_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            $scores[]=[
                'name'=>$user->name,
                'score'=>$sum ? round($total / $sum * 100, 2) : '-',
            ];
        }
        
        $this->scores = collect($scores)->sortByDesc('score');
    }

    public function render()
    {
        return view('livewire.q-c-supervisors.leaderboard');
    }
}
