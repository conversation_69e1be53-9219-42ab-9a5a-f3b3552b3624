<?php

namespace App\Livewire\QCSupervisors;

use Livewire\Component;
use App\Models\Period;
use App\Models\User;

class ShowReport extends Component
{
    public $users;
    public $periods;
    public $support_agent_id;
    public $supervisor_scores = [];
    
    public function mount()
    {
        $role_id = auth()->user()->role_id;
        if($role_id<3) {abort(403);}

        $this->users = User::when($role_id == 3, function ($q) {
            $q->where('id', auth()->user()->id);
        })
        ->where('status', true)
        ->whereIn('role_id', [3])
        ->get();

        $this->periods = Period::
        where('id',">",8)
        ->get();
    }

    public function updated()
    {
            foreach($this->periods as $period) {
            // calc total head score
            $sum = 0.0;
            $total = 0.0;

            $user = User::with(['supervisor_parameters' => function ($q) use ($period) {
                $q->wherePivot('period_id', $period->id);
            }])
            ->where('id', $this->support_agent_id)->first();
            
            foreach($user->supervisor_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            $this->supervisor_scores[$period->id] = $sum ? round($total / $sum * 100, 2) : '-';
        }
    }

    public function render()
    {
        return view('livewire.q-c-supervisors.show-report');
    }
}
