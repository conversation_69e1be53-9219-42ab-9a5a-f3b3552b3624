<?php

namespace App\Livewire\Livechat;

use App\Models\Queue_abandonment;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
class ShowQueueAbandonment extends Component
{
    use WithPagination;
    public string $search = '';
    protected $queryString = ['search'];
    public $from_date;
    public $to_date;

    public function render()
    {
        $chats = Queue_abandonment::latest()
        ->with('livechat')
        ->when($this->search,function($q){
            $q->whereHas('livechat',function($query){
                $query->where('thread_id','LIKE',"%{$this->search}%")->orWhere('chat_id','LIKE',"%{$this->search}%");
            });
        })
        ->when($this->from_date,function($q){
            $q->where('created_at', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->paginate(10);

        return view('livewire.livechat.show-queue-abandonment',compact('chats'));
    }
}
