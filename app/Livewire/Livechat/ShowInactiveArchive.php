<?php

namespace App\Livewire\Livechat;

use App\Models\Inactive_archive;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class ShowInactiveArchive extends Component
{
    use WithPagination;
    
    public string $search = '';
    protected $queryString = ['search'];

    public $from_date;
    public $to_date;

    public $support_agent_email;
    public $support_agents;

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
    }
    
    public function render()
    {
        $chats = Inactive_archive::latest()
        ->with('livechat')
        ->when($this->search,function($q){
            $q->whereHas('livechat',function($query){
                $query->where('thread_id','LIKE',"%{$this->search}%")->orWhere('chat_id','LIKE',"%{$this->search}%");
            });
        })
        ->when($this->from_date,function($q){
            $q->where('created_at', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->when($this->support_agent_email,function($q){
            $q->whereHas('livechat',function($query){
                $query->where('author_id',$this->support_agent_email);
            });
        })
        ->paginate(10);

        return view('livewire.livechat.show-inactive-archive',compact('chats'));
    }
}
