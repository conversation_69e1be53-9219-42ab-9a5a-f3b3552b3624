<?php

namespace App\Livewire\Livechat;

use App\Models\Signed_out_transfer;
use Livewire\Component;
use Livewire\WithPagination;
use App\Models\User;
use <PERSON><PERSON><PERSON><PERSON>ser\Verta\Verta;

class ShowSignedOutTransfer extends Component
{
    use WithPagination;
    public string $search = '';
    protected $queryString = ['search'];
    public $from_date;
    public $to_date;
    
    public $support_agents;
    public $support_agent_email;

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
    }

    public function render()
    {
        $chats = Signed_out_transfer::latest()
        ->when($this->search,function($q){
            $q->whereHas('livechat',function($query){
                $query->where('thread_id','LIKE',"%{$this->search}%")->orWhere('chat_id','LIKE',"%{$this->search}%");
            });
        })
        ->when($this->from_date,function($q){
            $q->where('created_at', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->when($this->support_agent_email,function($q){
            $q->where('agent_removed',$this->support_agent_email);
        })
        ->with('livechat')
        ->paginate(10);
        
        return view('livewire.livechat.show-signed-out-transfer',compact('chats'));
    }
}
