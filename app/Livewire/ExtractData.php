<?php

namespace App\Livewire;


use Livewire\Component;
use Illuminate\Support\Facades\Gate;
use App\Models\Period;
use App\Models\Setting;

use App\AI\Extractor\Chat_count;
use App\AI\Extractor\Chat_AHT;
use App\AI\Extractor\Wrong_subject;
use App\AI\Extractor\Wrong_SL;
use App\AI\Extractor\Wrong_user;
use App\AI\Extractor\Position_logs;
use App\AI\Extractor\Repetitive;
use App\AI\Extractor\Waiting_queue;
use App\AI\Extractor\Leave_announcement;
use App\AI\Extractor\Back_to_call;
use App\AI\Extractor\Chat_unassign;
use App\AI\Extractor\Lost_connections;

class ExtractData extends Component
{
    public $period_id;
    public $period;
    public $periods;

    protected $queryString = ['period_id'];

    public function mount()
    {
        $this->periods = Period::where('id',">=",7)->get();
    }

    public function check_lock()
    {
        $this->period = $this->periods->where('id', $this->period_id)->first();

        if (! Gate::allows('extract-data')) {
            abort(403);
        }
        $lock_period = Setting::where('name', 'lock_period')->first()->value;

        if($this->period_id <= $lock_period) {
            $this->dispatch('operation-failed');
            $this->addError('custom', 'Editing of this period has been locked.');
            return true;
        } else {
            return false;
        }
    }

    public function extract_everything()
    {
        $this->extract_chat_count();
        $this->extract_chat_aht();
        // $this->extract_lost_connection();
        $this->extract_unassigned_chats();

        $this->extract_wrong_subject();
        $this->extract_repetitive();
        $this->extract_wrong_user();
        $this->extract_wrong_sl();
        $this->extract_back_to_call();

        $this->extract_position_logs();
        $this->extract_waiting_queue();

        $this->extract_leave_announcement();
    }

    public function extract_chat_count()
    {
        if($this->check_lock()) {
            return;
        }

        $extractor = new Chat_count;
        $extractor->index($this->period);
        
        $this->dispatch('operation-successful');
    }
    
    public function extract_chat_aht()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Chat_AHT;
        $extractor->index($this->period);

        $this->dispatch('operation-successful');
    }

    public function extract_unassigned_chats()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Chat_unassign;
        $extractor->index($this->period);

        $this->dispatch('operation-successful');

    }

    public function extract_lost_connection()
    {
        if($this->check_lock()) {
            return;
        }

        $extractor = new Lost_connections;
        $extractor->index($this->period);

        $this->dispatch('operation-successful');
    }

    public function extract_wrong_subject()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Wrong_subject;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }

    public function extract_repetitive()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Repetitive;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }

    public function extract_wrong_user()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Wrong_user;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }

    public function extract_wrong_sl()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Wrong_SL;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }

    public function extract_back_to_call()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Back_to_call;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }

    public function extract_position_logs()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Position_logs;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }

    public function extract_waiting_queue()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Waiting_queue;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }

    public function extract_leave_announcement()
    {
        if($this->check_lock()) {
            return;
        }
        $extractor = new Leave_announcement;
        $extractor->index($this->period);
        $this->dispatch('operation-successful');
    }
    
    public function render()
    {
        return view('livewire.extract-data');
    }
}
