<?php

namespace App\Livewire\Campaigns\Second;

use App\Models\Campaign;
use Livewire\Component;

class Help extends Component
{
    public $currentStep = 'initial';    // identifier

    public $campaign;

    public $call_status = '';

    public $why_not_buy = '';
    public $why_register = '';
    public $will_buy;
    public $how_familiar = '';
    public $city = '';

    public $description = '';

    public $male;

    public $user_level = '';

    public function mount()
    {
        $this->campaign = Campaign::where('admin_id',request()->input('admin_id',null))->firstOrFail();

        $this->call_status = $this->campaign->call_status;
        $this->why_not_buy = $this->campaign->why_not_buy ?? '';
        $this->why_register = $this->campaign->why_register ?? '';
        $this->will_buy = $this->campaign->will_buy;
        $this->male = $this->campaign->male;
        $this->how_familiar = $this->campaign->how_familiar ?? '';
        $this->city = $this->campaign->city ?? '';
        $this->description = $this->campaign->description ?? '';
        $this->user_level = $this->campaign->user_level ?? '';
    }

    protected $rules = [
        'call_status'=>'required|string',
        'why_not_buy'=>'string',
        'why_register'=>'string',
        'will_buy'=>'boolean|nullable',
        'male'=>'boolean|nullable',
        'how_familiar'=>'string',
        'city'=>'string',
        'description'=>'string',
        'user_level'=>'integer|nullable',
    ];

    public $statuses = [
        'پاسخگو بود',
        'پاسخگو نبود',
        'عدم همکاری',
        'درخواست تماس در زمان دیگر',
        'خرید طلا قبل از تماس',
    ];

    public function resetFlow()
    {
        $this->reset('currentStep');
    }

    public function goToStep($step)
    {
        switch ($step)
        {
            // details
            case 'initial':
                $this->reset(['call_status','why_not_buy','why_register','will_buy','how_familiar','city','description']);
            break;

            case 'no_response':
                $this->call_status = 'پاسخگو نبود';
                $this->reset(['why_not_buy','why_register','will_buy','how_familiar','city','description']);
            break;

            case 'no_cooperation':
                $this->call_status = 'عدم همکاری';
                $this->reset(['why_not_buy','why_register','will_buy','how_familiar','city','description']);
            break;
                
            case 'call_later':
                $this->call_status = 'درخواست تماس در زمان دیگر';
                $this->reset(['why_not_buy','why_register','will_buy','how_familiar','city','description']);
            break;

            case 'yes_no_problem':
                $this->call_status='پاسخگو بود';
            break;

            case 'yes_will':
                $this->will_buy=1;
            break;

            case 'no_wont':
                $this->will_buy=0;
            break;
            
            // custom 
            
            

            default:
        }
        $this->currentStep = $step;
    }

    public function save()
    {
        if($this->user_level=='')
        {
            $this->user_level = null;
        }
        $validatedData = $this->validate();
        $validatedData['called_at'] = now();
        $this->campaign->update($validatedData);
        $this->dispatch('operation-successful');
    }

    public function next()
    {
        $admin_id = Campaign::
        where(function($q){
            $q->whereNull('call_status')
            ->orWhere('call_status','');
        })
        ->where('user_id',auth()->user()->id)->inRandomOrder()->first()?->admin_id;

        if($admin_id) {
            return redirect()->to("/campaigns/second/help?admin_id={$admin_id}");
        } else {
            $this->dispatch('operation-failed');
        }
    }
    
    public function render()
    {
        return view('livewire.campaigns.second.help');
    }
}
