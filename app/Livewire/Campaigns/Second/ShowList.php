<?php

namespace App\Livewire\Campaigns\Second;

use Livewire\WithPagination;
use App\Models\Campaign;

use Livewire\Component;

class ShowList extends Component
{
    use WithPagination;
    public $without_result = false;
    public $agent_but_without_result = false;
    public $only_me = false;
    public $call_again = false;
    public $search = '';

    public $statuses = [
        'پاسخگو بود',
        'پاسخگو نبود',
        'عدم پاسخگویی',
        'عدم همکاری',
        'درخواست تماس در زمان دیگر',
    ];
    
    public function assign($id)
    {
        $campaign = Campaign::find($id);
        $campaign->user_id = auth()->user()->id;
        $campaign->save();
        $this->dispatch('operation-successful');
    }

    public function checkBeingAssigned($campaign)
    {
        if(is_null($campaign->user_id))
        {
            abort(403);
        }
    }

    public function changeStatus($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->call_status = $value;
        $campaign->save();
    }


    public function changeDescription($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->why_not_buy = $value ?? '';
        $campaign->save();
    }

    public function deleteNull()
    {
        if(auth()->user()->role_id>3)
        {
            Campaign::whereNull('call_status')->whereNull('user_id')->delete();
        }
    }

    public function render()
    {
        return view('livewire.campaigns.second.show-list',
        [
            'list'=>Campaign::
                    when($this->only_me,function($q){
                        $q->where('user_id',auth()->user()->id);
                    })
                    ->when($this->without_result,function($q){
                        $q->where(function($query){
                            $query->whereNull('call_status')
                            ->orWhere('call_status','');
                        });
                    })
                    ->when($this->agent_but_without_result,function($q){
                        $q->whereNotNull('user_id')
                        ->where(function($query){
                            $query->whereNull('call_status')
                            ->orWhere('call_status','');
                        });
                    })
                    ->when($this->call_again,function($q){
                        $q->where('call_status','درخواست تماس در زمان دیگر');
                    })
                    ->when($this->search,function($q){
                        $q->where('admin_id','LIKE',"%{$this->search}%");
                    })
                    ->orderbyDesc('id')
                    ->paginate(50)
        ]
    );
    }
}
