<?php

namespace App\Livewire\Campaigns\First;

use App\Models\Campaign;
use Livewire\Component;

class Help extends Component
{
    public $currentStep = 'initial';    // identifier

    public $campaign;

    public $call_status;
    public $call_result = '';
    public $familiar = null;
    public $used  = null;
    public $experience = null;
    public string $description = '';

    public $phone_number;

    public function mount()
    {
        $this->campaign = Campaign::where('phone_number',request()->input('phone_number',null))->firstOrFail();
        $this->call_status = $this->campaign->call_status;
        $this->call_result = $this->campaign->call_result;
        $this->familiar = $this->campaign->familiar;
        $this->used = $this->campaign->used;
        $this->experience = $this->campaign->experience;
        $this->description = $this->campaign->description;
    }

    protected $rules = [
        'call_status'=>'required|string',
        'call_result'=>'string',
        'familiar'=>'boolean|nullable',
        'used'=>'boolean|nullable',
        'experience'=>'boolean|nullable',
        'description'=>'string',
    ];

    public $statuses = [
        'پاسخگو بود',
        'عدم پاسخگویی',
        'عدم همکاری',
        'درخواست تماس در زمان دیگر',
    ];
    
    public $results = [
        'عدم همکاری',
        'درخواست تماس در ساعات دیگر',
        'اعلام تمایل به خرید در والگلد داشت',
        'خیلی تمایلی نشان ندادند',
    ];

    public function resetFlow()
    {
        $this->reset('currentStep');
    }

    public function goToStep($step)
    {
        switch ($step)
        {
            case 'initial':
                $this->reset(['familiar','used','experience','call_status','call_result']);
            break;

            case 'no_response':
                $this->call_status = 'عدم پاسخگویی';
                $this->reset(['call_result']);
            break;

            case 'no_cooperation':
                $this->call_status = 'عدم همکاری';
                $this->call_result = 'عدم همکاری';
            break;
                
            case 'call_later':
                $this->call_status = 'درخواست تماس در زمان دیگر';
                $this->call_result = 'درخواست تماس در ساعات دیگر';
            break;
            
            case 'yes_familiar':
                $this->familiar='1';
            break;
            
            case 'not_familiar':
                $this->familiar='0';
            break;

            case 'yes_buy':
                $this->used='1';
            break;

            case 'not_buy':
                $this->used='0';
            break;

            case 'yes_experience':
                $this->experience='1';
            break;

            case 'not_experience':
                $this->experience='0';
            break;

            case 'yes_no_problem':
                $this->call_status='پاسخگو بود';
            break;

            default:
        }
        $this->currentStep = $step;
    }

    public function save()
    {
        $validatedData = $this->validate();
        $validatedData['called_at'] = now();
        $this->campaign->update($validatedData);
        $this->dispatch('operation-successful');
    }

    public function next()
    {
        $phone_number = Campaign::
        where(function($q){
            $q->whereNull('call_status')
            ->orWhere('call_status','');
        })
        ->where(function($q){
            $q->whereNull('call_result')
            ->orWhere('call_result','');
        })->where('user_id',auth()->user()->id)->inRandomOrder()->first()?->phone_number;

        if($phone_number) {
            return redirect()->to("/campaigns/help?phone_number={$phone_number}");
        } else {
            $this->dispatch('operation-failed');
        }
    }

    public function render()
    {
        return view('livewire.campaigns.first.help');
    }
}
