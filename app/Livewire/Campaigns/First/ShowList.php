<?php

namespace App\Livewire\Campaigns\First;
use Livewire\WithPagination;
use App\Models\Campaign;

use Livewire\Component;

class ShowList extends Component
{
    use WithPagination;
    public $without_result = false;
    public $only_me = false;
    public $call_again = false;
    public $search = '';

    public $statuses = [
        'پاسخگو بود',
        'پاسخگو نبود',
        'عدم پاسخگویی',
        'عدم همکاری',
        'درخواست تماس در زمان دیگر',
    ];
    
    public $results = [
        'عدم همکاری',
        'درخواست تماس در ساعات دیگر',
        'اعلام تمایل به خرید در والگلد داشت',
        'خیلی تمایلی نشان ندادند',
    ];

    public $list = [];
    
    public function mount()
    {
        $this->updateList();
    }

    public function updatedOnlyMe()
    {
        $this->updateList();
    }

    public function updatedWithoutResult()
    {
        $this->updateList();
    }

    public function updatedSearch()
    {
        $this->updateList();
    }

    public function updatedCallAgain()
    {
        $this->updateList();
    }

    public function updateList()
    {
        $this->list = Campaign::
        when($this->only_me,function($q){
            $q->where('user_id',auth()->user()->id);
        })
        ->when($this->without_result,function($q){
            $q->where(function($query){
                $query->whereNull('call_status')
                ->orWhere('call_status','')
                ->orWhere('call_status','پاسخگو بود');
            })
            ->where(function($query){
                $query->whereNull('call_result')
                ->orWhere('call_result','');
            });

        })
        ->when($this->call_again,function($q){
            $q->where('call_result','درخواست تماس در ساعات دیگر')
            ->orWhere('call_status','درخواست تماس در زمان دیگر');
        })
        ->when($this->search,function($q){
            $q->where('phone_number','LIKE',"%{$this->search}%");
        })
        ->get();
    }

    public function assign($id)
    {
        $campaign = Campaign::find($id);
        $campaign->user_id = auth()->user()->id;
        $campaign->save();
        $this->dispatch('operation-successful');
        $this->updateList();
    }

    public function checkBeingAssigned($campaign)
    {
        if(is_null($campaign->user_id))
        {
            abort(403);
        }
    }

    public function changeStatus($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->call_status = $value;
        $campaign->save();
    }

    public function changeResult($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->call_result = $value;
        $campaign->called_at = now();
        $campaign->save();
    }

    public function changeFamiliar($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->familiar = $value;
        $campaign->save();
    }

    public function changeUsed($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->used = $value;
        $campaign->save();
    }

    public function changeExperience($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->experience = $value;
        $campaign->save();
    }

    public function changeDescription($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->description = $value;
        $campaign->save();
    }

    public function changeAdminId($id,$value)
    {
        $campaign = Campaign::find($id);
        $this->checkBeingAssigned($campaign);
        $campaign->admin_id = $value;
        $campaign->save();
    }

    public function render()
    {
        return view('livewire.campaigns.first.show-list');
    }
}
