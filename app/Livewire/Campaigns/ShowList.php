<?php

namespace App\Livewire\Campaigns;

use App\Models\CampaignList;
use Livewire\Component;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
use Livewire\WithPagination;

class ShowList extends Component
{
    use WithPagination;
    public string $name;
    public string $description;
    public $from;
    public $to;

    protected $rules = [
        'name'=>'required|string',
        'description'=>'required|string',
        'from'=>'required',
        'to'=>'required',
    ];

    public function add()
    {
        $this->validate();

        CampaignList::create([
            'name'=>$this->name,
            'description'=>$this->description,
            'from'=>Verta::parse($this->from)->toCarbon(),
            'to'=>Verta::parse($this->to)->toCarbon(),
        ]);

        $this->dispatch('operation-successful');
    }

    public function delete($id)
    {
        // CampaignList::find($id)->delete();
    }

    public function changeName($id,$value)
    {
        $campaign = CampaignList::find($id);
        $campaign->name = $value;
        $campaign->save();
        $this->dispatch('operation-successful');
    }

    public function changeDescription($id,$value)
    {
        $campaign = CampaignList::find($id);
        $campaign->description = $value;
        $campaign->save();
        $this->dispatch('operation-successful');
    }

    public function changeFrom($id,$value)
    {
        $campaign = CampaignList::find($id);
        $campaign->from = Verta::parse($value)->toCarbon();
        $campaign->save();
        $this->dispatch('operation-successful');
    }

    public function changeTo($id,$value)
    {
        $campaign = CampaignList::find($id);
        if($value=='' or is_null(($value))) {
            $campaign->to = null;
        } else {
            $campaign->to = Verta::parse($value)->toCarbon();
        }
        $campaign->save();
        $this->dispatch('operation-successful');
    }
    
    public function render()
    {
        $campaigns = CampaignList::orderByDesc('from')->paginate(10);
        return view('livewire.campaigns.show-list',compact('campaigns'));
    }
}
