<?php

namespace App\Livewire;

use App\Models\Parameter;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;

class ShowParameters extends Component
{
    public string $name;
    public string $incoming_type = '';
    public int $score;
    public $active = '';

    public function create()
    {

        $validated = $this->validate([
            'name' => 'required|string',
            'incoming_type' => 'required|in:supervisor,chat,call,kyc,outgoing,red_chat,red_call,red_outgoing,QC_head,supervisor_head,faq,email,ticket',
            'score' => 'required|integer|min:0|max:100',
        ]);
        Parameter::create($validated);
        $this->dispatch('operation-successful');
    }

    public function changeName($id, $name): void
    {
        if (! Gate::allows('update-parameter')) {
            abort(403);
        }

        $parameter = Parameter::find($id);
        $parameter->name = $name;
        $parameter->save();
        $this->dispatch('operation-successful');
    }

    public function changeScore($id, $score): void
    {
        if (! Gate::allows('update-parameter')) {
            abort(403);
        }

        $parameter = Parameter::find($id);
        $parameter->score = $score;
        $parameter->save();
        $this->dispatch('operation-successful');
    }

    public function changeIncomingType($id, $incoming_type): void
    {
        if (! Gate::allows('update-parameter')) {
            abort(403);
        }

        $parameter = Parameter::find($id);
        $parameter->incoming_type = $incoming_type;
        $parameter->save();
        $this->dispatch('operation-successful');
    }

    public function changeStatus($id, $status): void
    {
        if (! Gate::allows('update-parameter')) {
            abort(403);
        }

        $parameter = Parameter::find($id);
        $parameter->active = (bool) $status;
        $parameter->save();
        $this->dispatch('operation-successful');
    }

    public function delete($id): void
    {
        if (! Gate::allows('delete-parameter')) {
            abort(403);
        }

        $parameter = Parameter::find($id);
        $parameter->delete();
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        return view('livewire.show-parameters', [
            'parameters' => Parameter::
            // latest()->
            when($this->incoming_type, function ($q) {$q->where('incoming_type', $this->incoming_type);})
            ->when($this->active, function ($q) {$q->where('active', true);})
            ->get(),
        ]);
    }
}
