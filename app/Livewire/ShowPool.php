<?php

namespace App\Livewire;

use App\Models\Pool;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Gate;

class ShowPool extends Component
{
    use WithPagination;

    public $platform = 'wallex';

    public $qc_agents = [];
    public int $filter = 0;
    public int|string $incoming_type = 0;

    protected $queryString = ['filter','incoming_type'];

    public function mount()
    {
        $this->qc_agents = User::
        where('platform',$this->platform)
        ->where('role_id', 2)->get()->shuffle();
    }

    public function updatedPlatform()
    {
        $this->filter = 0;
        $this->qc_agents = User::
        where('platform',$this->platform)
        ->where('role_id', 2)->get()->shuffle();
    }

    public function truncate()
    {
        if (! Gate::allows('delete-pool')) {
            abort(403);
        }
        Pool::truncate();
        $this->dispatch('operation-successful');
    }

    public function deleteChats()
    {
        if (! Gate::allows('delete-pool')) {
            abort(403);
        }

        Pool::where('incoming_type', 'chat')
        ->where('platform',$this->platform)
        ->delete();
        $this->dispatch('operation-successful');
    }

    public function deleteCalls()
    {
        if (! Gate::allows('delete-pool')) {
            abort(403);
        }
        Pool::where('incoming_type', 'call')
        ->where('platform',$this->platform)
        ->delete();
        $this->dispatch('operation-successful');
    }

    public function deleteKyc()
    {
        if (! Gate::allows('delete-pool')) {
            abort(403);
        }
        Pool::where('incoming_type', 'kyc')
        ->where('platform',$this->platform)
        ->delete();
        $this->dispatch('operation-successful');
    }

    public function deleteOutgoing()
    {
        if (! Gate::allows('delete-pool')) {
            abort(403);
        }
        Pool::where('incoming_type', 'outgoing')
        ->where('platform',$this->platform)
        ->delete();
        $this->dispatch('operation-successful');
    }

    public function changeAgent($datum_id, $qc_agent_id)
    {
        if (! Gate::allows('assign-pool')) {
            abort(403);
        }

        $item = Pool::where('id', $datum_id)->first();
        if($qc_agent_id == 0) {
            $qc_agent_id = null;
        }
        $item->qc_agent_id = $qc_agent_id;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function assign()
    {
        $this->assignCalls();
        $this->assignChats();
        $this->assignKyc();
        $this->assignOutgoing();
        
        $this->dispatch('operation-successful');
    }

    public function assignChats()
    {
        if (! Gate::allows('assign-pool')) {
            abort(403);
        }

        $chats = Pool::
        where('platform',$this->platform)
        ->whereNull('qc_agent_id')->where('incoming_type', 'chat')->get()->shuffle();

        foreach ($this->qc_agents as $qc_agent) {
            $count = min([$qc_agent->trace_chat,$chats->count()]);
            if($count) {
                if($qc_agent->id == 8) {
                    // حسین عزیزی
                    // زینب عزیزی
                    //  $random_chats = $chats->where('support_agent_id','<>',12)->shift($count);
                    $random_chats = $chats->where('support_agent_id', '<>', 12)->take($count);
                    $chats = $chats->whereNotIn('id',$random_chats->pluck('id'));
                } else {
                    $random_chats = $chats->shift($count);
                }
                Pool::whereIn('id', $random_chats->pluck('id')->toArray())->update(['qc_agent_id' => $qc_agent->id]);
            }
        }

    }

    public function assignCalls()
    {
        if (! Gate::allows('assign-pool')) {
            abort(403);
        }

        $calls = Pool::
        where('platform',$this->platform)
        ->whereNull('qc_agent_id')->where('incoming_type', 'call')->get()->shuffle();

        foreach ($this->qc_agents as $qc_agent) {
            $count = min([$qc_agent->trace_call,$calls->count()]);
            if($count) {
                if($qc_agent->id == 8) {
                    // حسین عزیزی
                    // زینب عزیزی
                    $random_calls = $calls->where('support_agent_id','<>',12)->shift($count);
                } else {
                    $random_calls = $calls->shift($count);
                }
                
                Pool::whereIn('id', $random_calls->pluck('id')->toArray())->update(['qc_agent_id' => $qc_agent->id]);
            }
        }
    }

    public function assignKyc()
    {
        if (! Gate::allows('assign-pool')) {
            abort(403);
        }

        $kyc_records = Pool::
        where('platform',$this->platform)
        ->whereNull('qc_agent_id')->where('incoming_type', 'kyc')->get()->shuffle();
        foreach ($this->qc_agents as $qc_agent) {
            $count = min([$qc_agent->trace_kyc,$kyc_records->count()]);
            if($count) {

                if($qc_agent->id == 8) {
                    // حسین عزیزی
                    // زینب عزیزی
                    $random_kyc_records = $kyc_records->where('support_agent_id','<>',12)->shift($count);
                } else {
                    $random_kyc_records = $kyc_records->shift($count);
                }

                if($random_kyc_records)
                {
                    Pool::whereIn('id', $random_kyc_records->pluck('id')->toArray())->update(['qc_agent_id' => $qc_agent->id]);
                }
            }
        }
    }

    public function assignOutgoing()
    {
        if (! Gate::allows('assign-pool')) {
            abort(403);
        }

        $outgoing_records = Pool::
        where('platform',$this->platform)
        ->whereNull('qc_agent_id')->where('incoming_type', 'outgoing')->get()->shuffle();
        foreach ($this->qc_agents as $qc_agent) {
            $count = min([$qc_agent->trace_outgoing,$outgoing_records->count()]);
            if($count) {

                if($qc_agent->id == 8) {
                    // حسین عزیزی
                    // زینب عزیزی
                    $random_outgoing_records = $outgoing_records->where('support_agent_id','<>',12)->shift($count);
                } else {
                    $random_outgoing_records = $outgoing_records->shift($count);
                }

                Pool::whereIn('id', $random_outgoing_records->pluck('id')->toArray())->update(['qc_agent_id' => $qc_agent->id]);
            }
        }
    }

    public function me($id)
    {
        $pool = Pool::find($id);
        $pool->qc_agent_id = auth()->user()->id;
        $pool->save();
    }

    public function delete($id)
    {
        // if (! Gate::allows('delete-pool')) {
        //     abort(403);
        // }
        Pool::find($id)
        ->delete();
    }

    public function render()
    {

        $pool = Pool::
        where('platform',$this->platform)
        ->when($this->filter, function ($q) {
            $q->where('qc_agent_id', $this->filter);
        })
        ->when($this->incoming_type, function ($q) {
            $q->where('incoming_type', $this->incoming_type);
        })
        ->get();

        return view(
            'livewire.show-pool',
            [
                'data' => $pool,
            ]
        );
    }
}
