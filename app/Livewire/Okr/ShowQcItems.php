<?php

namespace App\Livewire\Okr;

use App\Models\Qc_item;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Gate;

use Livewire\Component;

class ShowQcItems extends Component
{
    use WithPagination;

    public $title;
    public $description;
    
    public function changeTitle($id,$title)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Qc_item::find($id);
        $item->title = $title;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function changeDescription($id,$description)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Qc_item::find($id);
        $item->description = $description;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function changeStatus($id,$status)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Qc_item::find($id);
        $item->status = $status;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function delete($id)
    {
        if (! Gate::allows('delete-item')) {
            abort(403);
        }
        Qc_item::find($id)->delete();

        $this->dispatch('operation-successful');
    }
    public function add()
    {
        if (! Gate::allows('add-item')) {
            abort(403);
        }

        Qc_item::create([
            'title' => $this->title,
            'description' => $this->description,
        ]);
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        $items = Qc_item::paginate(100);

        return view('livewire.okr.show-qc-items',compact('items'));
    }
}
