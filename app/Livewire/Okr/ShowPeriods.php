<?php

namespace App\Livewire\Okr;

use App\Models\Okr_period;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

use Livewire\Component;

class ShowPeriods extends Component
{
    public ?string $from;
    public ?string $to;

    protected $rules = 
    [
        'from' => 'required|string',
        'to' => 'required|string',
    ];

    public function insert()
    {
        $validated = $this->validate();

        $period = Okr_period::create([
            'from' => verta::parse($validated['from'])->toCarbon(),
            'to' => verta::parse($validated['to'])->toCarbon(),
        ]);

        $this->dispatch('operation-successful');
    }

    public function change_start($period_id,$value)
    {
        $period = Okr_period::find($period_id);
        $period->from = Verta::parse($value)->toCarbon();
        $period->save();
        $this->dispatch('operation-successful');
    }
    
    public function change_end($period_id,$value)
    {
        $period = Okr_period::find($period_id);
        $period->to = Verta::parse($value)->toCarbon();
        $period->save();
        $this->dispatch('operation-successful');
    }
    public function delete($period_id)
    {
        Okr_period::find($period_id)->delete();
        $this->dispatch('operation-successful');
    }

    public function render()
    {
         $periods = Okr_period::all();
        return view('livewire.okr.show-periods',compact('periods'));
    }
}
