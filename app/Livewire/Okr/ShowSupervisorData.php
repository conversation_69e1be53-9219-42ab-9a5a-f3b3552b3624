<?php

namespace App\Livewire\Okr;

use App\Models\Okr_period;
use App\Models\Setting;
use App\Models\Supervisor_item;
use App\Models\Supervisor_stat;
use App\Models\User;
use Illuminate\Support\Facades\Gate;

use Livewire\Component;

class ShowSupervisorData extends Component
{
    public int $period_id = 0;
    public $periods;
    public $users;
    public $items;
    public $stats;

    public function mount()
    {
        $this->periods = Okr_period::all();

        $this->users = User::where('role_id', 3)
        ->where('status', true)
        ->get();
    }

    public function updatedPeriodId()
    {
        $this->items = Supervisor_item::where('status',true)->get();
        $this->stats = Supervisor_stat::where('period_id', $this->period_id)->get();
    }

    public function change($user_id, $item_id, $value)
    {
        if (!Gate::allows('change-qc-data')) {
            abort(403);
        }

        // $lock_period = Setting::where('name', 'lock_period')->first()->value;

        // if($this->period_id <= $lock_period) {
        //     $this->dispatch('operation-failed');
        //     $this->addError('custom', 'Editing of this period has been locked.');
        //     return;
        // }

        if($value === '') {
            $value = null;
        }

        if(is_null($value)) {
            Supervisor_stat::where('user_id', $user_id)->where('period_id', $this->period_id)->where('supervisor_item_id', $item_id)->delete();
        } else {
            Supervisor_stat::updateOrCreate([
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'supervisor_item_id' => $item_id,
            ], [
                'value' => $value,
            ]);
        }

    }

    public function render()
    {
        return view('livewire.okr.show-supervisor-data');
    }
}
