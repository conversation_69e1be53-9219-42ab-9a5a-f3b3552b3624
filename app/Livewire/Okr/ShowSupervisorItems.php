<?php

namespace App\Livewire\Okr;

use App\Models\Supervisor_item;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Gate;

class ShowSupervisorItems extends Component
{
    use WithPagination;

    public $title;
    public $description;

    public function changeTitle($id,$title)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Supervisor_item::find($id);
        $item->title = $title;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function changeDescription($id,$description)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Supervisor_item::find($id);
        $item->description = $description;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function changeStatus($id,$status)
    {
        if (! Gate::allows('change-item')) {
            abort(403);
        }

        $item = Supervisor_item::find($id);
        $item->status = $status;
        $item->save();
        $this->dispatch('operation-successful');
    }

    public function delete($id)
    {
        if (! Gate::allows('delete-item')) {
            abort(403);
        }
        Supervisor_item::find($id)->delete();

        $this->dispatch('operation-successful');
    }
    public function add()
    {
        if (! Gate::allows('add-item')) {
            abort(403);
        }

        Supervisor_item::create([
            'title' => $this->title,
            'description' => $this->description,
        ]);
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        $items = Supervisor_item::paginate(100);

        return view('livewire.okr.show-supervisor-items',compact('items'));
    }
}
