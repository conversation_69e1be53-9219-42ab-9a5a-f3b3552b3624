<?php

namespace App\Livewire\Okr;

use App\Models\Okr_period;
use App\Models\Qc_stat;
use App\Models\Supervisor_stat;
use App\Models\User;
use Livewire\Component;

class ShowOkrResult extends Component
{
    public $periods;
    public int $period_id = 0;

    public $qc_team = [];
    public $qc_team_percent;

    public $supervisor_team = [];
    public $supervisor_team_percent;

    protected $rules = [
        'period_id'=>'required|exists:okr_periods,id',
    ];

    public function mount()
    {
        $this->periods = Okr_period::all();
    }

    public function updated()
    {
        $this->validate();

        $this->qc_calc();
        $this->supervisor_calc();
    }

    public function qc_calc()
    {
        $this->qc_team = User::where('role_id',2)->where('status',true)->get();
        $qc_stats = Qc_stat::where('period_id',$this->period_id)->get();

        foreach($this->qc_team as $qc_member)
        {
            $qc_data = $qc_stats->where('user_id',$qc_member->id);
            if(!!!$qc_data->count()){continue;}

            $working_days = $qc_data->where('qc_item_id',24)->first()?->value;
            $working_hours = $qc_data->where('qc_item_id',25)->first()?->value;

            $availability = $working_days * $working_hours;

            $leaves = (float) ($qc_data->where('qc_item_id',26)->first()?->value * 60);

            $overtime = (float) ($qc_data->where('qc_item_id',27)->first()?->value * 60);

            $rests = (float) ($qc_data->where('qc_item_id',28)->first()?->value * 60);
            
            $availability -= ($leaves + $overtime + $rests);

            // جلسه آموزش و کوچ برای تیم qc	
            $availability -= (float) ($qc_data->where('qc_item_id',29)->first()?->value * 60);

            // مشکلات متفرقه
            $availability -= (float) ($qc_data->where('qc_item_id',30)->first()?->value * 60);

            $total = 0;

            // مدت کل تماس های ورودی
            $total += (float) ($qc_data->where('qc_item_id',1)->first()?->value * $qc_data->where('qc_item_id',2)->first()?->value);

            // مدت کل چت ها
            $total += (float) ($qc_data->where('qc_item_id',3)->first()?->value * $qc_data->where('qc_item_id',4)->first()?->value);

            // مدت کل احراز ها
            $total += (float) ($qc_data->where('qc_item_id',5)->first()?->value * $qc_data->where('item_id',6)->first()?->value);

            // مدت کل خروجی ها
            $total += (float) ($qc_data->where('qc_item_id',31)->first()?->value * $qc_data->where('item_id',32)->first()?->value);
            
            // مدت کل جلسات کوچ
            $total += (float) ($qc_data->where('qc_item_id',7)->first()?->value * $qc_data->where('qc_item_id',8)->first()?->value);

            // مدت کل بررسی اعتراضات
            $total += (float) ($qc_data->where('qc_item_id',9)->first()?->value * $qc_data->where('qc_item_id',10)->first()?->value);

            // آپدیت درخت دانش و اطلاع رسانی ها
            $total += (float) ($qc_data->where('qc_item_id',11)->first()?->value);

            // مدت کل آموزش توسط تیم qc
            $total += (float) ($qc_data->where('qc_item_id',12)->first()?->value * $qc_data->where('qc_item_id',13)->first()?->value);

            // آموزش محصولات و امکانات جدید
            $total += (float) ($qc_data->where('qc_item_id',14)->first()?->value);

            // مدت کل qc QC
            $total += (float) ($qc_data->where('qc_item_id',15)->first()?->value * $qc_data->where('qc_item_id',16)->first()?->value);

            // سمپلینگ و آپلود موارد در سایت qc
            $total += (float) ($qc_data->where('qc_item_id',17)->first()?->value);

            // کارشناس پشتیبانی (agent)
            $total += (float) ($qc_data->where('qc_item_id',18)->first()?->value * 60);

            // آپدیت موارد crm
            $total += (float) ($qc_data->where('qc_item_id',19)->first()?->value * 60);

            // تکمیل مستندات آموزشی
            $total += (float) ($qc_data->where('qc_item_id',20)->first()?->value * 60);

            // آموزش به تیم qc
            $total += (float) ($qc_data->where('qc_item_id',21)->first()?->value);

            // پاسخگویی به تیم
            $total += (float) ($qc_data->where('qc_item_id',22)->first()?->value * 60);

            // تحویل تسک و رویه های داخلی تیم
            $total += (float) ($qc_data->where('qc_item_id',23)->first()?->value * 60);

            $total = $total / 60;
            $availability = $availability / 60;

            if($availability)
            {
                $this->qc_team_percent[$qc_member->id] = $availability ? round($total / $availability * 100,2) : '';
            }
        }
    }

    public function supervisor_calc()
    {
        $this->supervisor_team = User::where('role_id',3)->get();
        $supervisor_stats = Supervisor_stat::where('period_id',$this->period_id)->get();
        foreach ($this->supervisor_team as $supervisor_member)
        {
            $supervisor_data = $supervisor_stats->where('user_id',$supervisor_member->id);
            if(!!!$supervisor_data->count()){continue;}

            $supervisor_days = $supervisor_data->where('supervisor_item_id',16)->first()?->value;
            $SLS_days = $supervisor_data->where('supervisor_item_id',17)->first()?->value;

            $total = 0;

            $total += (float) ($supervisor_data->where('supervisor_item_id',1)->first()?->value);
            $total += (float) ($supervisor_data->where('supervisor_item_id',2)->first()?->value * $supervisor_days);
            $total += (float) ($supervisor_data->where('supervisor_item_id',3)->first()?->value * $supervisor_days);
            $total += (float) ($supervisor_data->where('supervisor_item_id',4)->first()?->value);
            $total += (float) ($supervisor_data->where('supervisor_item_id',5)->first()?->value * $supervisor_days);

            $total += (float) ($supervisor_data->where('supervisor_item_id',6)->first()?->value * $SLS_days);
            $total += (float) ($supervisor_data->where('supervisor_item_id',7)->first()?->value * $SLS_days);
            $total += (float) ($supervisor_data->where('supervisor_item_id',8)->first()?->value * $SLS_days);
            $total += (float) ($supervisor_data->where('supervisor_item_id',9)->first()?->value * $SLS_days);

            $total += (float) ($supervisor_data->where('supervisor_item_id',10)->first()?->value);
            $total += (float) ($supervisor_data->where('supervisor_item_id',11)->first()?->value);
            $total += (float) ($supervisor_data->where('supervisor_item_id',12)->first()?->value);
            $total += (float) ($supervisor_data->where('supervisor_item_id',13)->first()?->value);
            $total += (float) ($supervisor_data->where('supervisor_item_id',14)->first()?->value);
            $total += (float) ($supervisor_data->where('supervisor_item_id',15)->first()?->value * $supervisor_days);

            $working_days = $supervisor_data->where('supervisor_item_id',18)->first()?->value;
            $working_hours = $supervisor_data->where('supervisor_item_id',19)->first()?->value;
            $leaves = $supervisor_data->where('supervisor_item_id',20)->first()?->value;
            $rests = $supervisor_data->where('supervisor_item_id',21)->first()?->value;
            $meetings = $supervisor_data->where('supervisor_item_id',22)->first()?->value;
            $exhibition = $supervisor_data->where('supervisor_item_id',23)->first()?->value;
            $tutorial = $supervisor_data->where('supervisor_item_id',24)->first()?->value;
            $overtime = $supervisor_data->where('supervisor_item_id',25)->first()?->value;
            
            $availability = $working_days*$working_hours + ($leaves+$rests+$meetings+$exhibition+$tutorial+$overtime);

            
            $this->supervisor_team_percent[$supervisor_member->id] = $availability ? round($total / $availability * 100,2) : '-';
        }
        
    }

    public function render()
    {
        return view('livewire.okr.show-okr-result');
    }
}
