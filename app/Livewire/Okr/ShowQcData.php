<?php

namespace App\Livewire\Okr;

use App\Models\Okr_period;
use App\Models\Qc_item;
use App\Models\Qc_stat;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\Gate;

use Livewire\Component;
class ShowQcData extends Component
{
    public int $period_id = 0;
    public $periods;
    public $users;
    public $items;
    public $stats;

    public function mount()
    {
        $this->periods = Okr_period::all();

        $this->users = User::
        where(function($q){
            $q->where('role_id', 2)
            ->orWhere('id',2);
        })
        ->where('status', true)
        ->get();
    }

    public function updatedPeriodId()
    {
        $this->items = Qc_item::where('status',true)->get();
        $this->stats = Qc_stat::where('period_id', $this->period_id)->get();
    }

    public function change($user_id, $item_id, $value)
    {
        if (!Gate::allows('change-qc-data')) {
            abort(403);
        }

        // $lock_period = Setting::where('name', 'lock_period')->first()->value;

        // if($this->period_id <= $lock_period) {
        //     $this->dispatch('operation-failed');
        //     $this->addError('custom', 'Editing of this period has been locked.');
        //     return;
        // }

        if($value === '') {
            $value = null;
        }

        if(is_null($value)) {
            Qc_stat::where('user_id', $user_id)->where('period_id', $this->period_id)->where('qc_item_id', $item_id)->delete();
        } else {
            Qc_stat::updateOrCreate([
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'qc_item_id' => $item_id,
            ], [
                'value' => $value,
            ]);
        }

    }

    public function render()
    {
        return view('livewire.okr.show-qc-data');
    }
}
