<?php

namespace App\Livewire\Upload;

use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ParslogicImport;
use App\Models\Period;
use App\Models\Setting;
use App\Models\Stat;
use App\Models\User;

class IncomingCall extends Component
{
    use WithFileUploads;
    public $excel;
    public $support_agents;
    public $periods;
    public $period_id = '';

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
        $this->periods = Period::where('id','>=',7)->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'period_id' => 'required|exists:periods,id',
            'excel' => 'required|max:5120', // 5MB Max
        ]);

        $lock_period = Setting::where('name', 'lock_period')->first()->value;
        if($this->period_id <= $lock_period)
        {
            $this->addError('custom', 'Editing of this period has been locked.');
            return;
        }

        $records = $this->process($validated['excel']);

        
        $data = [];
        foreach($records as $user_id => $array)
        {
            $data [] = [
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => 2,     // تعداد تماس
                'value' => $array['count'],
            ];

            $data [] = [
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => 6,     // میانگین زمان پاسخ گویی تماس ها (بر حسب ثانیه)	
                'value' => round($array['duration']/$array['count'],2),
            ];
        }

        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
            $this->dispatch('operation-successful');
        }
        else
        {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new ParslogicImport(), $file);
        $array = $array[0];
        array_shift($array);

        $records = [];
        foreach ($array as $record)
        {
            $name = $record[1];
            $duration = $record[10];

            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })->first();

            if($support_agent) 
            {
                $records[$support_agent->id]['count'] = isset($records[$support_agent->id]['count']) ? ($records[$support_agent->id]['count'] + 1) : 1;
                $records[$support_agent->id]['duration'] = isset($records[$support_agent->id]['duration']) ? ($records[$support_agent->id]['duration'] + $duration) : $duration;
            }
        }

        return $records;
    }

    public function render()
    {
        return view('livewire.upload.incoming-call');
    }
}
