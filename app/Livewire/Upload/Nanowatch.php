<?php

namespace App\Livewire\Upload;

use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\NanowatchImport;
use App\Models\Period;
use App\Models\Setting;
use App\Models\Stat;
use App\Models\User;

class Nanowatch extends Component
{
    use WithFileUploads;
    public $excel;
    public $support_agents;
    public $periods;
    public $period_id = '';

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
        $this->periods = Period::where('id','>=',7)->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'period_id' => 'required|exists:periods,id',
            'excel' => 'required|max:5120', // 5MB Max
        ]);

        $lock_period = Setting::where('name', 'lock_period')->first()->value;
        if($this->period_id <= $lock_period)
        {
            $this->addError('custom', 'Editing of this period has been locked.');
            return;
        }

        $records = $this->process($validated['excel']);
        $data = [];
        foreach($records as $user_id => $value)
        {
            $data [] = [
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => 18,     // ثبت فراموشی
                'value' => $value,
            ];
        }

        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
            $this->dispatch('operation-successful');
        }
        else
        {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new NanowatchImport(), $file);
        $array = $array[0];
        
        $records = [];

        $support_agent_id = null;
        foreach ($array as $record)
        {
            $agent_name = $record[0];
            // $personnel_code = $record[1];
            $manual = $record[2];
            $confirmed = $record[5];
            
            $support_agent = $this->support_agents
            ->filter(function($user) use ($agent_name) {
                return str_contains($user->name,$agent_name) or str_contains($user->nickname,$agent_name);
            })->first();

            if($support_agent)
            {
                $support_agent_id = $support_agent->id;
            }

            if($manual != 'فراموشی ثبت تردد' or $confirmed != 'تایید'){continue;}

            $records[$support_agent_id] = isset($records[$support_agent_id]) ? ($records[$support_agent_id] + 1) : 1;
        }

        return $records;

    }

    public function render()
    {
        return view('livewire.upload.nanowatch');
    }
}
