<?php

namespace App\Livewire\Upload;

use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ParslogicImport;
use App\Models\Period;
use App\Models\Setting;
use App\Models\User;
use <PERSON>k<PERSON><PERSON>ser\Verta\Verta;
use App\Models\Stat;

class Comprehensive extends Component
{
    // analyzing open tickets

    use WithFileUploads;
    public $excel;
    public $support_agents;
    public $periods;
    public $period_id;
    public $sls = [];
    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
        $this->periods = Period::where('id',">=",7)->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'period_id' => 'required|exists:periods,id',
            'excel' => 'required|max:10240', // 10MB Max
        ]);

        $lock_period = Setting::where('name', 'lock_period')->first()->value;
        if($this->period_id <= $lock_period)
        {
            $this->addError('custom', 'Editing of this period has been locked.');
            return;
        }
        
        $users = $this->process($validated['excel']);

        $open_tickets = [];
        $delayed_SL_tickets = [];
        $delayed_FL_tickets = [];
        
        foreach($users as $user_id => $user_records)
        {
            foreach($user_records as $record)
            {
                if($record['diff'] > 24*60)
                {
                    $open_tickets[$user_id] = isset($open_tickets[$user_id]) ? ($open_tickets[$user_id] + 1) : 1;
                }
                elseif(in_array($record['id'],$this->sls) and $record['diff']>30)
                {
                    $delayed_SL_tickets[$user_id] = isset($delayed_SL_tickets[$user_id]) ? ($delayed_SL_tickets[$user_id] + 1) : 1;
                }
                elseif($record['diff']>60)
                {
                    $delayed_FL_tickets[$user_id] = isset($delayed_FL_tickets[$user_id]) ? ($delayed_FL_tickets[$user_id] + 1) : 1;
                }
            }
        }

        $data = [];

        foreach($open_tickets as $user_id => $count)
        {
            $data[]=[
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => 7,     // تیکت باز کارشناس
                'value' => $count,
            ];
        }

        foreach($delayed_SL_tickets as $user_id => $count)
        {
            $data[]=[
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => 9,     // زمان ایجاد پاپ آپ چت و تماس تا زمان ثبت آن (SL)
                'value' => $count,
            ];
        }

        foreach($delayed_FL_tickets as $user_id => $count)
        {
            $data[]=[
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => 8,     // زمان ایجاد پاپ آپ چت و تماس تا زمان ثبت آن (FL)
                'value' => $count,
            ];
        }

        // dd($data);
        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
            $this->dispatch('operation-successful');
        }
        else
        {
            $this->dispatch('operation-failed');
        }

    }
    
    public function process($file)
    {
        $array = Excel::toArray(new ParslogicImport(), $file);
        $array = $array[0]; // عملیات
        array_shift($array);
        
        $users = [];

        foreach ($array as $record)
        {
            $operation = $record[0];   
            $id = $record[4];           // ID
            if($operation=='ارجاع به تیم SLS')
            {
                $this->sls[]=$id;
            }
            elseif($operation='تماس ورودی' or $operation=='liveChat')
            {
                $name = str()->replace(' - ', ' ', $record[1]); // کاربر اقدام کننده
                $support_agent = $this->support_agents
                ->filter(function($user) use ($name) {
                    return ($user->name == $name or $user->nickname == $name);
                })
                ->first();
                
                $created_at = trim($record[2]);   // تاریخ ایجاد
                $done_at = trim($record[3]);      // تاریخ انجام
                if($support_agent and $created_at and $done_at)
                {
                    $created_at = Verta::parse($created_at)->toCarbon();
                    $done_at = Verta::parse($done_at)->toCarbon();
                    $diff = $done_at->diffInMinutes($created_at);
                    $users[$support_agent->id][]=[
                        'operation'=>$operation,
                        'id'=>$id,
                        'diff'=>$diff,
                    ];
                }
            }

        }

        return $users;
    }

    public function render()
    {
        return view('livewire.upload.comprehensive');
    }
}
