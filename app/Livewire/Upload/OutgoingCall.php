<?php

namespace App\Livewire\Upload;

use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ParslogicImport;
use App\Models\Pool;
use App\Models\User;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class OutgoingCall extends Component
{
    use WithFileUploads;

    public $excel;
    public $support_agents;
    public $platform = 'wallex';

    public function mount()
    {
        $this->support_agents = User::whereIn('role_id',[1,2,3])->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'excel' => 'required|max:5120', // 5MB Max
        ]);

        $records = $this->process($validated['excel']);
        $data = [];

        if($records)
        {
            foreach ($records as &$record) {
                $record['created_at'] = now();
                $record['updated_at'] = now();
            }
            Pool::insertOrIgnore($records);
            $this->dispatch('operation-successful');
            return redirect()->route('pool');
        }
        else
        {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new ParslogicImport(), $file);
        $array = $array[0];
        array_shift($array);

        $records = [];
        $target = User::where('role_id', 2)
        ->where('platform',$this->platform)
        ->where('trace_outgoing', '<>', 0)->sum('trace_outgoing');

        foreach($array as $record)
        {
            $name = $record[1];

            $support_agent = $this->support_agents
            ->where('platform',$this->platform)
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })->first();

            if($support_agent)
            {
                $date=$record[3];
                $time = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($record[4])->format("h:i:s A");
                $datetime = Verta::parse($date . " " . $time);

                $item = [
                    'identity' => $record[2],
                    'incoming_date' => $datetime->toCarbon(),
                    'support_agent_id' => $support_agent->id,
                    'incoming_type' => 'outgoing',
                    'note'=>$record[5],
                    'platform'=>$this->platform,
                ];
    
                array_push($records, $item);
            }

        }

        $min = min($target, count($records));
        shuffle($records);
        return array_slice($records, 0,$min);
    }

    public function render()
    {
        return view('livewire.upload.outgoing-call');
    }
}
