<?php

namespace App\Livewire\Upload;

use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ParslogicImport;
use App\Models\User;
use App\Models\Pool;

class HardCases extends Component
{
    use WithFileUploads;
    public $excel;
    public $support_agents;

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'excel' => 'required|max:5120', // 5MB Max
        ]);
        
        $records = $this->process($validated['excel']);

        if($records)
        {
            foreach ($records as &$record) {
                $record['created_at'] = now();
                $record['updated_at'] = now();
            }
            Pool::insertOrIgnore($records);
            $this->dispatch('operation-successful');
            return redirect()->route('pool');
        }else
        {
            $this->dispatch('operation-failed');
        }

    }

    public function process($file)
    {
        $array = Excel::toArray(new ParslogicImport(), $file);
        $array = $array[0];
        array_shift($array);

        $records = [];

        foreach ($array as $record)
        {
            $name = $record[3];

            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })->first();
            
            if($support_agent)
            {
                $CaseId = $record[0];
                // $Subject = $record[1];
                $item = [
                    'identity' => null,
                    'incoming_date' => \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($record[2]),
                    'support_agent_id' => $support_agent->id,
                    'incoming_type' => null,
                    'note' => "CaseId : {$CaseId}", 
                ];
    
                array_push($records, $item);
            }
        }

        return $records;
    }
    public function render()
    {

        
        return view('livewire.upload.hard-cases');
    }
}
