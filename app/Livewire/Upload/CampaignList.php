<?php

namespace App\Livewire\Upload;

use App\Imports\CampaignImport;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Campaign;
use App\Models\User;

class CampaignList extends Component
{
    use WithFileUploads;
    public $excel;
    public $users = null;
    public function mount()
    {
        $this->users = User::where('role_id',1)->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'excel' => 'required|max:5120', // 5MB Max
        ]);

        $list = $this->process($validated['excel']);

        foreach ($list as &$item)
        {
            $item['created_at'] = now();
            $item['updated_at'] = now();
        }

        if($list) {
            Campaign::insertOrIgnore($list);
            $this->dispatch('operation-successful');
        } else {
            $this->dispatch('operation-failed');
        }
        
    }

    public function process($file)
    {
        $array = Excel::toArray(new CampaignImport(), $file);
        $array = $array[0];
        array_shift($array);
        $list = [];

        foreach ($array as $record)
        {
            
            $list[]=[
                'admin_id'=>$record[0],
            ];
        }
        return $list;
    }

    public function render()
    {
        return view('livewire.upload.campaign-list');
    }
}
