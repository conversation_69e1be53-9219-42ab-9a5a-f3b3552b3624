<?php

namespace App\Livewire\Upload;

use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\NanowatchImport;
use App\Models\Period;
use App\Models\Setting;
use App\Models\Stat;
use App\Models\User;

class SecondNanowatch extends Component
{
    use WithFileUploads;
    public $excel;
    public $support_agents;
    public $periods;
    public $period_id = '';

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->get();
        $this->periods = Period::where('id','>=',7)->get();
    }

    public function save()
    {
        $validated = $this->validate([
            'period_id' => 'required|exists:periods,id',
            'excel' => 'required|max:5120', // 5MB Max
        ]);

        $lock_period = Setting::where('name', 'lock_period')->first()->value;
        if($this->period_id <= $lock_period)
        {
            $this->addError('custom', 'Editing of this period has been locked.');
            return;
        }

        $records = $this->process($validated['excel']);
        $data = [];

        foreach($records as $user_id => $value)
        {
            $data [] = [
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => 18,
                'value' => $value,
            ];
        }

        if($data)
        {
            Stat::upsert($data,uniqueBy:['user_id','period_id','item_id'],update:['value']);
            $this->dispatch('operation-successful');
        }
        else
        {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new NanowatchImport(), $file);
        $array = $array[0];
        $records = [];

        foreach ($array as $record)
        {
            $personal_code = $record[1];

            $support_agent = $this->support_agents
            ->where('personal_code',$personal_code)->first();

            $type = $record[2] == 'فراموشی ثبت تردد';
            $confirmed = $record[5] == 'تایید';
            
            if($confirmed and $type and $support_agent)
            {
                $records[$support_agent->id] = isset($records[$support_agent->id]) ? ($records[$support_agent->id] + 1) : 1;
            }

        }
        return $records;
    }

    public function render()
    {
        return view('livewire.upload.second-nanowatch');
    }
}
