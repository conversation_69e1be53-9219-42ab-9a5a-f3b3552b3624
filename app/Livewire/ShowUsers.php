<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Gate;

class ShowUsers extends Component
{
    public string $name;
    public string $password;
    public string $email;

    public array $roles = [
        1 => 'کارشناس پشتیبانی',
        2 => 'کارشناس کنترل کیفیت',
        3 => 'سوپروایزر',
        4 => 'سرپرست',
        5 => 'مدیر',
        6 => 'ادمین',
    ];

    public function create()
    {

        $validated = $this->validate([
            'name' => 'required|string',
            'password' => 'required|string',
            'email' => 'required|email',
        ]);
        $validated['password'] = Hash::make($validated['password']);
        User::create($validated);
        $this->dispatch('operation-successful');
    }

    public function changeName($id, $name): void
    {
        $user = User::find($id);
        $user->name = $name;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeNickName($id, $nickname): void
    {
        $user = User::find($id);
        $user->nickname = $nickname;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeWeight($id, $weight): void
    {
        $user = User::find($id);
        $user->weight = $weight;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeTraceChat($id, $trace_chat): void
    {
        $user = User::find($id);
        $user->trace_chat = $trace_chat;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeTraceCall($id, $trace_call): void
    {
        $user = User::find($id);
        $user->trace_call = $trace_call;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeTraceKyc($id, $trace_kyc): void
    {
        $user = User::find($id);
        $user->trace_kyc = $trace_kyc;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeTraceOutgoing($id, $trace_outgoing): void
    {
        $user = User::find($id);
        $user->trace_outgoing = $trace_outgoing;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeRole($id, $role_id): void
    {
        if (! Gate::allows('change-role')) {
            abort(403);
        }

        $user = User::find($id);
        $user->role_id = $role_id;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeStatus($id, $status): void
    {
        $user = User::find($id);
        $user->status = $status;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeEmail($id, $email): void
    {
        $user = User::find($id);
        $user->email = $email;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeEyebeamId($user_id, $eyebeam_id): void
    {
        $user = User::find($user_id);
        $user->eyebeam_id = $eyebeam_id;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeShift($user_id, $shift): void
    {
        $user = User::find($user_id);
        $user->shift = $shift;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changePlatform($user_id, $platform): void
    {
        $user = User::find($user_id);
        $user->platform = $platform;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeWorkingDays($user_id, $working_days): void
    {
        $user = User::find($user_id);
        $user->working_days = $working_days;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeSupervisorId($user_id, $supervisor_id)
    {
        $user = User::find($user_id);
        if($supervisor_id == 0) {
            $supervisor_id = null;
        }
        $user->supervisor_id = $supervisor_id;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changePersonalCode($user_id,$personal_code)
    {
        $user = User::find($user_id);
        $user->personal_code = $personal_code;
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function changeSlackId($user_id,$slack_id)
    {
        $user = User::find($user_id);
        $user->slack_id = $slack_id;
        $user->save();
        $this->dispatch('operation-successful');
    }


    public function reset_password($user_id)
    {
        $user = User::find($user_id);
        $user->password = Hash::make('wallex');
        $user->save();
        $this->dispatch('operation-successful');
    }

    public function delete($id)
    {
        $user = User::find($id);
        $user->delete();
    }

    public function render()
    {
        return view(
            'livewire.show-users',
            [
            'users' => User::all(),
        ]
        );
    }
}
