<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\Setting;
// use App\Models\Parameter;
// use App\Models\Record;
// use App\Models\Score;
// use App\Models\Stat;
// use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;

use App\AI\Calculator\AHT_chat;
use App\AI\Calculator\AHT_call;
use App\AI\Calculator\Miscall;
use App\AI\Calculator\Open_ticket;
use App\AI\Calculator\Ticketing_delay;
use App\AI\Calculator\Choose_wrong_subject;
use App\AI\Calculator\Repetitive;
use App\AI\Calculator\Choose_wrong_user;
use App\AI\Calculator\Choose_wrong_SL;
use App\AI\Calculator\Back_to_call;
use App\AI\Calculator\Position_logs;
use App\AI\Calculator\Waiting_queue;
use App\AI\Calculator\Leave_announcement;
use App\AI\Calculator\Lost_connections;
use App\AI\Calculator\Manual_nanowatch;
use App\AI\Calculator\Unassign_chat;

class AutoCalc extends Component
{
    // process data
    public $periods;
    public int $period_id = 0;
    public $period;

    public $users;
    public $parameters;

    protected $queryString = ['period_id'];

    public function mount()
    {
        $this->periods = Period::where('id',">=",6)->get();

        // $this->parameters = Parameter::all();
        // $this->users = User::where('role_id', 1)
        // ->whereNotNull('supervisor_id')
        // ->where('status', true)
        // ->get();
    }

    // public function updatedPeriodId()
    // {
        
    // }

    public function check_lock()
    {
        $this->period = $this->periods->where('id', $this->period_id)->first();

        if (! Gate::allows('auto-calc')) {
            abort(403);
        }
        $lock_period = Setting::where('name', 'lock_period')->first()->value;

        if($this->period_id <= $lock_period) {
            $this->dispatch('operation-failed');
            $this->addError('custom', 'Editing of this period has been locked.');
            return true;
        } else {
            return false;
        }
    }

    public function process_everything()
    {
        $this->process_chat_aht();
        $this->process_call_aht();
        // $this->process_lost_connection();

        $this->process_miscall();
        
        $this->process_open_tickets_24();
        $this->process_ticketing_delay();

        $this->process_choose_wrong_subject();
        $this->process_repetitive();
        $this->process_wrong_user();
        $this->process_wrong_sl();
        $this->process_back_to_call();

        $this->process_position_logs();
        $this->process_waiting_queue();

        $this->process_leave_announcement();
        $this->process_manual_nanowatch();
    }

    public function process_chat_aht()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new AHT_chat;
        $calculator->index($this->period);


        $this->dispatch('operation-successful');
    }

    public function process_unassigned_chats()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Unassign_chat;
        $calculator->index($this->period);


        $this->dispatch('operation-successful');
    }

    public function process_lost_connection()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Lost_connections;
        $calculator->index($this->period);


        $this->dispatch('operation-successful');
    }

    public function process_call_aht()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new AHT_call;
        $calculator->index($this->period);

        $this->dispatch('operation-successful');
    }

    public function process_miscall()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Miscall;
        $calculator->index($this->period);

        
        $this->dispatch('operation-successful');
    }

    public function process_open_tickets_24()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Open_ticket;
        $calculator->index($this->period);
        
        $this->dispatch('operation-successful');
    }

    public function process_ticketing_delay()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Ticketing_delay;
        $calculator->index($this->period);

        
        $this->dispatch('operation-successful');
    }

    public function process_choose_wrong_subject()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Choose_wrong_subject;
        $calculator->index($this->period);
        
        $this->dispatch('operation-successful');
    }

    public function process_repetitive()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Repetitive;
        $calculator->index($this->period);

        
        $this->dispatch('operation-successful');
    }

    public function process_wrong_user()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Choose_wrong_user;
        $calculator->index($this->period);
        
        
        $this->dispatch('operation-successful');
    }

    public function process_wrong_sl()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Choose_wrong_SL;
        $calculator->index($this->period);

        $this->dispatch('operation-successful');
    }

    public function process_back_to_call()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Back_to_call;
        $calculator->index($this->period);
        
        $this->dispatch('operation-successful');
    }

    public function process_position_logs()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Position_logs;
        $calculator->index($this->period);
        
        $this->dispatch('operation-successful');
    }

    public function process_waiting_queue()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Waiting_queue;
        $calculator->index($this->period);
        
        $this->dispatch('operation-successful');
    }

    public function process_leave_announcement()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Leave_announcement;
        $calculator->index($this->period);

        
        $this->dispatch('operation-successful');
    }

    public function process_manual_nanowatch()
    {
        if($this->check_lock()) {
            return;
        }

        $calculator = new Manual_nanowatch;
        $calculator->index($this->period);

        
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        return view('livewire.auto-calc');
    }
}
