<?php

namespace App\Livewire;

use App\Exports\OKR_Export;
use App\Imports\OKR_Import;
use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

class OKR extends Component
{
    use WithFileUploads;

    public $support_agents;

    // excel files
    public $kyc;
    public $incoming_call;
    public $chat;
    public $account_manager;
    public $fata;
    public $outgoing_call;
    public $position_log;

    public function mount()
    {
        $this->support_agents = User::where('role_id', 1)->get();
    }

    public function save()
    {
        // required xlsx
        $validated = $this->validate([
            'kyc' => 'required|max:2048',
            'incoming_call' => 'required|max:2048',
            'chat' => 'required|max:2048',
            'account_manager' => 'required|max:2048',
            'fata' => 'required|max:2048',
            'outgoing_call' => 'required|max:2048',
            'position_log' => 'required|max:2048',
        ]);

        // convert file to array
        $kyc = $this->process($validated['kyc']);
        $incoming_call = $this->process($validated['incoming_call']);
        $chat = $this->process($validated['chat']);
        $account_manager = $this->process($validated['account_manager']);
        $fata = $this->process($validated['fata']);
        $outgoing_call = $this->process($validated['outgoing_call']);
        $position_log = $this->process($validated['position_log']);

        // extract data from array
        $data = [];
        $data['kyc'] = $this->extract_kyc($kyc);
        $data['incoming_call'] = $this->extract_incoming_call($incoming_call);
        $data['chat'] = $this->extract_chat($chat);
        $data['account_manager'] = $this->extract_account_manager($account_manager);
        $data['fata'] = $this->extract_fata($fata);
        $data['outgoing_call'] = $this->extract_outgoing_call($outgoing_call);
        $data['position_log'] = $this->extract_position_log($position_log);
        // $this->dispatch('operation-successful');

        return Excel::download(new OKR_Export($data), 'okr.xlsx');
    }



    public function process($file)
    {
        $array = Excel::toArray(new OKR_Import(), $file);
        $array = $array[0];
        array_shift($array);
        return $array;
    }

    public function extract_kyc($array)
    {
        $data = [];
        foreach($array as $row) {
            $name = $row[0];
            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })
            ->first();
            if($support_agent) {
                $item = [
                    'support_agent_name' => $support_agent->name,
                    'DoneByAdminId' => $row[1],
                    'KYCcount' => $row[2],
                    'Total_AgentResponsetimebysecond' => $row[3],
                    'Average_AgentResponsetimebysecond' => $row[4],
                ];
                array_push($data, $item);
            }

        }
        return collect($data);
    }

    public function extract_incoming_call($array)
    {
        $data = [];
        foreach($array as $row) {
            $name = $row[1];
            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })
            ->first();
            if($support_agent) {
                $item = [
                    'eyebeam_id' => $row[0],
                    'support_agent_name' => $support_agent->name,
                    'Count' => $row[2],
                    'DurationBysecond' => $row[3],
                ];
                array_push($data, $item);
            }

        }
        return collect($data);
    }

    public function extract_chat($array)
    {
        $data = [];
        foreach($array as $row) {
            $email = $row[0];
            $support_agent = $this->support_agents->where('email', $email)->first();
            if($support_agent) {
                $item = [
                    'support_agent_name' => $support_agent->name,
                    'chats_rated_good' => $row[1],
                    'chats_count' => $row[2],
                    'Rated_Count' => $row[3],
                    'chat_simultaneously' => $row[4],
                    'workdurationbysecond' => $row[5],
                ];
                array_push($data, $item);
            }
        }
        return collect($data);
    }

    public function extract_account_manager($array)
    {
        $data = [];
        foreach($array as $row) {
            $name = str()->replace(' - ', ' ', $row[0]);
            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })
            ->first();
            if($support_agent) {
                $item = [
                    'support_agent_name' => $support_agent->name,
                    'Total_accountManager' => $row[1],
                ];
                array_push($data, $item);
            }

        }
        return collect($data);
    }

    public function extract_fata($array)
    {
        $data = [];
        foreach($array as $row) {
            $name = str()->replace(' - ', ' ', $row[0]);
            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })
            ->first();
            if($support_agent) {
                $item = [
                    'support_agent_name' => $support_agent->name,
                    'Cases_Counts' => $row[1],
                    'User_Counts' => $row[2],
                ];
                array_push($data, $item);
            }

        }
        return collect($data);
    }

    public function extract_outgoing_call($array)
    {
        $data = [];
        foreach($array as $row) {
            $name = $row[0];
            
            $support_agent = $this->support_agents
            ->filter(function($user) use ($name) {
                return ($user->name == $name or $user->nickname == $name);
            })
            ->first();

            if($support_agent) {
                $item = [
                    'support_agent_name' => $support_agent->name,
                    'Durationbysecond' => $row[1],
                    'Counts' => $row[2],
                ];
                array_push($data, $item);
            }

        }
        return collect($data);
    }

    public function extract_position_log($array)
    {
        // seconds
        $data = [];
        foreach($array as $row) {
            $item = [
                'support_agent_name' => $row[0],
                'working_hours'=> $row[1],
                'livechat_ticket' => $row[22] ?? 0,
                'email' => $row[23] ?? 0,
                'legal' => $row[24] ?? 0,
                'supervisor_task' => $row[25] ?? 0,
                'phinix' => $row[26] ?? 0,
                'sls_night_shift' => $row[27] ?? 0,
                'memo_deposit' => $row[28] ?? 0,

                'leaves' => $row[3] ?? 0,
                'rests' => $row[4] ?? 0,
                'coach' => $row[5] ?? 0,
                'meeting' => $row[6] ?? 0,
                'tutorial' => $row[7] ?? 0,
                'office' => $row[8] ?? 0,
                'IT' => $row[10] ?? 0,
                'act' => $row[11] ?? 0,
            ];
            array_push($data, $item);
        }
        return collect($data);
    }

    public function render()
    {
        return view('livewire.o-k-r');
    }
}
