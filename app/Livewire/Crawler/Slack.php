<?php

namespace App\Livewire\Crawler;

use App\Jobs\CrawlSlackChannel;
use Livewire\Component;
use Illuminate\Support\Facades\Http;

class Slack extends Component
{

    public $url = "https://slack.com/api/conversations.history";
    public $api = "*********************************************************";
    // public $channel = "C04LB9KH83X";    // faq
    public $channel = "C0889QPAMPD";    // test (annoucements)

    public $limit = 5;   // max

    public function mount()
    {
        CrawlSlackChannel::dispatch();
        // $this->start();
        // dd($this->get_thread_replies('1737278859.511739'));
    }
    
    public function start()
    {
        dd($this->get_messages());

        $object = $this->get_messages();
        
        $has_more = $object?->has_more;
        $next_cursor = $object->response_metadata?->next_cursor;

        // $pin_count = $object->pin_count;
        // $channel_actions_ts = $object->channel_actions_ts;
        // $channel_actions_count = $object->channel_actions_count;
        // $warnings = $object->warnings;
        $this->process_messages_or_replies($object->messages);
    }

    public function process_messages_or_replies($messages_or_replies)
    {
        foreach ($messages_or_replies as $messages_or_reply)
        {
            if($this->should_continue($messages_or_reply))
            {
                continue;
            }
            $this->process_message_or_reply($messages_or_reply);
        }
    }

    public function should_continue($message_or_reply)
    {
        $subtype = $message_or_reply?->subtype ?? null;
        // thread_broadcast
        return ($subtype == 'bot_message' or $subtype == 'tombstone' or $subtype=='channel_join' or $subtype == 'bot_add');
    }

    public function process_message_or_reply($message)
    {
            // $username = $message?->username ?? null;
            // $bot_profile = $message?->bot_profile ?? null;
            // $bot_id = $message?->bot_id ?? null;
            // $app_id = $message?->app_id ?? null;
            // $trigger_id = $message?->trigger_id ?? null;
            // $display_as_bot = $message?->display_as_bot ?? null;
            
            // $client_msg_id = $message?->client_msg_id ?? null;
            // $type = $message?->type ?? 'message';

            $user = $message?->user ?? null;
            // $parent_user_id = $message?->parent_user_id ?? null;

            $ts = $message?->ts ?? null;

            $files = $message?->files ?? [];
            // $upload = $message?->upload ?? null;
            // $blocks = $message?->blocks ?? [];
            // $reactions = $message?->reactions ?? [];

            $text = $message?->text ?? '';

            $team = $message?->team ?? null;    // T01K6BX1S87

            $thread_ts = $message?->thread_ts ?? null;  // parent_ts
            $root = $message?->root ?? null;    // parent_message
            $reply_count = $message?->reply_count ?? 0;
            $latest_reply = $message?->latest_reply ?? null;
            // $reply_users_count = $message?->reply_users_count ?? 0;
            // $reply_users = $message?->reply_users ?? [];

            // $is_locked = $message?->is_locked ?? null;
            // $subscribed = $message?->subscribed ?? null;
    }

    public function get_messages()
    {
        $body = [
            'channel'=>$this->channel,
            'limit'=>$this->limit,
            // 'cursor'=>'bmV4dF90czoxNzM3Mjc2MDY4ODEyNDY5',
            // 'latest'=>'1737276864.702209',       // before
            // 'oldest'=>'1737276864.702209',       // after
            // 'latest'=>'0',
        ];
        $response = Http::withToken($this->api)
        ->get('https://slack.com/api/conversations.history',$body)
        ->object();
        return $response;
    }

    public function get_thread_replies($ts)
    {
        $response = Http::withToken($this->api)
        ->get('https://slack.com/api/conversations.replies',[
            'channel'=>$this->channel,
            'ts'=>$ts,
        ])->object();
        // $has_more = $response->has_more;
        return array_slice($response->messages, 1);
    }

    public function render()
    {
        return view('livewire.crawler.slack');
    }
}
