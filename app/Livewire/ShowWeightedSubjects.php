<?php

namespace App\Livewire;

use App\Models\Weighted_subject;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;

class ShowWeightedSubjects extends Component
{
    public string $subject;
    public float $weight;

    protected $rules = [
        'subject'=>'required|string',
        'weight'=>'required|integer|min:1',
    ];

    public function mount()
    {

    }

    public function add()
    {
        $validated = $this->validate();
        Weighted_subject::create($validated);
    }

    public function delete($id)
    {
        $weighted_subject = Weighted_subject::find($id);
        if (! Gate::allows('delete-weighted-subject')) {
            abort(403);
        }
        $weighted_subject->delete();
    }

    public function toggle($id)
    {
        $weighted_subject = Weighted_subject::find($id);
        $weighted_subject->status = !$weighted_subject->status;
        $weighted_subject->save();
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        $weighted_subjects = Weighted_subject::all();

        return view('livewire.show-weighted-subjects',compact('weighted_subjects'));
    }
}
