<?php

namespace App\Livewire;

use App\Models\Comment;
use App\Models\Objection;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON>k<PERSON><PERSON>ser\Verta\Verta;

class ShowObjects extends Component
{

    use WithPagination;

    public string $from_date = '';
    public string $to_date = '';
    public string $search = '';
    
    public $incoming_type;
    public $support_agent_id;
    public $qc_agent_id;
    public $status;
    
    protected $queryString = ['from_date','to_date','search','incoming_type','support_agent_id','status'];

    public function delete($id)
    {
        $pending = Objection::find($id)->status == 'pending';
        if($pending)
        {
            $comments = Comment::where('object_id',$id);
            $comments->delete();
            Objection::find($id)->delete();
            $this->dispatch('operation-successful');
        }
        else
        {
            $this->dispatch('operation-failed');
        }
    }

    public function render()
    {
        $role_id = auth()->user()->role_id;
        
        $objects = Objection::
        when($role_id==1,function($q) {
            $q->whereHas('record',function ($query){
                $query->where('support_agent_id',auth()->user()->id);
            });
        })
        ->when($this->from_date,function($q){
            $q->where('created_at','>=',Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at','<',Verta::parse($this->to_date)->toCarbon());
        })
        ->when($this->search,function($q){
            $q->whereHas('record',function ($query){
                $query->where('identity','LIKE', "%" . $this->search . "%");
            });
        })
        ->when($this->support_agent_id,function($q){
            $q->whereHas('record',function ($query){
                $query->where('support_agent_id',$this->support_agent_id);
            });
        })
        ->when($this->qc_agent_id,function($q){
            $q->whereHas('record',function ($query){
                $query->where('qc_agent_id',$this->qc_agent_id);
            });
        })
        ->when($this->status,function($q){
            $q->where('status',$this->status);
        })
        ->latest()
        ->paginate(10);

        return view('livewire.show-objects',[
            'objects'=>$objects,
            'support_agents' => User::whereIn('role_id',[1,2,3])->get(),
            'qc_agents' => User::where('role_id',2)->get(),
        ]);
    }
}
