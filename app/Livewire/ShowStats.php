<?php

namespace App\Livewire;

use App\Models\Parameter;
use Livewire\Component;
use App\Models\Period;
use App\Models\Record;
use App\Models\Setting;
use App\Models\Snapshot;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class ShowStats extends Component
{
    public $users = [];
    public $parameters;
    public $periods;
    public int $period_id;
    public $period;

    public array $chat_scores = [];
    public array $chat_count;
    public array $call_scores = [];
    public array $call_count;
    public array $kyc_scores = [];
    public array $kyc_count;
    public array $outgoing_scores = [];
    public array $outgoing_count;
    public array $redline_scores = [];
    public array $qc_scores = [];
    public array $final_qc_scores = [];

    public array $supervisor_scores = [];
    public array $final_scores = [];
    public int $supervisor_coeff;
    public int $qc_coeff;
    public bool $weight;

    public $supervisors;
    public $supervisor_id;

    public function mount()
    {
        $this->periods = Period::all();
        $this->parameters = Parameter::where('incoming_type', 'supervisor')->get();
        $this->supervisor_coeff = Setting::where('name', 'supervisor_coeff')->first()->value;
        $this->qc_coeff = Setting::where('name', 'qc_coeff')->first()->value;
        $this->weight = (bool) Setting::where('name', 'weight_integration')->first()->value;
        $this->supervisors = User::where('role_id',3)->get();
    }

    public function get_parameters()
    {

    }

    public function get_users()
    {
        return User::where('role_id', 1)
        ->when($this->supervisor_id,function($q){
            $q->where('supervisor_id',$this->supervisor_id);
        })
        ->where('status', true)
        ->with(['supervisor_parameters' => function ($q) {
            // $q->wherePivot('period_id', $this->period_id);
            $q->where('period_id', $this->period_id);
        }])
        ->where(function (Builder $query) {
            // return
            $query->whereHas('supervisor_parameters', function ($q) {
                $q->where('period_id', $this->period_id);
            })->orWhereHas('records', function ($q) {
                $q->where('incoming_date', '>=', $this->period->from)
                ->where('incoming_date', '<', $this->period->to);
            });
        })
        ->get();
    }

    public function get_records()
    {
        
    }

    public function updatedPeriodId()
    {
        $this->re_calc();
    }

    public function updatedSupervisorId()
    {
        if(isset($this->period_id)) { $this->re_calc(); }
    }

    public function re_calc()
    {
        $period = $this->periods->firstWhere('id', $this->period_id);
        $this->period = $period;
        
        $this->parameters = Parameter::where('incoming_type', 'supervisor')
        ->whereHas('supervisor_periods', function ($q) {
            $q->where('period_id', $this->period_id);
        })
        ->get();

        $this->users = $this->get_users();

        $records = Record::with(['qc_parameters','red_lines'])
        ->where('incoming_date', '>=', $period->from)
        ->where('incoming_date', '<', $period->to)
        ->get();

        foreach($this->users as $user) {
            // calc total qc score
            $scores = [];
            $chat_scores = [];
            $call_scores = [];
            $kyc_scores = [];
            $outgoing_scores = [];
            $redline_scores = [];

            foreach($records->where('support_agent_id', $user->id)->where('incoming_type', 'chat')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;
                }

                if($sum) {
                    $coefficient = 1;
                    if($this->weight){$coefficient = $record->coefficient;}

                    for($weight=1;$weight<=$coefficient;$weight++)
                    {
                        $chat_scores[] = $total / $sum * 100;
                        $scores[] = $total / $sum * 100;
                    }
                }
            }

            foreach($records->where('support_agent_id', $user->id)->where('incoming_type', 'call')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;
                }
                if($sum) {
                    $coefficient = 1;
                    if($this->weight){$coefficient = $record->coefficient;}

                    for($weight=1;$weight<=$coefficient;$weight++)
                    {
                        $call_scores[] = $total / $sum * 100;
                        $scores[] = $total / $sum * 100;
                    }

                }
            }

            foreach($records->where('support_agent_id', $user->id)->where('incoming_type', 'kyc')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;
                }
                if($sum) {
                    $kyc_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;
                }
            }

            foreach($records->where('support_agent_id', $user->id)->where('incoming_type', 'outgoing')->where('ignore',false) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;
                }
                if($sum) {
                    $outgoing_scores[] = $total / $sum * 100;
                    $scores[] = $total / $sum * 100;
                }
            }

            foreach($records->where('support_agent_id', $user->id)->where('red', true) as $record) {
                $sum = 0.0;
                $total = 0.0;
                foreach ($record->red_lines as $red_line) {
                    if(is_null($red_line->pivot->value)) {
                        continue;
                    }
                    $sum += $red_line->score;
                    $total += $red_line->pivot->value;
                }
                if($sum) {
                    $redline_scores[] = $total;
                }
            }

            $this->chat_scores[$user->id] = count($chat_scores) ? round(array_sum($chat_scores) / count($chat_scores), 2) : '-';
            $this->chat_count[$user->id] = count($chat_scores);

            $this->call_scores[$user->id] = count($call_scores) ? round(array_sum($call_scores) / count($call_scores), 2) : '-';
            $this->call_count[$user->id] = count($call_scores);

            $this->kyc_scores[$user->id] = count($kyc_scores) ? round(array_sum($kyc_scores) / count($kyc_scores), 2) : '-';
            $this->kyc_count[$user->id] = count($kyc_scores);
            
            $this->outgoing_scores[$user->id] = count($outgoing_scores) ? round(array_sum($outgoing_scores) / count($outgoing_scores), 2) : '-';
            $this->outgoing_count[$user->id] = count($outgoing_scores);

            $this->redline_scores[$user->id] = count($redline_scores) ? array_sum($redline_scores) : '-';
            $this->qc_scores[$user->id] = count($scores) ? round(array_sum($scores) / count($scores), 2) : '-';

            if(count($redline_scores))
            {
                if($this->redline_scores[$user->id] < 10)
                {
                    $this->final_qc_scores[$user->id] = $this->qc_scores[$user->id] - 2;
                }
                elseif($this->redline_scores[$user->id] >= 10 and $this->redline_scores[$user->id] <= 20)
                {
                    $this->final_qc_scores[$user->id] = $this->qc_scores[$user->id] - 3;
                }
                elseif($this->redline_scores[$user->id] > 20)
                {
                    $this->final_qc_scores[$user->id] = $this->qc_scores[$user->id] - 5;
                }
            }
            else
            {
                $this->final_qc_scores[$user->id] = $this->qc_scores[$user->id];
            }

            // calc total supervisor score
            $sum = 0.0;
            $total = 0.0;

            foreach($user->supervisor_parameters as $parameter) {
                if(is_null($parameter->pivot->value)) {
                    continue;
                }
                $sum += $parameter->score;
                $total += $parameter->pivot->value;
            }
            $this->supervisor_scores[$user->id] = $sum ? round($total / $sum * 100, 2) : '-';

            // total agent score
            if($this->supervisor_scores[$user->id] != '-' && $this->final_qc_scores[$user->id] != '-') {
                $this->final_scores[$user->id] = ($this->final_qc_scores[$user->id] * $this->qc_coeff + $this->supervisor_scores[$user->id] * $this->supervisor_coeff) / 100;
                $this->final_scores[$user->id] = round($this->final_scores[$user->id], 2);
            } elseif ($this->supervisor_scores[$user->id] == '-' && $this->final_qc_scores[$user->id] != '-') {
                $this->final_scores[$user->id] = $this->final_qc_scores[$user->id];
            } elseif ($this->supervisor_scores[$user->id] != '-' && $this->final_qc_scores[$user->id] == '-') {
                $this->final_scores[$user->id] = $this->supervisor_scores[$user->id];
            } else {
                $this->final_scores[$user->id] = '-';
            }
        }
    }

    public function picture()
    {
        $data = [];

        foreach($this->get_users() as $user)
        {
            $data['users'][$user->id]['name'] = $user->name;
            foreach($this->parameters as $parameter)
            {
                $data['supervisor_parameters_scores'][$parameter->id] = $parameter->score;
                $data['supervisor_parameters'][$parameter->id] = $parameter->name;
                $data['users'][$user->id]['supervisor_parameters'][$parameter->id] = $user->supervisor_parameters->firstWhere('id',$parameter->id)?->pivot?->value ?? '-';
            }
            $data['users'][$user->id]['supervisor_score'] = $this->supervisor_scores[$user->id];
            $data['users'][$user->id]['chat_score'] = $this->chat_scores[$user->id];
            $data['users'][$user->id]['chat_count'] = $this->chat_count[$user->id];
            $data['users'][$user->id]['call_score'] = $this->call_scores[$user->id];
            $data['users'][$user->id]['call_count'] = $this->call_count[$user->id];
            $data['users'][$user->id]['kyc_score'] = $this->kyc_scores[$user->id];
            $data['users'][$user->id]['kyc_count'] = $this->kyc_count[$user->id];
            $data['users'][$user->id]['redline_score'] = $this->redline_scores[$user->id];
            $data['users'][$user->id]['qc_score'] = $this->qc_scores[$user->id];
            $data['users'][$user->id]['final_qc_score'] = $this->final_qc_scores[$user->id];
            $data['users'][$user->id]['final_score'] = $this->final_scores[$user->id];
        }

        
        $snapshot = Snapshot::create([
            'data' => json_encode($data),
            'period_id' => $this->period_id,
        ]);
        $this->dispatch('operation-successful');
        return redirect()->route('show-snapshot',['id'=>$snapshot->id]);
    }

    public function render()
    {
        return view('livewire.show-stats');
    }
}
