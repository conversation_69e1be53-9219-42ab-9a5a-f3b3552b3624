<?php

namespace App\Livewire\Google;

use App\Models\Wrong_deposit;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class DepositWithoutMemo extends Component
{
    use WithPagination;
    public $search = '';
    public $filter = false;
    public $only_me = false;
    public $count;
    public array $results = [
        'تیکت CRM ثبت شد',
        'ریجکتی',
        // 'تست',
        'تکراری ثبت تیکت',
        'صفر',
        'انتقال داخلی-ایمیل شد',
        'رمز نگاری شده',

    ];

    public array $reasons = [
        'هش اشتباه',
        'عدم ثبت هش',
        'عکس نامعتبر',
        'رمز نگاری شده',
    ];

    // protected $rules = [];
    // public function mount() {}

    public function assign($id)
    {
        $wrong_deposit = Wrong_deposit::find($id);
        $wrong_deposit->updated_by = auth()->user()->id;
        $wrong_deposit->save();
        // $this->dispatch('operation-successful');
    }

    public function sync_sheet()
    {
        if (!Gate::allows('sync-sheet')) {
            abort(403);
        }

        $offset = Wrong_deposit::count();
        $response = Http::get("https://sheetdb.io/api/v1/ydesle5p95uur?offset={$offset}");
        $wrong_deposits = $response->collect();
        $data = [];
        foreach($wrong_deposits as $wrong_deposit)
        {
            $values = array_values($wrong_deposit);
            try{
                $datetime = Carbon::parse($values[0]);
            }catch(\Exception $e){
                $datetime= Carbon::createFromTimestamp($values[0]);
            }
            
            $data[]=
            [
                'form_filled_at' => $datetime,
                'phone_number' => $values[1],
                'national_code' => $values[2],
                'memo' => $values[3],
                'hash' => $values[4],
                'screenshot' => $values[5],
                'user_note' => $values[6],
                'email' => $values[7],
                'coin' => $values[8],
                'amount' => $values[9],
                // 10
                // 11
                'result' => $values[12],
                'reason' => $values[13],
                'agent_note' => $values[14],
            ];
        }

        $chunkSize = 100;
        $chunks = array_chunk($data, $chunkSize);

        DB::transaction(function () use ($chunks) {
            foreach ($chunks as $chunk) {
                Wrong_deposit::insert($chunk);
            }
        });
        $this->dispatch('operation-successful');
    }

    public function delete($id)
    {
        $wrong_deposit = Wrong_deposit::find($id);
        // $wrong_deposit->delete();
        // $this->dispatch('operation-successful');

    }

    public function changeResult($id,$value)
    {
        $wrong_deposit = Wrong_deposit::find($id);

        if(!is_null($wrong_deposit->updated_by) and $wrong_deposit->updated_by != auth()->user()->id)
        {
            abort(403);
            return;
        }

        $wrong_deposit->result = $value;
        $wrong_deposit->updated_by = auth()->user()->id;
        $wrong_deposit->save();
        $this->dispatch('operation-successful');
    }

    public function changeReason($id,$value)
    {
        $wrong_deposit = Wrong_deposit::find($id);
        if(!is_null($wrong_deposit->updated_by) and $wrong_deposit->updated_by != auth()->user()->id)
        {
            abort(403);
            return;
        }
        $wrong_deposit->reason = $value;
        $wrong_deposit->updated_by = auth()->user()->id;
        $wrong_deposit->save();
        $this->dispatch('operation-successful');
    }

    public function changeNote($id,$value)
    {
        $wrong_deposit = Wrong_deposit::find($id);
        if(!is_null($wrong_deposit->updated_by) and $wrong_deposit->updated_by != auth()->user()->id)
        {
            abort(403);
            return;
        }
        $wrong_deposit->agent_note = $value;
        $wrong_deposit->updated_by = auth()->user()->id;
        $wrong_deposit->save();
        $this->dispatch('operation-successful');
    }

    public function render()
    {
        $wrong_deposits = Wrong_deposit::
        orderBy('id','asc')
        ->when($this->search,function($q){
            $q->where(function($query){
                $query->where('hash','LIKE',"%{$this->search}%")
                ->orWhere('phone_number','LIKE',"%{$this->search}%")
                ->orWhere('email','LIKE',"%{$this->search}%")
                ->orWhere('national_code','LIKE',"%{$this->search}%")
                ->orWhere('screenshot','LIKE',"%{$this->search}%");
            });
        })
        ->when($this->filter,function($q){
            $q->where(function($query){
                $query->whereNull('result')
                ->orWhere('result','');
            });
        })
        ->when($this->only_me,function($q){
                $q->where('updated_by',auth()->user()->id);
        });
        
        $wrong_deposits->paginate(100);
        $this->count = $wrong_deposits->clone()->count();

        return view('livewire.google.deposit-without-memo',['wrong_deposits'=>$wrong_deposits->paginate(100)]);
    }
}
