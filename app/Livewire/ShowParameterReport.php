<?php

namespace App\Livewire;

use App\Models\Parameter;
use App\Models\Parameter_record;
use App\Models\Period;
use App\Models\User;
use Livewire\Component;

class ShowParameterReport extends Component
{
    public $users = [];
    public $list = [];
    public $platform = 'wallex';

    public $periods;
    public $parameters;

    public $period_id = 0;
    public $parameter_id = 0;

    public function mount()
    {

        $this->periods = Period::all();

        $this->parameters = Parameter::where('active',true)->get();

    }

    public function updated()
    {
        if($this->parameter_id and $this->period_id)
        {
            $this->calc();
        }
    }

    public function calc()
    {
        $this->users = User::
        where('status',true)
        ->where('role_id',1)
        ->where('platform',$this->platform)
        ->get();

        $this->list = [];
        foreach($this->users as $user)
        {
            $parameter = $this->parameters->firstWhere('id',$this->parameter_id);

            # first method
            $sum_grade = 0;
            $count = 0;
            foreach($this->parameters->where('name',$parameter->name) as $parameter)
            {
                $score = $parameter->score;

                $parameter_records = Parameter_record::
                where('parameter_id',$parameter->id)
                ->whereHas('record',function($q)use($user){
                    $q->where('support_agent_id',$user->id)
                    ->where('incoming_date','>=',$this->periods->where('id',$this->period_id)->first()->from)
                    ->where('incoming_date','<',$this->periods->where('id',$this->period_id)->first()->to);
                })->get();

                foreach($parameter_records as $parameter_record)
                {
                    $sum_grade+=$parameter_record->value/$score;
                    $count+=1;
                }
            }
            if($sum_grade==0)
            {
                continue;
            }
            $this->list[$user->id]['name'] = $user->name;
            $this->list[$user->id]['average'] = round($sum_grade/$count*100,2);

            # second method
            // $score = $parameter->score;
            // $average=Parameter_record::
            // where('parameter_id',$this->parameter_id)
            // ->whereHas('record',function($q)use($user){
            //     $q->where('support_agent_id',$user->id);
            // })->average('value');
            // if($average == 0)
            // {
            //     continue;
            // }
            // $this->list[$user->id]['average'] = round($average / $score * 100,2);
            // $this->list[$user->id]['name'] = $user->name;
        }

        $this->list = collect($this->list)->sortBy('average');
    }
    
    public function render()
    {
        return view('livewire.show-parameter-report');
    }
}
