<?php

namespace App\Livewire;

use App\Models\Snapshot;
use Illuminate\Support\Facades\Gate;
use Livewire\WithPagination;
use Livewire\Component;


class ShowSnapshots extends Component
{
    use WithPagination;

    public function delete($id){
        if (! Gate::allows('delete-snapshot')) {
            abort(403);
        }
        Snapshot::find($id)->delete();
        $this->dispatch('operation-successful');
    }

    public function changeNote($id,$note)
    {
        if (! Gate::allows('update-snapshot')) {
            abort(403);
        }
        $snapshot = Snapshot::find($id);
        $snapshot->note = $note;
        $snapshot->save();
        $this->dispatch('operation-successful');


    }

    public function render()
    {
        $snapshots = Snapshot::latest()->paginate(10);
        return view('livewire.show-snapshots',compact('snapshots'));
    }
}
