<?php

namespace App\Livewire;

use Livewire\Component;
use App\Imports\ParslogicImport;
use App\Models\Pool;
use App\Models\User;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Gate;

class UploadCallExcel extends Component
{
    use WithFileUploads;
    public $excel;
    public int $manual = 0;
    public $platform = 'wallex';

    public function save()
    {
        if (! Gate::allows('upload-qc')) {
            abort(403);
        }

        $validated = $this->validate([
            'excel' => 'extensions:xlsx|max:5120', // 51MB Max
        ]);

        $records = $this->process($validated['excel']);
        if($records) {
            // $records = $records[0];
            foreach ($records as &$record) {
                $record['created_at'] = now();
                $record['updated_at'] = now();
            }
            Pool::insertOrIgnore($records);
            $this->dispatch('operation-successful');
            return redirect()->route('pool');
        } else {
            $this->dispatch('operation-failed');
        }
    }

    public function process($file)
    {
        $array = Excel::toArray(new ParslogicImport(), $file);
        // dump($array);
        $array = $array[0];
        array_shift($array);
        $users = User::all();

        $support_agents = $users->where('role_id', 1)
        ->where('platform',$this->platform)
        ->where('weight', '<>', 0);

        $qc_agents = $users->where('role_id', 2)
        ->where('platform',$this->platform)
        ->where('trace_call', '<>', 0);

        $records = [];

        foreach ($array as $record) {
            $duration = $record[7];
            if($duration < 10) {
                continue;
            }
            $eyebeam_id = $record[6];

            $support_agent = $support_agents->firstWhere('eyebeam_id', $eyebeam_id);

            if(is_null($support_agent)) {
                continue;
            }

            $item = [
                'identity' => $record[9],
                'incoming_date' => \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($record[0]),
                'support_agent_id' => $support_agent->id,
                'incoming_type' => 'call',
                'platform'=>$this->platform,
            ];

            array_push($records, $item);
        }
        
        $collection = collect($records);
        $support_agents = $support_agents->whereIn('id',$collection->pluck('support_agent_id')->all());
        $total_weigth = $support_agents->sum('weight');
        $target = $qc_agents->sum('trace_call');
        $result = [];

        foreach($support_agents as $support_agent) {
            $count = ceil($support_agent->weight / $total_weigth * $target);
            $data = $collection->where('support_agent_id', $support_agent->id);
            $data_count = $data->count();
            if($count > $data_count) {
                $this->manual += $count - $data_count;
                $count = $data_count;
            }
            if($count)
            {
                $random = $data->random($count);
                foreach($random->all() as $selected) {
                    array_push($result, $selected);
                }
            }
            
        }
        return $result;
    }

    public function render()
    {
        return view('livewire.upload-call-excel');
    }
}
