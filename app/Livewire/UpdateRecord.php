<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Parameter;
use App\Models\User;
use App\Models\Record;
use App\Models\Setting;
use App\Models\Weighted_subject;
use App\Social\Slack;
use Hek<PERSON><PERSON>ser\Verta\Verta;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class UpdateRecord extends Component
{
    public $record;
    public string $identity;
    public string $incoming_type;
    public string $incoming_date;
    public int $support_agent_id;
    public string $incoming_subject;
    public string $recorded_subject;
    public string $message;
    public string $feedback;
    public bool $red;
    public bool $draft;
    public bool $challenging;
    public bool $ignore;
    public string $crm_identity='';
    public int $coefficient;
    public $once = true;
    public $users;
    public $qc_parameters = [];
    public $scores = [];

    public $red_lines = [];
    public $red_scores = [];

    protected $queryString = ['identity'];

    protected $rules = [
        'identity' => 'required|string',
        'incoming_type' => 'required|in:chat,call,kyc,outgoing,faq,email,ticket',
        'incoming_date' => 'required|string',
        'support_agent_id' => 'required|integer|exists:users,id',
        'incoming_subject' => 'required|string',
        'recorded_subject' => 'required|string',
        'message' => 'string|nullable',
        'feedback' => 'string|nullable',
        'red' => 'required|boolean',
        'draft' => 'required|boolean',
        'challenging' => 'required|boolean',
        'ignore' => 'required|boolean',
        'crm_identity' => 'required|string',
        'coefficient' => 'required|integer',
        'scores' => 'required|array',
        // 'red_scores' => 'required|array',
    ];

    public function mount()
    {
        $this->identity = urldecode($_GET['identity']);
        $this->record = Record::with('qc_parameters')->where('identity', $this->identity)->firstOrFail();
        $this->incoming_type = $this->record->incoming_type;

        $this->users = User::all();

        $this->qc_parameters = Parameter::where('incoming_type', $this->incoming_type)
        ->where(function ($query) {
            $query->where('active', true)
                  ->orWhereIn('id', $this->record->qc_parameters->pluck('id')->toArray());
        })
        ->get();
        $this->scores = [];

        foreach($this->qc_parameters as $qc_parameter) {
            $this->scores[$qc_parameter->id] = null;
        }
        foreach($this->record->qc_parameters as $qc_parameter) {
            $this->scores[$qc_parameter->id] = $qc_parameter->pivot->value;
        }

        if($this->incoming_type == 'chat') {
            $red = 'red_chat';
        } elseif($this->incoming_type == 'call') {
            $red = 'red_call';
        }  elseif($this->incoming_type == 'outgoing') {
            $red = 'red_outgoing';
        } else {
            $red = null;
        }

        $this->red_lines = Parameter::where('incoming_type', $red)
        ->where(function ($query) {
            $query->where('active', true)
                  ->orWhereIn('id', $this->record->red_lines->pluck('id')->toArray());
        })
        ->get();
        $this->red_scores = [];

        foreach($this->red_lines as $red_line) {
            $this->red_scores[$red_line->id] = null;
        }

        foreach($this->record->red_lines as $red_line) {
            $this->red_scores[$red_line->id] = $red_line->pivot->value;
        }

        $this->incoming_date = verta($this->record->incoming_date)->format('Y/m/d');
        $this->support_agent_id = $this->record->support_agent_id;
        $this->incoming_subject = $this->record->incoming_subject;
        $this->recorded_subject = $this->record->recorded_subject;
        $this->message = $this->record->message ?? "";
        $this->feedback = $this->record->feedback ?? "";
        $this->red = $this->record->red;
        $this->draft = $this->record->draft;
        $this->challenging = $this->record->challenging;
        $this->ignore = $this->record->ignore;
        $this->coefficient=$this->record->coefficient;
        $this->crm_identity=$this->record->crm_identity ?? '';
    }


    public function changeRedScore($red_line_id, $red_score_value)
    {
        if(empty($red_score_value) and $red_score_value != 0) {
            $red_score_value = null;
        }
        $this->red_scores[$red_line_id] = $red_score_value;
    }

    public function changeScore($parameter_id, $score_value)
    {
        if(empty($score_value) and $score_value != 0) {
            $score_value = null;
        }
        $this->scores[$parameter_id] = $score_value;
    }

    public function updatedRed()
    {
        if($this->red) {
            foreach($this->record->red_lines as $red_line) {
                $this->red_scores[$red_line->id] = $red_line->pivot->value;
            }
        } else {
            foreach($this->record->red_lines as $red_line) {
                $this->red_scores[$red_line->id] = null;
            }
        }
    }

    public function updatedIncomingSubject()
    {
        $weighted_subject = Weighted_subject::where('subject',trim($this->incoming_subject))->first();
        if($weighted_subject)
        {
            $this->coefficient=$weighted_subject->weight;
        }else{
            $this->coefficient=1;
        }
    }

    public function save()
    {
        if (! Gate::allows('update-record', $this->record)) {
            abort(403);
        }

        $validated = $this->validate();
        
        $validated['incoming_date'] = Verta::parse($validated['incoming_date'])->toCarbon();
        
        $lock_date = Setting::where('name', 'lock_records_date')->first()->value;
        $lock_date = Verta::parse($lock_date)->toCarbon();

        if($validated['incoming_date'] <= $lock_date) {
            $this->dispatch('operation-failed');
            $this->addError('custom', 'Editing of this record has been locked.');
            return;
        }

        $validated['qc_agent_id'] = $this->record->qc_agent_id;
        $validated['updated_by'] = auth()->user()->id;
        
        unset($validated['scores']);
        unset($validated['red_scores']);

        $data = [];
        foreach ($this->qc_parameters as $qc_parameter) {
            $data[$qc_parameter->id] = ['value' => $this->scores[$qc_parameter->id]];
        }

        foreach ($this->red_lines as $red_line) {
            $data[$red_line->id] = ['value' => $this->red_scores[$red_line->id]];
        }

        DB::transaction(function () use ($validated, $data) {
            $record = Record::updateOrCreate(['identity' => $this->identity], $validated);

            $record->parameters()->sync($data);
        });

        if($this->once and $this->record->red == true and $validated['red'] == false and isset($this->record->thread_ts))
        {
            $this->once = false;
            $text = "red line removed";
            Slack::qc_alert_thread($text,$this->record->thread_ts);
        }

        $this->dispatch('operation-successful');

    }

    public function render()
    {
        return view('livewire.update-record');
    }
}
