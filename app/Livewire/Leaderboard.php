<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\Snapshot;
use App\Models\User;
use Livewire\Component;

class Leaderboard extends Component
{
    public string $platform = 'wallex';
    // wallgold

    public $users = [];
    public $periods = 0;

    public function mount()
    {
    }

    public function updated()
    {
        $platform_active_users = User::where('status',true)->where('platform',$this->platform)->get()->pluck('id')->toArray();
        $this->users = [];
        $support_agents = [];

        $periods = Period::latest()->limit($this->periods)
        ->whereHas('snapshot')
        ->with('snapshot')
        ->get();


        foreach($periods as $period)
        {
            $data = collect(json_decode($period->snapshot['data']));
            $users = $data['users'];
            foreach($users as $user_id => $user)
            {
                // ignoring manually new juniors and exclude qc
                $skip = (in_array($user_id,[54,])) || (!in_array($user_id,$platform_active_users));
                if($skip)  {continue;}
                $final_score = $user->final_score;
                if($final_score == "-") {continue;}

                $support_agents[$user_id][$period->id]['name'] = $user->name;
                $support_agents[$user_id][$period->id]['final_score'] = $final_score;
                $support_agents[$user_id][$period->id]['count'] = $user?->kyc_count + $user?->call_count + $user?->chat_count;
                
                if($user->supervisor_score!='-')
                {
                    $support_agents[$user_id][$period->id]['supervisor_score'] = $user->supervisor_score;
                }
                if($user->final_qc_score!='-')
                {
                    $support_agents[$user_id][$period->id]['final_qc_score'] = $user->final_qc_score;
                }
            }
        }


        foreach($support_agents as $support_agent_id => $support_agent)
        {
            $this->users[$support_agent_id] = [
                'name' => reset($support_agent)['name'],
                'final_score' => round(collect($support_agent)->pluck('final_score')->average(),2),

                'supervisor_score' => round(collect($support_agent)->pluck('supervisor_score')->average(),2),
                'final_qc_score' => round(collect($support_agent)->pluck('final_qc_score')->average(),2),
                'count' => collect($support_agent)->pluck('count')->sum(),
            ];
        }

        $this->users = collect($this->users)->sortByDesc('final_score');
    }
    public function render()
    {
        // $snapshots = Snapshot::latest()->whereIn('period_id',$periods)->get()->unique('period_id');
        
        return view('livewire.leaderboard');
    }
}
