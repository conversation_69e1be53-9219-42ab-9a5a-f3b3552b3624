<?php

namespace App\Livewire;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\Record;
use App\Models\Setting;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON><PERSON><PERSON>ser\Verta\Verta;
use Illuminate\Support\Facades\Gate;

class ShowRecords extends Component
{
    use WithPagination;

    public string $from_date = '';
    public string $to_date = '';
    public $periods;
    public $period_id;

    public string $search = '';
    public $incoming_type;
    public $support_agent_id;
    public $qc_agent_id;
    public $red = false;
    public $ignore = false;

    protected $queryString = ['from_date','to_date','search','incoming_type','support_agent_id','qc_agent_id','red'];

    public function mount()
    {
        if(isset($_GET['search']))
        {
            $this->search = urldecode($_GET['search']);
        }
        $this->periods = Period::all();
    }

    public function delete($id)
    {
        if (! Gate::allows('delete-record')) {
            abort(403);
        }

        $record = Record::find($id);
        $record->delete();
        $this->dispatch('operation-successful');
    }

    public function rendered()
    {
        $this->dispatch('initFlowbite');
    }

    public function render()
    {
        $role_id = auth()->user()->role_id;

        $show_date = Setting::where('name', 'show_records_date')->first()->value;
        $show_date = Verta::parse($show_date)->toCarbon();

        $records = Record::with('qc_parameters', 'red_lines','object')
        ->when(in_array($role_id,[1]), function ($query) use ($show_date) {
            $query
            ->where('support_agent_id', auth()->user()->id)
            ->where('incoming_date', '<=', $show_date);
        })
        ->when(in_array($role_id,[3]), function ($query) use ($show_date) {
            $query
            ->whereNotIn('support_agent_id', User::where('role_id',3)->where('id','<>',auth()->user()->id)->get()->pluck('id')->toArray());
        })
        ->when($this->red, function ($query) {
            $query->where('red', true);
        })
        ->when($this->ignore, function ($query) {
            $query->where('ignore', true);
        })
        ->when($this->from_date, function ($q) {
            $q->where('incoming_date', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date, function ($q) {
            $q->where('incoming_date', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->when($this->period_id, function ($q) {
            $q->where('incoming_date', '>=', $this->periods->where('id',$this->period_id)->first()->from)
            ->where('incoming_date', '<', $this->periods->where('id',$this->period_id)->first()->to);
        })
        ->when($this->search, function ($q) {
            $q
            ->where('identity', 'like', "%" . trim($this->search) . "%")
            ->orWhere('incoming_subject', 'like', "%" . trim($this->search) . "%")
            ->orWhere('recorded_subject', 'like', "%" . trim($this->search) . "%");
        })
        ->when($this->incoming_type, function ($q) {
            $q->where('incoming_type', $this->incoming_type);
        })
        ->when($this->support_agent_id, function ($q) {
            $q->where('support_agent_id', $this->support_agent_id);
        })
        ->when($this->qc_agent_id, function ($q) {
            if($this->qc_agent_id == 'except') {
                $q->whereNotIn('qc_agent_id', User::where('role_id', 2)->get()->pluck('id'));
            } else {
                $q->where('qc_agent_id', $this->qc_agent_id);
            }
        })
        ->when(request()->input('parameter_id',false),function($q){
            $q->whereHas('qc_parameters',function($query){
                $query
                ->whereNotNull('value')
                ->where('value','<',Parameter::where('id',request()->input('parameter_id'))->first()->score)
                ->where('parameter_id',request()->input('parameter_id'));
            });
        })
        ->latest()
        ->paginate(10);

        $users = User::where('status',true)->get();

        return view('livewire.show-records', [
            'records' => $records,
            'support_agents' => $users->whereIn('role_id', [1,2,3]),
            'qc_agents' => $users->whereIn('role_id', [2]),
        ]);
    }
}
