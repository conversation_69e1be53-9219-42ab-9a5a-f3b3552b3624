<?php

namespace App\Livewire;

use App\Models\Livechat;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
class ShowLivechats extends Component
{
    use WithPagination;

    protected $queryString = [
        'search',
        'id',
        'platform',
        'shift',
        'orderBy',
        'filter',
        'support_agent_email',
        'from_date',
        'to_date',
    ];

    public $count = 0;

    public $platform = 'wallex';

    public $min_frt = '';
    public $average_frt = '';
    public $max_frt = '';

    public $min_chat_duration = '';
    public $aht = '';
    public $max_chat_duration = '';

    public $queue_count = '';
    public $average_queue = '';
    public $max_queue = '';

    public $orderBy='id';
    public string $search = '';
    public $shift='';
    public int $id = 0;

    public $from_date;
    public $to_date;
    public $support_agents;
    public $support_agent_email;
    public $filter;

    public function mount()
    {
        $this->support_agents = User::where('role_id',1)->where('status',true)->get();
    }

    public function delete ($id)
    {
        Livechat::find($id)->delete();
    }

    public function inspect($id)
    {
        $webhook = new \App\Webhook\Livechat();

        $chat = Livechat::find($id);
        if(in_array(auth()->user()->id,[1,55]))
        {
            $response = $webhook->list_archives($chat->thread_id,$chat->platform);
            $object=$response->object();
             dd($object);
        }
    }

    public function render()
    {
        $livechats = Livechat::
        where('platform',$this->platform)
        ->orderBy($this->orderBy,'desc')
        ->when($this->search,function($q){
            $q->where('thread_id','LIKE',"%{$this->search}%")->orWhere('chat_id','LIKE',"%{$this->search}%");
        })
        ->when($this->id,function($q){
            $q->where('id','=',$this->id);
        })
        ->when($this->from_date,function($q){
            $q->where('created_at', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->when($this->support_agent_email,function($q){
            $q->where('author_id',$this->support_agent_email);
        })

        ->when($this->shift,function($q){
            $q->when($this->shift=="صبح",function($q){
                $q->whereRaw('HOUR(created_at) BETWEEN 8 AND 15');
            })
            ->when($this->shift=="عصر",function($q){
                $q->whereRaw('HOUR(created_at) BETWEEN 16 AND 23');
            })
            ->when($this->shift=="شب",function($q){
                $q->whereRaw('HOUR(created_at) BETWEEN 0 AND 7');
            });
        })
        
        // ->when($this->shift,function($q){
        //     $q->when($this->filter=='missed-chat',function($q){
        //         $q->when($this->shift=="صبح",function($q){
        //             $q->whereRaw('HOUR(created_at) BETWEEN 8 AND 15');
        //         })
        //         ->when($this->shift=="عصر",function($q){
        //             $q->whereRaw('HOUR(created_at) BETWEEN 16 AND 23');
        //         })
        //         ->when($this->shift=="شب",function($q){
        //             $q->whereRaw('HOUR(created_at) BETWEEN 0 AND 7');
        //         });
        //     },function($q){
        //         $q->whereIn('author_id',User::where('shift',$this->shift)->where('status',true)->pluck('email')->toArray());
        //     });
        // })

        ->when($this->filter=='missed-chat',function($q){
            $q->whereHas('missed_chat');
        })
        ->when($this->filter=='inactive-transfer',function($q){
            $q->whereHas('inactive_transfer');
        })
        ->when($this->filter=='lost-connection',function($q){
            $q->whereHas('lost_connection');
        })
        ->when($this->filter=='queue-abandonment',function($q){
            $q->whereHas('queue_abandonment');
        })
        ->when($this->filter=='signed-out-transfer',function($q){
            $q->whereHas('signed_out_transfer');
        })
        ->when($this->filter=='chat-transfer',function($q){
            $q->whereHas('chat_transfer');
        })
        ->when($this->filter=='take-over',function($q){
            $q->whereHas('take_over');
        })
        ->when($this->filter=='inactive-archived',function($q){
            $q->whereHas('inactive_archived')
            ->where('ticket',false);
        })
        ->when($this->filter=='offline',function($q){
            $q->where('offline',true);
        })
        ->when($this->filter=='ai',function($q){
            $q->where('ai',true);
        })
        ->when($this->filter=='comment',function($q){
            $q->where('comment',true);
        })
        ->when($this->filter=='ticket',function($q){
            $q->where('ticket',true);
        })
        ->when($this->filter=='chat_rated',function($q){
            $q->whereNotNull('chat_rated');
        })
        ->when($this->filter=='good',function($q){
            $q->where('chat_rated',true);
        })
        ->when($this->filter=='bad',function($q){
            $q->where('chat_rated',false);
        })
        ->when($this->filter=='started_by_agent',function($q){
            $q->where('started_by_agent',true);
        })
        ->when($this->filter=='closed_by_agent',function($q){
            $q->where('archived_method','manual_archived_agent')
            ->where('ticket',false)
            ->where(function($query){
                $query->where('survey','<>',true)
                ->orWhereNull('survey');
            });
        });
        
        $this->count = $livechats->clone()->count();

        // aht
        $aht = $livechats->clone()
        ->where('chat_duration','>',0)
        ->average('chat_duration');
        $this->aht = round($aht,2);

        $min_chat_duration = $livechats->clone()
        ->where('chat_duration','>',0)
        ->min('chat_duration');
        $this->min_chat_duration = round($min_chat_duration,2);

        $max_chat_duration = $livechats->clone()
        ->where('chat_duration','>',0)
        ->max('chat_duration');
        $this->max_chat_duration = round($max_chat_duration,2);

        // frt
        $min_frt =  $livechats->clone()
        ->where('FRT','>',0)
        ->min('FRT');
        $this->min_frt = round($min_frt,2);

        $max_frt = $livechats->clone()
        ->where('FRT','>',0)
        ->max('FRT');
        $this->max_frt = round($max_frt,2);

        $average_frt = $livechats->clone()
        ->where('FRT','>',0)
        ->average('FRT');
        $this->average_frt = round($average_frt,2);

        // queue
        $this->queue_count = $livechats->clone()->where('queues_duration','>',0)->count();

        $max_queue = $livechats->clone()
        ->max('queues_duration');
        $this->max_queue = round($max_queue,2);

        $average_queue = $livechats->clone()
        ->where('queues_duration','>',0)
        ->average('queues_duration');
        $this->average_queue = round($average_queue,2);

        return view('livewire.show-livechats',[
            'livechats'=>$livechats->clone()->paginate(100),
        ]);
    }
}
