<?php

namespace App\Livewire;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\Record;
use App\Models\User;
use Livewire\Component;

class ShowSupervisorReport extends Component
{
    public $users;
    public $periods;
    public $parameters;
    public $supervisor_id = 0;

    public $faq_scores = [];
    public $email_scores = [];
    public $ticket_scores = [];
    
    public $scores = [];

    protected $rules = [
        'supervisor_id' => 'required|exists:users,id',
    ];

    public function mount()
    {
        $this->users = User::where('status', true)
        ->where(function($q){
            $q->whereIn('role_id', [3])
            ->orWhereIn('id',[
                67,
                80,
                4,
                9,
                41,
                43,
                42,
                14,
                13,
                44,
            ]);
        })
        ->when(auth()->user()->role_id<4,function($q){
            $q->where('id',auth()->user()->id);
        })
        ->get();

        $this->periods = Period::
        where('id',">",13)
        ->get();

        $this->parameters = Parameter::whereIn('incoming_type', ['faq','email','ticket'])
        // ->where('active',true)
        ->get();
    }
    

    public function updated()
    {
        $this->validate();

        foreach($this->periods as $period)
        {
            $records = Record::with(['qc_parameters'])
            ->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->where('support_agent_id', $this->supervisor_id)
            ->get();

            $scores = [];
            $faq_scores = [];
            $email_scores = [];
            $ticket_scores = [];

            foreach($records->where('incoming_type', 'faq') as $record) {
                $sum = 0.0;
                $total = 0.0;

                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                $faq_scores[] = $total / $sum * 100;
                $scores[] = $total / $sum * 100;
            }

            foreach($records->where('incoming_type', 'email') as $record) {
                $sum = 0.0;
                $total = 0.0;

                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                $email_scores[] = $total / $sum * 100;
                $scores[] = $total / $sum * 100;
            }

            foreach($records->where('incoming_type', 'ticket') as $record) {
                $sum = 0.0;
                $total = 0.0;

                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                $ticket_scores[] = $total / $sum * 100;
                $scores[] = $total / $sum * 100;
            }

            $this->faq_scores[$period->id] = count($faq_scores) ? round(array_sum($faq_scores) / count($faq_scores), 2) : '-';
            $this->email_scores[$period->id] = count($email_scores) ? round(array_sum($email_scores) / count($email_scores), 2) : '-';
            $this->ticket_scores[$period->id] = count($ticket_scores) ? round(array_sum($ticket_scores) / count($ticket_scores), 2) : '-';

            $this->scores[$period->id] = count($scores) ? round(array_sum($scores) / count($scores), 2) : '-';
        }
    }

    public function render()
    {
        return view('livewire.show-supervisor-report');
    }
}
