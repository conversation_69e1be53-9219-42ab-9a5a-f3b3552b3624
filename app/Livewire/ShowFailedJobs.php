<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
// use Illuminate\Queue\Failed\FailedJobProviderInterface;
use Illuminate\Support\Facades\DB;
class ShowFailedJobs extends Component
{
    use WithPagination;
    
    public function render()
    {
        return view('livewire.show-failed-jobs',[
            'jobs'=>DB::table('failed_jobs')->paginate(20),
        ]);
    }
}
