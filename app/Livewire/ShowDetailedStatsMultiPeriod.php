<?php

namespace App\Livewire;

use Livewire\Component;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\User;

class ShowDetailedStatsMultiPeriod extends Component
{
    public int $from_period_id;
    public int $to_period_id;

    public $supervisor_id = 0;
    public $agent_id = 0;

    public $supervisors = [];
    public $agents = [];

    public function mount($from_period_id,$to_period_id)
    {
        $this->supervisors = User::where('role_id',3)
        ->where('status',true)
        ->get();

        $this->agents = User::whereIn('role_id',[1,2])
        ->where('status',true)
        ->get();

        $this->from_period_id = $from_period_id;
        $this->to_period_id = $to_period_id;
    }

    public function get_data()
    {
        $from_period = Period::find($this->from_period_id);
        $to_period = Period::find($this->to_period_id);

        return Parameter::whereIn('incoming_type', ['chat', 'call'])
        ->whereHas('records', function ($q) use ($from_period,$to_period) {
            $q->where('incoming_date', '>=', $from_period->from)
            ->where('incoming_date', '<', $to_period->to)
            ->when($this->agent_id,function($query){
                $query->where('support_agent_id',$this->agent_id);
            })
            ->when($this->supervisor_id, function ($query) {
                $query->whereRelation('user', 'supervisor_id', $this->supervisor_id);
            });
        })
        ->with(['records' => function ($q) use ($from_period,$to_period) {
            $q->where('incoming_date', '>=', $from_period->from)
            ->where('incoming_date', '<', $to_period->to)
            ->when($this->agent_id,function($query){
                $query->where('support_agent_id',$this->agent_id);
            })
            ->when($this->supervisor_id, function ($query) {
                $query->whereRelation('user', 'supervisor_id', $this->supervisor_id);
            });
        }])
        ->get();
    }

    public function get_super_data()
    {
        return Parameter::
        where('incoming_type','supervisor')
        ->whereHas('supervisor_periods', function ($q) {
            $q->whereIn('period_id', Period::where('id','>=',$this->from_period_id)->where('id','<=',$this->to_period_id)->get()->pluck('id')->toArray())
            ->when($this->agent_id,function($query){
                $query->where('user_id',$this->agent_id);
            })
            ->when($this->supervisor_id,function($query){
                $query->whereIn('user_id',User::where('supervisor_id',$this->supervisor_id)->where('role_id',1)->where('status',true)->get()->pluck('id')->toArray());
            });
        })
        ->with('supervisor_scores',function($q) {
            $q->whereIn('period_id', Period::where('id','>=',$this->from_period_id)->where('id','<=',$this->to_period_id)->get()->pluck('id')->toArray())
            ->when($this->agent_id,function($query){
                $query->where('user_id',$this->agent_id);
            })
            ->when($this->supervisor_id,function($query){
                $query->whereIn('user_id',User::where('supervisor_id',$this->supervisor_id)->where('role_id',1)->where('status',true)->get()->pluck('id')->toArray());
            });
        })
        ->get();
    }

    public function render()
    {
        return view('livewire.show-detailed-stats-multi-period',[
            'parameters'=>$this->get_data(),
            'supervisor_parameters'=>$this->get_super_data(),
        ]);
    }
}
