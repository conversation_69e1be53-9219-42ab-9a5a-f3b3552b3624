<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\User;
use Livewire\Component;

class WeightedSubjectsReport extends Component
{
    public $periods;
    public $period_id;
    public $users = [];

    public function mount()
    {
        $this->periods = Period::where('id','>=',7)->get();
    }

    public function updated()
    {
        $period = $this->periods->where('id',$this->period_id)->first();
        $this->users = User::where('role_id',1)->where('status',true)
        ->withCount(
        [
            'records as weighted_one'=>function($q) use ($period){
                $q->where('incoming_date','>=',$period->from)
                ->where('incoming_date','<',$period->to)
                ->where('coefficient',1);
            },
            "records as weighted_more"=>function($q) use ($period){
                $q->where('incoming_date','>=',$period->from)
                ->where('incoming_date','<',$period->to)
                ->where('coefficient','<>',1);
            },
        ])
        ->get();
    }

    public function render()
    {
        return view('livewire.weighted-subjects-report');
    }
}
