<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\Setting;
use App\Models\Stat;
use App\Models\User;
use App\Models\Item;

use Livewire\Component;
use Illuminate\Support\Facades\Gate;

class ShowData extends Component
{
    // data to be proceesed later

    public int $period_id = 0;
    public $periods;
    public $users;
    public $supervisors;
    public $supervisor_id = '';
    public $stats;
    public $items;

    // protected $queryString = ['period_id'];

    public function mount()
    {
        $this->periods = Period::where('id','>=',6)->get();

        $this->users = User::where('role_id', 1)
        ->when(auth()->user()->role_id == 1,function($q){
            $q->where('id',auth()->user()->id);
        })
        ->whereNotNull('supervisor_id')
        ->where('status', true)
        ->get()
        ->sortBy('supervisor_id');

        $this->supervisors = User::where('role_id',3)
        // ->where('status', true)
        ->get()
        ->sortBy('supervisor_id');
    }

    public function updatedPeriodId()
    {
        $this->items = Item::where('status',true)->get();
        $this->stats = Stat::where('period_id', $this->period_id)->get();
    }

    public function updatedSupervisorId()
    {
        $this->users = User::where('role_id', 1)
        ->when($this->supervisor_id,function($q){
            $q->where('supervisor_id',$this->supervisor_id);
        })
        ->whereNotNull('supervisor_id')
        ->where('status', true)
        ->get()
        ->sortBy('supervisor_id');
    }

    public function change($user_id, $item_id, $value)
    {

        if (!Gate::allows('change-data', $this->users->where('id', $user_id)->first())) {
            // abort(403);
        }

        $lock_period = Setting::where('name', 'lock_period')->first()->value;

        if($this->period_id <= $lock_period) {
            $this->dispatch('operation-failed');
            $this->addError('custom', 'Editing of this period has been locked.');
            return;
        }

        if($value === '') {
            $value = null;
        }

        if(is_null($value)) {
            Stat::where('user_id', $user_id)->where('period_id', $this->period_id)->where('item_id', $item_id)->delete();
        } else {
            Stat::updateOrCreate([
                'user_id' => $user_id,
                'period_id' => $this->period_id,
                'item_id' => $item_id,
            ], [
                'value' => $value,
            ]);
        }

        // $this->dispatch('operation-successful');
    }

    public function render()
    {
        return view('livewire.show-data');
    }
}
