<?php

namespace App\Livewire;

use App\Models\Parameter;
use App\Models\Period;
use App\Models\User;
use Livewire\Component;

class ShowDetailedStats extends Component
{
    
    public int $period_id;

    public $supervisor_id = 0;
    public $supervisors = [];

    public function mount($period_id)
    {
        $this->supervisors = User::where('role_id',3)
        ->where('status',true)
        ->get();

        $this->period_id = $period_id;
    }

    public function get_data()
    {
        $period = Period::find($this->period_id);

        return Parameter::whereIn('incoming_type', ['chat', 'call'])
        ->whereHas('records', function ($q) use ($period) {
            $q->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->when($this->supervisor_id, function ($query) {
                $query->whereRelation('user', 'supervisor_id', $this->supervisor_id);
            });
        })
        ->with(['records' => function ($q) use ($period) {
            $q->where('incoming_date', '>=', $period->from)
            ->where('incoming_date', '<', $period->to)
            ->when($this->supervisor_id, function ($query) {
                $query->whereRelation('user', 'supervisor_id', $this->supervisor_id);
            });
        }])
        ->get();

        // return Parameter::whereIn('incoming_type', ['chat', 'call'])
        // ->whereHas('records', function ($q) use ($period) {
        //     $q->where('incoming_date', '>=', $period->from)
        //     ->where('incoming_date', '<', $period->to)
        //     ->when($this->supervisor_id, function ($query) {
        //         $query->whereHas('user', function ($qUser) {
        //             $qUser->where('supervisor_id', $this->supervisor_id);
        //         });
        //     });
        // })
        // ->with(['records' => function ($q) use ($period) {
        //     $q->where('incoming_date', '>=', $period->from)
        //     ->where('incoming_date', '<', $period->to)
        //     ->when($this->supervisor_id, function ($query) {
        //         $query->whereHas('user', function ($qUser) {
        //             $qUser->where('supervisor_id', $this->supervisor_id);
        //         });
        //     });
        // }])
        // ->get();
    }

    public function get_super_data()
    {
        return Parameter::
        where('incoming_type','supervisor')
        ->whereHas('supervisor_periods', function ($q) {
            $q->where('period_id', $this->period_id)
            ->when($this->supervisor_id,function($query){
                $query->whereIn('user_id',User::where('supervisor_id',$this->supervisor_id)->where('role_id',1)->where('status',true)->get()->pluck('id')->toArray());
            });
        })
        ->with('supervisor_scores',function($q) {
            $q->where('period_id',$this->period_id)
            ->when($this->supervisor_id,function($query){
                $query->whereIn('user_id',User::where('supervisor_id',$this->supervisor_id)->where('role_id',1)->where('status',true)->get()->pluck('id')->toArray());
            });
        })
        ->get();
    }

    public function render()
    {    
        return view('livewire.show-detailed-stats',[
            'parameters'=>$this->get_data(),
            'supervisor_parameters'=>$this->get_super_data(),
        ]);
    }
}
