<?php

namespace App\Livewire;

use App\Models\Period;
use App\Models\Record;
use App\Models\User;
use Livewire\Component;

class SupervisorRecordsLeaderboard extends Component
{
    public $periods = [];
    public $period_id;
    public $platform = 'wallex';
    public $supervisors_score = [];
    public $users = [];
    public function mount()
    {
        $this->periods = Period::where('id','>',15)->get();

        $this->users = User::
        when(auth()->user()->role_id==3,function($q){
            $q->where('role_id',3);
        })
        ->when(auth()->user()->role_id<3,function($q){
            $q->whereIn('id',[
                67,
                80,
                4,
                9,
                41,
                43,
                42,
                14,
                13,
                44,
            ]);
        })
        ->when(auth()->user()->role_id>3,function($q){
            $q->where(function($query){

                $query->where('role_id',3)->orWhereIn('id',[
                    67,
                    80,
                    4,
                    9,
                    41,
                    43,
                    42,
                    14,
                    13,
                    44,
                ]);

            });
        })
        
        ->get();

    }
    public function updated()
    {
        if(!$this->period_id)
        {
            return;
        }

        $this->supervisors_score = [];
        foreach($this->users->where('platform',$this->platform) as $user)
        {
            $scores = [];
            $faq_scores = [];
            $email_scores = [];
            $ticket_scores = [];

            $records = Record::with(['qc_parameters'])
            ->where('incoming_date', '>=', $this->periods->where('id',$this->period_id)->first()->from)
            ->where('incoming_date', '<', $this->periods->where('id',$this->period_id)->first()->to)
            ->where('support_agent_id', $user->id)
            ->get();

            foreach($records->where('incoming_type', 'faq') as $record)
            {
                $sum = 0.0;
                $total = 0.0;

                foreach ($record->qc_parameters as $parameter) {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                $faq_scores[] = $total / $sum * 100;
                $scores[] = $total / $sum * 100;
            }

            foreach($records->where('incoming_type', 'email') as $record)
            {
                $sum = 0.0;
                $total = 0.0;

                foreach ($record->qc_parameters as $parameter)
                {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                $email_scores[] = $total / $sum * 100;
                $scores[] = $total / $sum * 100;
            }

            foreach($records->where('incoming_type', 'ticket') as $record)
            {
                $sum = 0.0;
                $total = 0.0;

                foreach ($record->qc_parameters as $parameter)
                {
                    if(is_null($parameter->pivot->value)) {
                        continue;
                    }
                    $sum += $parameter->score;
                    $total += $parameter->pivot->value;

                    $parameters_sum[$parameter->id] = isset($parameters_sum[$parameter->id]) ? $parameters_sum[$parameter->id] + $parameter->pivot->value : $parameter->pivot->value;
                    $parameters_count[$parameter->id] = isset($parameters_count[$parameter->id]) ? $parameters_count[$parameter->id] + 1 : 1;

                }
                $ticket_scores[] = $total / $sum * 100;
                $scores[] = $total / $sum * 100;
                }
                $this->supervisors_score[$user->id]['id'] = $user->id;
                $this->supervisors_score[$user->id]['name'] = $user->name;
                $this->supervisors_score[$user->id]['faq'] = count($faq_scores) ? round(array_sum($faq_scores) / count($faq_scores), 2) : '-';
                $this->supervisors_score[$user->id]['email'] = count($email_scores) ? round(array_sum($email_scores) / count($email_scores), 2) : '-';
                $this->supervisors_score[$user->id]['ticket'] = count($ticket_scores) ? round(array_sum($ticket_scores) / count($ticket_scores), 2) : '-';

                $this->supervisors_score[$user->id]['average'] = count($scores) ? round(array_sum($scores) / count($scores), 2) : '-';
        }

        $this->supervisors_score = collect($this->supervisors_score)->sortByDesc('average');
        
    }

    public function render()
    {
        return view('livewire.supervisor-records-leaderboard');
    }
}
