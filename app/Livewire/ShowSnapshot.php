<?php

namespace App\Livewire;
use App\Models\Period;
use App\Models\Snapshot;
use Livewire\Component;

class ShowSnapshot extends Component
{
    public $snapshot;
    public $period;
    
    public function mount($id)
    {
        $snapshot = Snapshot::find($id);
        $this->snapshot = json_decode($snapshot['data']);
        $this->snapshot->created_at = $snapshot['created_at'];
        $this->period = Period::find($snapshot->period_id);
    }
    
    public function render()
    {
        return view('livewire.show-snapshot');
    }
}
