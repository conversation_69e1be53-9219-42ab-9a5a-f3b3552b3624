<?php

namespace App\Livewire;

use App\Models\Record;
use Livewire\Component;
use Livewire\WithPagination;

class ChallengingRecords extends Component
{
    use WithPagination;

    public function render()
    {
        $records = Record::with('qc_parameters')
        ->where('challenging',true)
        ->where('incoming_type','chat')
        ->latest()
        ->paginate(10);
        
        return view('livewire.challenging-records',compact('records'));
    }


}
