<?php

namespace App\Livewire\Goftino;

use App\Models\Goftino_unassigned_chat;
use App\Models\User;
use Livewire\Component;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class ShowUnassignedChats extends Component
{
    protected $queryString = [
        'from_date',
        'to_date',
        'operator_id',
    ];

    public $from_date = '';
    public $to_date = '';
    public $operator_id = 0;
    public $agents = [];

    public function mount()
    {
        $this->agents = User::where('status',1)->get();
    }
    public function render()
    {
        $chats = Goftino_unassigned_chat::
        when($this->operator_id,function($q){
            $q->where('operator_id',$this->operator_id);
        })
        ->when($this->from_date,function($q){
            $q->where('created_at', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->orderByDesc('id')
        ->with(['data','operator'])
        ->paginate(10);

        return view('livewire.goftino.show-unassigned-chats',[
            'chats'=>$chats,
        ]);
    }
}
