<?php

namespace App\Livewire\Goftino;
use Livewire\WithPagination;
use App\Models\Goftino_chat;
use App\Models\User;
use Livewire\Component;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
class ShowChats extends Component
{
    use WithPagination;
    public $data = [];
    public $order = 'id_desc';
    public $rating = 0;

    public $platform = 0;
    public $shift_id = 0;

    public $from_date = '';
    public $to_date = '';
    
    protected $queryString = [
        'order',
        'rating',
        'user_id',
        'from_date',
        'to_date',
        'shift_id',
        'platform'
    ];

    public $user_id = 0;
    public $agents = [];


    public function mount()
    {
        $this->agents = User::where('status',1)->get();
    }

    public function render()
    {
        $chats = Goftino_chat::with(['user','data'])
            ->when($this->order,function($q){
                switch($this->order)
                {
                    case 'id_desc':
                        $q->orderByDesc('id');
                    break;
                    case 'id_asc':
                        $q->orderBy('id');
                    break;
                    case 'queue':
                        $q->orderByDesc('queue');
                    break;
                    case 'duration':
                        $q->orderByDesc('duration');
                    break;
                }
            })
            ->when($this->rating,function($q){
                $q->whereNotNull('rating')->where('rating',$this->rating);
            })
            ->when($this->platform,function($q){
                $q->whereHas('data',function($query){
                    $query->where('platform',$this->platform);
                });
            })
            ->when($this->user_id,function($q){
                $q->where('operator_id',$this->user_id);
            })
            ->when($this->shift_id,function($q){
                switch($this->shift_id)
                {
                    case 1:
                        $q->whereRaw('HOUR(created_at) BETWEEN 8 AND 15');
                    break;
                        
                    case 2:
                        $q->whereRaw('HOUR(created_at) BETWEEN 16 AND 23');
                    break;
                    
                    case 3:
                        $q->whereRaw('HOUR(created_at) BETWEEN 0 AND 7');
                    break;
                }
            })
            ->when($this->from_date,function($q){
                $q->where('created_at', '>=', Verta::parse($this->from_date)->toCarbon());
            })
            ->when($this->to_date,function($q){
                $q->where('created_at', '<', Verta::parse($this->to_date)->toCarbon());
            });
        
        $this->data = [
            'max_duration'=>$chats->clone()->max('duration'),
            'average_duration'=>round($chats->clone()->avg('duration'),1),

            'max_queue'=>$chats->clone()->max('queue'),
            'average_queue'=>round($chats->clone()->avg('queue'),1),

            'count'=>$chats->clone()->count(),
        ];
        
        return view('livewire.goftino.show-chats',[
            'chats'=>$chats->clone()->paginate(50),
        ]);
    }
}
