<?php

namespace App\Livewire\Goftino;

use App\Models\Goftino_assignment;
use App\Models\User;
use Livewire\Component;

class ShowAssignments extends Component
{
    public $users = [];
    
    public function mount()
    {
        // $goftino_assignments = Goftino_assignment::with(['user'])
        // ->where('created_at','>=',now()->startOfDay());
        
        $this->users = User::where('status',true)
        ->withCount('today_goftino_assignments')
        ->having('today_goftino_assignments_count','>',0)
        ->orderBy('today_goftino_assignments_count', 'desc')
        ->get();

    }

    public function render()
    {
        return view('livewire.goftino.show-assignments');
    }
}
