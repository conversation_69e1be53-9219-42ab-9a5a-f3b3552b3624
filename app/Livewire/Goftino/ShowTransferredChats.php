<?php

namespace App\Livewire\Goftino;

use App\Models\Goftino_transfer;
use App\Models\User;
use Livewire\Component;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

class ShowTransferredChats extends Component
{
    protected $queryString = [
        'from_date',
        'to_date',
        'from_operator_id',
    ];
    
    public $from_date = '';
    public $to_date = '';
    public $from_operator_id = 0;
    public $agents = [];

    public function mount()
    {
        $this->agents = User::where('status',1)->get();
    }
    
    public function render()
    {
        $chats = Goftino_transfer::
        when($this->from_operator_id,function($q){
            $q->where('from_operator_id',$this->from_operator_id);
        })
        ->when($this->from_date,function($q){
            $q->where('created_at', '>=', Verta::parse($this->from_date)->toCarbon());
        })
        ->when($this->to_date,function($q){
            $q->where('created_at', '<', Verta::parse($this->to_date)->toCarbon());
        })
        ->orderByDesc('id')
        ->with(['data','from_operator','to_operator'])
        ->paginate(10);

        return view('livewire.goftino.show-transferred-chats',[
            'chats'=>$chats,
        ]);
    }
}
