<?php

namespace App\Providers;

use App\Models\Record;
use App\Models\User;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\URL;
class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        
        if (request()->getHost()=='qc.wallex.support')
        {
            URL::forceScheme('https');
        }

        Http::withoutVerifying();
        
        Gate::define('delete-record', function (User $user) {
            return $user->role_id > 1;
        });

        Gate::define('update-record', function (User $user, Record $record) {
            return $user->id === $record->qc_agent_id or $user->role_id > 3 or in_array($user->id, [1,31]);
        });

        Gate::define('create-record-from-pool', function (User $user, $pool) {
            return $pool->qc_agent_id === auth()->user()->id or $user->role_id > 3;
        });

        Gate::define('create-record', function (User $user, $record) {
            return $record->qc_agent_id === auth()->user()->id or $user->role_id > 3;
        });

        Gate::define('update-score', function (User $user, $agent) {
            return $user->id === $agent->supervisor_id or $user->role_id > 3;
        });

        Gate::define('delete-setting', function (User $user) {
            return $user->role_id > 2;
        });

        Gate::define('add-setting', function (User $user) {
            return $user->role_id > 2;
        });

        Gate::define('assign-pool', function (User $user) {
            return $user->role_id > 2 or in_array($user->id, [31,33,66,8]);
        });

        Gate::define('delete-pool', function (User $user) {
            return $user->role_id > 2 or in_array($user->id, [31,33,66,8]);
        });

        Gate::define('update-parameter', function (User $user) {
            return $user->role_id == 6 or in_array(auth()->user()->id,[1,37]) ;
        });

        Gate::define('delete-parameter', function (User $user) {
            return $user->role_id == 6;
        });

        Gate::define('change-status', function (User $user) {
            return $user->role_id > 1;
        });

        Gate::define('verify-exception', function (User $user) {
            return $user->role_id > 2;
        });

        Gate::define('delete-exception', function (User $user) {
            return $user->role_id > 3;
        });

        Gate::define('change-data', function (User $user, $agent) {
            return $user->id === $agent->supervisor_id or $user->role_id > 3;
        });
        

        Gate::define('change-qc-data', function (User $user) {
            return $user->role_id>=4;
        });

        Gate::define('change-supervisor-data', function (User $user) {
            return $user->role_id>=4;
        });

        Gate::define('change-role', function (User $user) {
            return in_array($user->id,[1,37,2]);
        });

        Gate::define('auto-calc', function (User $user) {
            return in_array($user->id,[1,37]);
        });
        
        Gate::define('extract-data', function (User $user) {
            return in_array($user->id,[1,37]);
        });

        Gate::define('upload-qc', function (User $user) {
            return $user->role_id > 2 or in_array($user->id, [31,33,66,8]);
        });

        Gate::define('delete-item', function (User $user) {
            return $user->id === 1;
        });

        Gate::define('add-item', function (User $user) {
            return $user->id === 1;
        });

        Gate::define('change-item', function (User $user) {
            return $user->id === 1;
        });

        Gate::define('delete-snapshot', function (User $user) {
            return $user->id === 1;
        });
        Gate::define('update-snapshot', function (User $user) {
            return $user->role_id >= 4;
        });

        Gate::define('delete-comment', function (User $user,$comment) {
            return 
            $user->id ===1 or
            $user->role_id > 3;
        });

        Gate::define('delete-ticket', function (User $user) {
            return $user->id === 1;
        });

        Gate::define('change-ticket', function (User $user,$ticket) {
            
            return $user->id === 1 or $user->id === $ticket->user->supervisor_id;
        });

        Gate::define('create-announcement', function (User $user) {
            return auth()->user()->role_id > 1 or in_array(auth()->user()->id,[13,14]);
        });

        Gate::define('delete-announcement', function (User $user,$announcement) {
            return $user->id === 1 or $user->id === $announcement->user_id;
        });

        Gate::define('delete-category', function (User $user) {
            return $user->id === 1;
        });

        Gate::define('delete-weighted-subject', function (User $user) {
            return $user->role_id > 2;
        });

        Gate::define('sync-sheet', function (User $user) {
            return $user->role_id >= 3 or in_array($user->id,[19,29]);
        });

    }
}
