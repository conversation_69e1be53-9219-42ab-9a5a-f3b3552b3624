<?php
namespace App\Tools;

use App\Models\Livechat;

class FRT
{
    // First Response Time
    public function __invoke($identity,$email)
    {
        $livechat = Livechat::where('thread_id',$identity)->where('author_id',$email)->first();
        if($livechat and !is_null($livechat->FRT))
        {
            return $livechat->FRT > 60;
        }
        else
        {
            return null;
        }
    }
}