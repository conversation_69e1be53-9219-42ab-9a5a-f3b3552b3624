<?php
namespace App\Tools;

use App\Models\Livechat;

class Chat_management
{
    public function __invoke($identity,$email)
    {
        $livechat = Livechat::where('thread_id',$identity)->where('author_id',$email)?->first();

        if($livechat and !is_null($livechat?->chat_management) and $livechat?->chat_management==false) {
            return false;
        } else {
            return true;
        }

    }
}