<?php

namespace App\Social;

use Illuminate\Support\Facades\Http;

class Slack
{
    public static function qc_alert($text)
    {
        // red lines
        
        // $response = Http::post('*********************************************************************************',
        // [
        //     'text' => $text,
        // ]);

        $channel_id = 'C0765UF9S6P';
        $response = Http::withToken('*********************************************************')
        ->post('https://slack.com/api/chat.postMessage',
        [
            'channel' => $channel_id,
            'text' => $text,
        ]);

        $data = $response->json();
        $thread_ts = $data['ts'];
        return $thread_ts;
    }

    public static function qc_alert_thread($text,$thread_ts)
    {
        // red lines thread
        $channel_id = 'C0765UF9S6P';
        Http::withToken('*********************************************************')
        ->withHeader('Content-Type','application/json; charset=utf-8')
        ->post('https://slack.com/api/chat.postMessage',
        [
            'channel' => $channel_id,
            'text' => $text,
            'thread_ts'=>$thread_ts,
        ]);
    }

    public static function qc_notification($text)
    {
        // sensitive words
        Http::post('*********************************************************************************',
        [
            'text' => $text,
        ]);
    }

    // daily coach (private message)
    public static function pv($text,$channel)
    {
        Http::
            withToken('*********************************************************')
            ->post('https://slack.com/api/chat.postMessage',
        [
            'channel' => $channel,
            'text' => $text,
        ]);
    }

    // social 
    public static function qc_social($text)
    {
        // sensitive words
        Http::post('*********************************************************************************',
        [
            'text' => $text,
        ]);
    }

    // okr
    public static function okr($text,$platform='wallex')
    {
        dispatch(function () use ($text,$platform) {

            if($platform=='wallex'){
                $url = "*********************************************************************************";
            } else {
                // wallgold
                $url = "*********************************************************************************";
            }

            Http::post($url,
            [
                'text' => $text,
            ]);
        })->afterResponse();
    }

    public static function services_monitoring($text)
    {
        Http::post('*********************************************************************************',
        [
            'text' => $text,
        ]);
    }

    // operation
    public static function operation($text,$platform='wallex')
    {
        dispatch(function () use ($text,$platform) {

            if($platform=='wallex'){
                $url = "*********************************************************************************";
                Http::post($url,
                [
                    'text' => $text,
                ]);
            } else {
                // wallgold
                $url = "*********************************************************************************";
            }
            
        })->afterResponse();
    }

    // cx-alerts
    public static function cx_unit($text)
    {
        $url = "*********************************************************************************";
            Http::post($url,
            [
                'text' => $text,
            ]);
    }
    

    public static function supervisor_action($text)
    {
        dispatch(function () use ($text) {
            Http::post('*********************************************************************************',
        [
            'text' => $text,
        ]);
        })->afterResponse();
    }

    // otc market health
    public static function otc_alert($text)
    {
        dispatch(function () use ($text) {
            Http::post('*********************************************************************************',
        [
            'text' => $text,
        ]);
        })->afterResponse();
    }

    public static function otc_spot_alert($text)
    {
        dispatch(function () use ($text) {
            Http::post('*********************************************************************************',
        [
            'text' => $text,
        ]);
        })->afterResponse();
    }

    public static function admin($text)
    {
        
       self::pv($text,'U02SRHL4A6P');
    }

    public static function pv_thread($text,$thread_ts)
    {
        Http::
            withToken('*********************************************************')
            ->post('https://slack.com/api/chat.postMessage',
        [
            'channel' => 'U02SRHL4A6P',
            'text' => $text,
            'thread_ts'=>$thread_ts,
        ]);
    }
    
    public static function ticket_alert($text)
    {
        $channel_id = "C08DURFK4BT";
        Http::withToken('*********************************************************')
        ->post('https://slack.com/api/chat.postMessage',
        [
            'channel' => $channel_id,
            'text' => $text,
        ]);
    }

    public static function announcement($text,$channel,$preview=true)
    {
            return Http::
            // withOptions([
            //     'verify' => false,
            //     'curl' => [
            //         CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2, // Force TLS 1.2
            //     ],
            // ])
            withToken('*********************************************************')
            ->post('https://slack.com/api/chat.postMessage',
            [
                'channel'=> $channel,
                'text' => $text,
                'unfurl_links' => $preview,
                'unfurl_media' => $preview,
            ])->object()->ts;
    }

    public static function thread_announcement($text,$channel,$thread_ts,$reply_broadcast=true)
    {
            return Http::
            withToken('*********************************************************')
            ->post('https://slack.com/api/chat.postMessage',
            [
                'channel'=> $channel,
                'text' => $text,
                'thread_ts'=>$thread_ts,
                'reply_broadcast'=>$reply_broadcast,
            ])->object();
    }

    public static function announcement_update($text,$announcement)
    {
        return Http::
            withToken('*********************************************************')
            ->post('https://slack.com/api/chat.update',
            [
                'channel'=> $announcement->channel,
                'ts'=>$announcement->thread_ts,
                'text' => $text,
            ])->object();
    }

    public static function announcement_attachments($files,$ts,$channel='C0889QPAMPD')
    {
        $file_id_list = [];
        
        foreach($files as $file)
        {
            $size = $file->getSize();

            $response = Http::
            withToken('*********************************************************')
            ->get('https://slack.com/api/files.getUploadURLExternal',[
                'filename'=>$file->getClientOriginalName(),
                'length'=>$size,
            ])->object();
        
            $upload_url = $response->upload_url;
            $file_id = $response->file_id;
            
            Http::
            withToken('*********************************************************')
            ->attach($file->getClientOriginalName(),file_get_contents($file->getRealPath()),$file->getClientOriginalName())
            ->post($upload_url);
                
            Http::
            withToken('*********************************************************')
            ->post('https://slack.com/api/files.completeUploadExternal',[
                'files'=>[
                    [
                        'id'=>$file_id,
                    ],
                ],
                'thread_ts'=>$ts,
                'channel_id'=>$channel,
            ]);

            array_push($file_id_list,$file_id);
        }

        return $file_id_list;
    }

    public static function announcement_delete($announcement)
    {
        self::delete_thread_items($announcement);

        return Http::
            withToken('*********************************************************')
            ->post('https://slack.com/api/chat.delete',
            [
                'channel'=> $announcement->channel,
                'ts'=>$announcement->thread_ts,
            ])->object();
    }

    public static function delete_thread_items($announcement)
    {
        $replies = Http::
        withToken('*********************************************************')
        ->get('https://slack.com/api/conversations.replies',
        [
            'channel'=> $announcement->channel,
            'ts'=>$announcement->thread_ts,
        ])->object();

        $messages = $replies?->messages ?? [];
        foreach($messages as $message)
        {
            Http::
            withToken('*********************************************************')
            ->post('https://slack.com/api/chat.delete',
            [
                'channel'=> $announcement->channel,
                'ts'=>$message->ts,
            ])->object();
        }

    }

    public static function get_reacts($announcement)
    {
        return Http::
            withToken('*********************************************************')
            ->get('https://slack.com/api/reactions.get',
            [
                'channel'=> $announcement->channel,
                'timestamp'=>$announcement->thread_ts,
            ])->object();
    }
}