<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Goftino_assignment extends Model
{
    protected $guarded = [];
    protected $table = 'goftino_assignments';
    use HasFactory;

    public function data()
    {
        return $this->belongsTo(Goftino_data::class, 'chat_id', 'chat_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class,'operator_id','goftino_operator_id');
    }
}
