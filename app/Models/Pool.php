<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Pool extends Model
{
    use HasFactory;
    protected $table = 'pool';
    protected $guarded = [];
    
    public function support_agent()
    {
        return $this->belongsTo(User::class,'support_agent_id','id');
    }

    public function qc_agent()
    {
        return $this->belongsTo(User::class,'qc_agent_id','id');
    }
}
