<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Goftino_unassigned_chat extends Model
{
    protected $table = 'goftino_unassigned_chats';
    protected $guarded = [];
    use HasFactory;

    public function data()
    {
        return $this->belongsTo(Goftino_data::class, 'chat_id', 'chat_id');
    }

    public function operator()
    {
        return $this->belongsTo(User::class, 'operator_id', 'goftino_operator_id');
    }
}
