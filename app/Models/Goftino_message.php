<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Goftino_message extends Model
{
    protected $table = 'goftino_messages';
    protected $guarded = [];
    use HasFactory;

    public function data()
    {
        return $this->belongsTo(Goftino_data::class, 'chat_id', 'chat_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'sender_id', 'goftino_operator_id');
    }
}
