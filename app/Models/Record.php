<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Record extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function parameters()
    {
        return $this->belongsToMany(Parameter::class)->withPivot('value')->withTimestamps();
    }

    public function qc_parameters()
    {
        return $this->belongsToMany(Parameter::class)->whereIn('incoming_type',['chat','call','kyc','outgoing','faq','email','ticket'])->withPivot('value')->withTimestamps();
    }

    public function red_lines()
    {
        return $this->belongsToMany(Parameter::class)->whereIn('incoming_type',['red_chat','red_call','red_outgoing'])->withPivot('value')->withTimestamps();
    }

    public function user()
    {
        return $this->belongsTo(User::class,'support_agent_id','id');
    }

    public function qc_agent()
    {
        return $this->belongsTo(User::class,'qc_agent_id','id');
    }

    public function object()
    {
        return $this->hasOne(Objection::class);
    }
}
