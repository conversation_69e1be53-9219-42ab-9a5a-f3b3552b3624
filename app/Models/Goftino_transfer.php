<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Goftino_transfer extends Model
{
    protected $table = 'goftino_transfers';
    protected $guarded = [];
    use HasFactory;

    public function data()
    {
        return $this->belongsTo(Goftino_data::class, 'chat_id', 'chat_id');
    }

    public function from_operator()
    {
        return $this->belongsTo(User::class, 'from_operator_id', 'goftino_operator_id');
    }

    public function to_operator()
    {
        return $this->belongsTo(User::class, 'to_operator_id', 'goftino_operator_id');
    }
}
