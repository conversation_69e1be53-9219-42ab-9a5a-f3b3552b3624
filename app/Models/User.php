<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function supervisor_parameters($period_id=false) {
        return $this->belongsToMany(Parameter::class, 'parameter_period_user')
                    ->when($period_id,function($q) use ($period_id) {
                        $q->wherePivot('period_id',$period_id);
                    })
                    ->withPivot('period_id', 'value','note')
                    ->withTimestamps();
    }

    public function records()
    {
        return $this->hasMany(Record::class,'support_agent_id','id');
    }

    public function qc_records()
    {
        return $this->hasMany(Record::class,'qc_agent_id','id');
    }

    public function today_goftino_assignments()
    {
        return $this->hasMany(Goftino_assignment::class, 'operator_id','goftino_operator_id')
                ->where('created_at', '>=', now()->startOfDay());
    }
}

