<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    protected $guarded = [];
    use HasFactory;

    public function user()
    {
        return $this->belongsTo(User::class,'support_agent_id','id');
    }

    public function supervisor()
    {
        return $this->belongsTo(User::class,'updated_by','id');
    }
}
