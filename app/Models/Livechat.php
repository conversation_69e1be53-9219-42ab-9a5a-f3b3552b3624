<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Livechat extends Model
{
    use HasFactory;
    protected $guarded = [];

    public function missed_chat()
    {
        return $this->hasOne(Missed_chat::class);
    }

    public function inactive_transfer()
    {
        return $this->hasOne(Inactive_transfer::class);
    }

    public function lost_connection()
    {
        return $this->hasOne(Lost_connection::class);
    }

    public function queue_abandonment()
    {
        return $this->hasOne(Queue_abandonment::class);
    }

    public function signed_out_transfer()
    {
        return $this->hasOne(Signed_out_transfer::class);
    }

    public function chat_transfer()
    {
        return $this->hasOne(Chat_transfer::class);
    }

    public function take_over()
    {
        return $this->hasOne(Take_over::class);
    }

    public function inactive_archived()
    {
        return $this->hasOne(Inactive_archive::class);
    }

}
