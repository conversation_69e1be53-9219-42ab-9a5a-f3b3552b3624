<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Parameter extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function records()
    {
        return $this->belongsToMany(Record::class)->withPivot('value')->withTimestamps();
    }

    public function supervisor_periods() {
        return $this->belongsToMany(Period::class, 'parameter_period_user')->withPivot('user_id');
    }

    public function supervisor_scores()
    {
        return $this->hasMany(Score::class);
    }
}
