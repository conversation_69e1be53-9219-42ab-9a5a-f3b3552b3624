<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Goftino_chat extends Model
{
    use HasFactory;

    protected $table = 'goftino_chats';
    protected $guarded = [];


    public function data()
    {
        return $this->belongsTo(Goftino_data::class, 'chat_id', 'chat_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class,'operator_id','goftino_operator_id');
    }

    // public function messages()
    // {
    //     return $this->hasMany(Goftino_message::class,'chat_id','chat_id');
    // }
}
