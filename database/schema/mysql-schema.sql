/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `announcements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcements` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `body` text COLLATE utf8mb4_unicode_ci,
  `slack` tinyint(1) NOT NULL DEFAULT '1',
  `mention` tinyint(1) NOT NULL DEFAULT '0',
  `channel` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tree` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `thread_ts` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category_id` bigint unsigned DEFAULT NULL,
  `files` json DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `url` text COLLATE utf8mb4_unicode_ci,
  `event` tinyint(1) DEFAULT NULL,
  `reacted_by` json DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `announcements_user_id_foreign` (`user_id`),
  KEY `announcements_category_id_foreign` (`category_id`),
  KEY `announcements_updated_by_foreign` (`updated_by`),
  CONSTRAINT `announcements_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  CONSTRAINT `announcements_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`),
  CONSTRAINT `announcements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `campaigns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `campaigns` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `from` date NOT NULL,
  `to` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `campaigns_first`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `campaigns_first` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `admin_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `called_at` datetime DEFAULT NULL,
  `call_later_at` datetime DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `call_status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `call_result` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `familiar` tinyint(1) DEFAULT NULL,
  `used` tinyint(1) DEFAULT NULL,
  `experience` tinyint(1) DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `campaigns_first_phone_number_unique` (`phone_number`),
  UNIQUE KEY `campaigns_first_admin_id_unique` (`admin_id`),
  KEY `campaigns_first_user_id_foreign` (`user_id`),
  CONSTRAINT `campaigns_first_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `option` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `chat_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chat_transfers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `initiator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `targets` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `chat_transfers_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `chat_transfers_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `object_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `comments_object_id_foreign` (`object_id`),
  KEY `comments_user_id_foreign` (`user_id`),
  CONSTRAINT `comments_object_id_foreign` FOREIGN KEY (`object_id`) REFERENCES `objects` (`id`),
  CONSTRAINT `comments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `exam_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exam_stats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `value` double(8,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `exam_stats_user_id_foreign` (`user_id`),
  KEY `exam_stats_period_id_foreign` (`period_id`),
  CONSTRAINT `exam_stats_period_id_foreign` FOREIGN KEY (`period_id`) REFERENCES `periods` (`id`),
  CONSTRAINT `exam_stats_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `exceptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exceptions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `verified` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `exceptions_user_id_foreign` (`user_id`),
  KEY `exceptions_updated_by_foreign` (`updated_by`),
  CONSTRAINT `exceptions_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`),
  CONSTRAINT `exceptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `inactive_archives`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inactive_archives` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `inactive_archives_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `inactive_archives_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `inactive_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inactive_transfers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `agent_added` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_removed` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `inactive_transfers_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `inactive_transfers_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `left_chats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `left_chats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `left_chats_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `left_chats_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `livechats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `livechats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'wallex',
  `thread_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `chat_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `FRT` int DEFAULT NULL,
  `author_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `customer_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `admin_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_message_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `chat_duration` int DEFAULT NULL,
  `queues_duration` int DEFAULT NULL,
  `agents_count` int DEFAULT NULL,
  `transfers_count` int DEFAULT NULL,
  `agent_message_count` int DEFAULT NULL,
  `customer_message_count` int DEFAULT NULL,
  `called_by_name` tinyint(1) DEFAULT NULL,
  `survey` tinyint(1) DEFAULT NULL,
  `start_scenario` tinyint(1) DEFAULT NULL,
  `chat_management` tinyint(1) DEFAULT NULL,
  `offline` tinyint(1) DEFAULT NULL,
  `ai` tinyint(1) DEFAULT NULL,
  `ticket` tinyint(1) DEFAULT NULL,
  `chatbot` tinyint(1) DEFAULT NULL,
  `file` tinyint(1) DEFAULT NULL,
  `comment` tinyint(1) DEFAULT NULL,
  `chat_rated` tinyint(1) DEFAULT NULL,
  `started_by_agent` tinyint(1) DEFAULT NULL,
  `archived_method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data` json DEFAULT NULL,
  `flagged` tinyint(1) DEFAULT NULL,
  `started_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `livechats_thread_id_chat_id_unique` (`thread_id`,`chat_id`),
  KEY `livechats_author_id_foreign` (`author_id`),
  CONSTRAINT `livechats_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `lost_connections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lost_connections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `agent_added` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agent_removed` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lost_connections_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `lost_connections_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `meetings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `meetings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `missed_chats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `missed_chats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `missed_chats_livechat_id_foreign` (`livechat_id`),
  KEY `missed_chats_user_id_foreign` (`user_id`),
  CONSTRAINT `missed_chats_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`),
  CONSTRAINT `missed_chats_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `objects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `objects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `record_id` bigint unsigned NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `status_change_count` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `objects_record_id_unique` (`record_id`),
  CONSTRAINT `objects_record_id_foreign` FOREIGN KEY (`record_id`) REFERENCES `records` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `okr_periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `okr_periods` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `from` date NOT NULL,
  `to` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `parameter_period_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parameter_period_user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `parameter_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `value` float DEFAULT NULL,
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `parameter_period_user_parameter_id_period_id_user_id_unique` (`parameter_id`,`period_id`,`user_id`),
  KEY `parameter_period_user_period_id_foreign` (`period_id`),
  KEY `parameter_period_user_user_id_foreign` (`user_id`),
  CONSTRAINT `parameter_period_user_parameter_id_foreign` FOREIGN KEY (`parameter_id`) REFERENCES `parameters` (`id`),
  CONSTRAINT `parameter_period_user_period_id_foreign` FOREIGN KEY (`period_id`) REFERENCES `periods` (`id`),
  CONSTRAINT `parameter_period_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `parameter_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parameter_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `parameter_id` bigint unsigned NOT NULL,
  `record_id` bigint unsigned NOT NULL,
  `value` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `parameter_record_parameter_id_foreign` (`parameter_id`),
  KEY `parameter_record_record_id_foreign` (`record_id`),
  CONSTRAINT `parameter_record_parameter_id_foreign` FOREIGN KEY (`parameter_id`) REFERENCES `parameters` (`id`),
  CONSTRAINT `parameter_record_record_id_foreign` FOREIGN KEY (`record_id`) REFERENCES `records` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `parameters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parameters` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `incoming_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'chat',
  `score` int NOT NULL DEFAULT '0',
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `periods` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `from` date NOT NULL,
  `to` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `pool`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pool` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'wallex',
  `identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `support_agent_id` bigint unsigned DEFAULT NULL,
  `qc_agent_id` bigint unsigned DEFAULT NULL,
  `incoming_date` datetime DEFAULT NULL,
  `incoming_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pool_identity_unique` (`identity`),
  KEY `pool_support_agent_id_foreign` (`support_agent_id`),
  KEY `pool_qc_agent_id_foreign` (`qc_agent_id`),
  CONSTRAINT `pool_qc_agent_id_foreign` FOREIGN KEY (`qc_agent_id`) REFERENCES `users` (`id`),
  CONSTRAINT `pool_support_agent_id_foreign` FOREIGN KEY (`support_agent_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `qc_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qc_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `qc_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qc_stats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `qc_item_id` bigint unsigned NOT NULL,
  `value` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `qc_stats_period_id_user_id_qc_item_id_unique` (`period_id`,`user_id`,`qc_item_id`),
  KEY `qc_stats_user_id_foreign` (`user_id`),
  KEY `qc_stats_qc_item_id_foreign` (`qc_item_id`),
  CONSTRAINT `qc_stats_period_id_foreign` FOREIGN KEY (`period_id`) REFERENCES `okr_periods` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `qc_stats_qc_item_id_foreign` FOREIGN KEY (`qc_item_id`) REFERENCES `qc_items` (`id`),
  CONSTRAINT `qc_stats_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `queue_abandonments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `queue_abandonments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `queue_abandonments_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `queue_abandonments_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `crm_identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `incoming_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `incoming_date` timestamp NOT NULL,
  `incoming_subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `recorded_subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `daily_coach` text COLLATE utf8mb4_unicode_ci,
  `red` tinyint(1) NOT NULL DEFAULT '0',
  `thread_ts` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `draft` tinyint(1) NOT NULL DEFAULT '0',
  `challenging` tinyint(1) NOT NULL DEFAULT '0',
  `ignore` tinyint(1) NOT NULL DEFAULT '0',
  `coefficient` double(8,2) NOT NULL DEFAULT '1.00',
  `support_agent_id` bigint unsigned NOT NULL,
  `qc_agent_id` bigint unsigned NOT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `trace_time` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `records_identity_unique` (`identity`),
  KEY `records_support_agent_id_foreign` (`support_agent_id`),
  KEY `records_qc_agent_id_foreign` (`qc_agent_id`),
  KEY `records_updated_by_foreign` (`updated_by`),
  CONSTRAINT `records_qc_agent_id_foreign` FOREIGN KEY (`qc_agent_id`) REFERENCES `users` (`id`),
  CONSTRAINT `records_support_agent_id_foreign` FOREIGN KEY (`support_agent_id`) REFERENCES `users` (`id`),
  CONSTRAINT `records_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sensitive_words`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sensitive_words` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'includes',
  `alert` tinyint(1) NOT NULL DEFAULT '0',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'critical',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `signed_out_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `signed_out_transfers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `agent_added` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_removed` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `signed_out_transfers_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `signed_out_transfers_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `slack_channels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `slack_channels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `channel_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lastest_crawled_message` double DEFAULT NULL,
  `oldest_crawled_message` double DEFAULT NULL,
  `crawling_direction` enum('up','down') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'up',
  `enabled` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `slack_messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `slack_messages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `channel_id` bigint unsigned NOT NULL,
  `user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ts` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `files` tinyint(1) NOT NULL DEFAULT '0',
  `text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reply_count` int NOT NULL DEFAULT '0',
  `thread_crawled_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slack_messages_ts_unique` (`ts`),
  KEY `slack_messages_channel_id_foreign` (`channel_id`),
  CONSTRAINT `slack_messages_channel_id_foreign` FOREIGN KEY (`channel_id`) REFERENCES `slack_channels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `slack_replies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `slack_replies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `message_id` bigint unsigned NOT NULL,
  `user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ts` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `files` tinyint(1) NOT NULL DEFAULT '0',
  `text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slack_replies_ts_unique` (`ts`),
  KEY `slack_replies_message_id_foreign` (`message_id`),
  CONSTRAINT `slack_replies_message_id_foreign` FOREIGN KEY (`message_id`) REFERENCES `slack_messages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `snapshots`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `snapshots` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `data` json NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `snapshots_period_id_foreign` (`period_id`),
  CONSTRAINT `snapshots_period_id_foreign` FOREIGN KEY (`period_id`) REFERENCES `periods` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `item_id` bigint unsigned NOT NULL,
  `value` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stats_period_id_user_id_item_id_unique` (`period_id`,`user_id`,`item_id`),
  KEY `stats_user_id_foreign` (`user_id`),
  KEY `stats_item_id_foreign` (`item_id`),
  CONSTRAINT `stats_item_id_foreign` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`),
  CONSTRAINT `stats_period_id_foreign` FOREIGN KEY (`period_id`) REFERENCES `periods` (`id`),
  CONSTRAINT `stats_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `supervisor_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supervisor_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `supervisor_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supervisor_stats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `supervisor_item_id` bigint unsigned NOT NULL,
  `value` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `supervisor_stats_period_id_user_id_supervisor_item_id_unique` (`period_id`,`user_id`,`supervisor_item_id`),
  KEY `supervisor_stats_user_id_foreign` (`user_id`),
  KEY `supervisor_stats_supervisor_item_id_foreign` (`supervisor_item_id`),
  CONSTRAINT `supervisor_stats_period_id_foreign` FOREIGN KEY (`period_id`) REFERENCES `okr_periods` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `supervisor_stats_supervisor_item_id_foreign` FOREIGN KEY (`supervisor_item_id`) REFERENCES `supervisor_items` (`id`),
  CONSTRAINT `supervisor_stats_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `take_overs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `take_overs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `livechat_id` bigint unsigned NOT NULL,
  `initiator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `targets` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `take_overs_livechat_id_foreign` (`livechat_id`),
  CONSTRAINT `take_overs_livechat_id_foreign` FOREIGN KEY (`livechat_id`) REFERENCES `livechats` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `family_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT '1',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tickets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `crm_id` bigint unsigned NOT NULL,
  `support_agent_id` bigint unsigned NOT NULL,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `creation_date` datetime NOT NULL,
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tickets_crm_id_support_agent_id_subject_unique` (`crm_id`,`support_agent_id`,`subject`),
  KEY `tickets_support_agent_id_foreign` (`support_agent_id`),
  KEY `tickets_updated_by_foreign` (`updated_by`),
  CONSTRAINT `tickets_support_agent_id_foreign` FOREIGN KEY (`support_agent_id`) REFERENCES `users` (`id`),
  CONSTRAINT `tickets_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'wallex',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role_id` int NOT NULL DEFAULT '1',
  `supervisor_id` bigint unsigned DEFAULT NULL,
  `weight` int NOT NULL DEFAULT '0',
  `trace_chat` int NOT NULL DEFAULT '0',
  `trace_call` int NOT NULL DEFAULT '0',
  `trace_kyc` int DEFAULT '0',
  `trace_outgoing` int NOT NULL DEFAULT '0',
  `eyebeam_id` int DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `personal_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `slack_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shift` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `working_days` int NOT NULL DEFAULT '26',
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `weighted_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `weighted_subjects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `weight` double(8,2) NOT NULL DEFAULT '1.00',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `wrong_deposits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wrong_deposits` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `coin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `hash` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `screenshot` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `form_filled_at` datetime NOT NULL,
  `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agent_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `phone_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `national_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `wrong_deposits_updated_by_foreign` (`updated_by`),
  CONSTRAINT `wrong_deposits_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2014_10_12_100000_create_password_reset_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2018_08_08_100000_create_telescope_entries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2024_05_01_204946_create_records_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2024_05_01_205014_create_parameters_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2024_05_01_205517_create_parameter_record_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2024_05_04_015917_create_pool_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2024_05_05_105823_create_objects_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2024_05_05_105842_create_comments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2024_05_22_080233_create_periods_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2024_05_22_092906_create_parameter_period_user_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2024_05_22_125308_create_settings_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2024_05_26_220248_add_new_column_to_parameter_period_user_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2024_05_31_160705_add_new_column_to_users_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2024_06_01_105954_add_new_column_to_parameters_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2024_06_12_090752_add_new_column_to_records_table',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2024_06_12_091059_change_columns_in_records_table',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2024_06_12_112707_add_new_column_to_users_table',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2024_06_19_140532_add_new_column_to_users_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2024_06_23_112832_create_stats_table',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2024_06_23_115805_create_exceptions_table',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2024_06_24_152916_add_new_column_to_records_table',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2024_06_26_092219_add_new_columns_to_users_table',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2024_07_14_093614_change_pool_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2024_07_14_101542_add_column_to_settings_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2024_07_21_084949_create_items_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2024_07_21_085406_change_stats_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2024_07_27_101128_create_tickets_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2024_07_28_083222_create_snapshots_table',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2024_08_02_161050_add_column_to_snapshots_table',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2024_08_04_105337_create_cache_table',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2024_08_05_131359_create_livechats_table',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2024_08_06_152259_add_columns_to_livechats_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2024_08_07_091754_create_lost_connections_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2024_08_07_094424_create_chat_transfers_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2024_08_07_100200_create_missed_chats_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2024_08_07_100232_create_queue_abandonments_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2024_08_07_102323_create_sensitive_words_table',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2024_08_07_111316_create_inactive_transfers_table',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2024_08_07_112405_create_left_chats_table',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2024_08_07_113839_create_take_overs_table',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2024_08_07_115600_create_meetings_table',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2024_08_07_120517_create_signed_out_transfers_table',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (102,'2024_08_07_120928_add_columns_to_take_overs_table',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (103,'2024_08_07_123225_add_columns_to_sensitive_words_table',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (104,'2024_08_10_090906_change_colmmn_in_transfer_chats_table',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (105,'2024_08_10_090958_change_colmmn_in_inactive_transfers_table',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (106,'2024_08_10_091101_change_colmmn_in_lost_connections',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2024_08_10_091215_change_colmmn_in_signed_out_transfers',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (108,'2024_08_10_091237_change_colmmn_in_take_overs_table',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (109,'2024_08_10_091307_change_colmmn_in_left_chats_table',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (110,'2024_08_10_113336_create_notifications_table',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (111,'2024_08_12_085713_add_new_column_to_tickets_table',26);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (112,'2024_08_12_103320_add_columns_to_livechats_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (113,'2024_08_13_133353_add_column_to_livechats',28);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (114,'2024_08_14_114656_add_column_to_livechats_table',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (115,'2024_08_17_110904_add_new_column_to_sensitive_words',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (116,'2024_08_17_153110_add_new_column_to_livechats',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (117,'2024_08_17_163629_create_inactive_archives_table',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (118,'2024_08_18_151926_change_stats_table',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (119,'2024_08_26_121329_add_new_column_to_snapshots_table',34);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2024_08_26_123256_add_column_to_livechats_table',34);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2024_08_27_085758_add_new_column_to_livechats',35);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2024_08_27_114251_add_column_to_records_table',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2024_08_31_112226_create_qc_items_table',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2024_08_31_112242_create_supervisor_items_table',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2024_08_31_112309_create_qc_stats_table',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2024_08_31_112316_create_supervisor_stats_table',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2024_09_09_092343_add_new_column_to_users_table',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2024_09_15_133540_create_weighted_subjects_table',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2024_09_15_154443_add_column_to_weighted_subjects_table',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2024_09_16_124309_add_column_to_records_table',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2024_09_18_114448_add_column_to_users_table',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2024_09_22_153528_create_okr_periods_table',43);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2024_09_25_090049_create_wrong_deposits_table',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (134,'2024_10_05_083220_add_column_to_tickets_table',45);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (135,'2024_10_14_082243_create_exam_stats_table',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (136,'2024_10_14_091034_create_campaigns_table',47);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (137,'2024_10_21_124618_add_column_to_users_table',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (138,'2024_10_23_084432_add_column_to_sensitive_words_table',49);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (139,'2024_11_06_111852_add_column_to_records_table',50);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (140,'2024_11_09_101215_create_campaigns_first_table',51);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (141,'2024_11_25_094719_add_column_to_records_table',52);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (142,'2024_12_15_112208_add_column_to_livechats_table',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (143,'2024_12_15_112347_add_column_to_pool_table',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (144,'2024_12_15_112355_add_column_to_user_table',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (145,'2024_12_16_132545_create_jobs_table',54);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (146,'2024_12_18_092724_add_column_to_livechats_table',55);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (147,'2025_01_07_134544_add_columns_to_livechats_table',56);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (148,'2025_01_11_123307_create_categories_table',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (149,'2025_01_12_102702_make_announcements_table',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (150,'2025_01_18_113547_add_column_to_livechats_table',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (151,'2025_01_19_145933_create_slack_channels_table',58);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (152,'2025_01_19_152447_create_slack_messages_table',58);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (153,'2025_01_19_152548_create_slack_replies_table',58);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (154,'2025_02_15_131402_add_column_to_livechats_table',59);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (155,'2025_02_26_114646_add_column_to_livechats_table',60);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (156,'2025_03_08_102006_add_column_to_livechats_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (157,'2025_03_08_102020_add_column_to_missed_chats_table',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (158,'2025_03_10_152549_add_column_to_announcements_table',63);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (159,'2025_03_12_110323_add_column_to_objects_table',64);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (160,'2025_04_14_120715_add_column_to_livechats_table',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (161,'2025_04_14_185451_add_column_to_announcements_table',66);
