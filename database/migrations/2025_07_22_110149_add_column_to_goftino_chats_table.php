<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goftino_chats', function (Blueprint $table) {
            $table->dateTime('assignment_date')->nullable()->after('duration');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goftino_chats', function (Blueprint $table) {
            $table->dropColumn('assignment_date');
        });
    }
};
