<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goftino_chats', function (Blueprint $table) {
            $table->timestamp('first_message_datetime')->nullable()->after('duration');
            $table->timestamp('last_message_datetime')->nullable()->after('first_message_datetime');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goftino_chats', function (Blueprint $table) {
            $table->dropColumn('first_message_datetime');
            $table->dropColumn('last_message_datetime');
        });
    }
};
