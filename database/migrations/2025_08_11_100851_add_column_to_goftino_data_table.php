<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goftino_data', function (Blueprint $table) {
            $table->json('metadata')->nullable()->after('data');
            $table->string('phone')->nullable()->after('user_id');
            // $table->string('email')->nullable()->after('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goftino_data', function (Blueprint $table) {
            $table->dropColumn('metadata');
            $table->dropColumn('phone_number');
            // $table->dropColumn('email');
        });
    }
};
