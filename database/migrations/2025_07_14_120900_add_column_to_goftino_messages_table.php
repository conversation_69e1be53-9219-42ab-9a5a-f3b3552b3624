<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goftino_messages', function (Blueprint $table) {
            $table->boolean('new_chat')->default(true)->after('sender_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goftino_messages', function (Blueprint $table) {
            $table->dropColumn('new_chat');
        });
    }
};
