<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goftino_chats', function (Blueprint $table) {
            $table->id();

            $table->string('chat_id');

            $table->integer('rating')->nullable();

            $table->string('operator_id')->nullable();
            
            $table->integer('messages_count')->nullable();
            $table->integer('operators_count')->nullable();

            $table->integer('duration')->nullable();

            // $table->integer('queue')->nullable();
            // $table->integer('diff')->nullable();
            // $table->integer('frt')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goftino_chats');
    }
};
