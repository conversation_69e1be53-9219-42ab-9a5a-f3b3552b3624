<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goftino_chats', function (Blueprint $table) {
            $table->boolean('file')->nullable()->after('operators_count');
            
            // QC parameters
            $table->boolean('start_scenario')->nullable()->after('operators_count');
            $table->integer('frt')->nullable()->after('start_scenario');
            $table->boolean('called_by_name')->nullable()->after('frt');
            $table->boolean('chat_management')->nullable()->after('called_by_name');
            $table->boolean('survey')->nullable()->after('chat_management');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goftino_chats', function (Blueprint $table) {
            $table->dropColumn('file');
            
            $table->dropColumn('start_scenario');
            $table->dropColumn('frt');
            $table->dropColumn('called_by_name');
            $table->dropColumn('chat_management');
            $table->dropColumn('survey');
        });
    }
};
