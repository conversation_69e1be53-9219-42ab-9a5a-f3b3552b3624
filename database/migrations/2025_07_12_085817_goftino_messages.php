<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goftino_messages', function (Blueprint $table) {
            $table->id();

            $table->string('chat_id')->nullable();
            $table->text('content')->nullable();
            $table->string('type')->nullable();
            $table->timestamp('date')->nullable();
            $table->string('sender')->nullable();
            $table->string('sender_id')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goftino_messages');
    }
};
