<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaigns_second', function (Blueprint $table) {
            $table->id();
            // available customers' data
            $table->string('admin_id')->unique()->nullable();   // uuid
            // $table->string('name')->nullable();
            // $table->string('phone_number')->nullable();
            // $table->string('national_code')->nullable();

            // details 
            $table->dateTime('called_at')->nullable();
            $table->dateTime('call_later_at')->nullable();

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users');

            $table->string('call_status')->nullable();

            $table->text('description')->nullable();

            // custom

            $table->text('why_not_buy')->nullable();

            $table->text('why_register')->nullable();
            $table->string('how_familiar')->nullable();
            $table->boolean('will_buy')->nullable();
            $table->string('city')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaigns_second');
    }
};
