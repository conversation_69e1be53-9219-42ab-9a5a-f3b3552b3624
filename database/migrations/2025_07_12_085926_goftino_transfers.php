<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goftino_transfers', function (Blueprint $table) {
            $table->id();

            $table->string('chat_id')->nullable();
            $table->string('from_operator_id')->nullable();
            $table->string('to_operator_id')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goftino_transfers');
    }
};
