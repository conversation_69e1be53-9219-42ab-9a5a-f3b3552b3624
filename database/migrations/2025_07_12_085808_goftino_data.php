<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goftino_data', function (Blueprint $table) {
            $table->id();

            $table->string('chat_id')->unique();

            $table->json('data')->nullable();

            $table->string('user_id')->nullable();
            
            $table->string('platform')->nullable();

            $table->integer('messages_count')->nullable();
            $table->integer('operators_count')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goftino_data');
    }
};
