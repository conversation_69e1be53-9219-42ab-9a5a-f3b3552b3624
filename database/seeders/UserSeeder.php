<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('users')->insert([
            'name' => 'احسان',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => 2,
            'trace_chat' => 5,
            'trace_call' => 5,
        ]);

        DB::table('users')->insert([
            'name' => 'مریم محبوبی',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'weight' => 10,
            'eyebeam_id' => 2074,
        ]);
    }
}
