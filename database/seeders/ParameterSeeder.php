<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ParameterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('parameters')->insert([
            'name' => 'قطع  کلام',
            'incoming_type' => 'chat',
            'score' => 10,
        ]);
        DB::table('parameters')->insert([
            'name' => 'شروع مکامله',
            'incoming_type' => 'call',
            'score' => 5,
        ]);
    }
}
